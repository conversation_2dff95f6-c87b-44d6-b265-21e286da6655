<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use crmeb\services\payment\config\AlipayAuthConfigResolver;

/**
 * 支付宝授权数据同步管理命令
 * Class AlipayAuthDataSync
 * @package app\command
 */
class AlipayAuthDataSync extends Command
{
    protected function configure()
    {
        $this->setName('alipay:auth-sync')
            ->setDescription('支付宝授权数据同步管理')
            ->addArgument('action', Argument::REQUIRED, '操作类型: check|fix|clean')
            ->addOption('merchant', 'm', Option::VALUE_OPTIONAL, '指定商户ID')
            ->addOption('force', 'f', Option::VALUE_NONE, '强制执行')
            ->addOption('dry-run', 'd', Option::VALUE_NONE, '仅检查不执行修复');
    }

    protected function execute(Input $input, Output $output)
    {
        $action = $input->getArgument('action');
        $merchantId = $input->getOption('merchant');
        $force = $input->getOption('force');
        $dryRun = $input->getOption('dry-run');

        $resolver = new AlipayAuthConfigResolver();

        $output->writeln("<info>支付宝授权数据同步管理工具</info>");
        $output->writeln("<comment>操作类型: {$action}</comment>");

        if ($merchantId) {
            $output->writeln("<comment>指定商户ID: {$merchantId}</comment>");
        }

        if ($dryRun) {
            $output->writeln("<comment>模式: 仅检查，不执行修复</comment>");
        }

        $output->writeln("");

        try {
            switch ($action) {
                case 'check':
                    $this->checkDataConsistency($resolver, $output, $merchantId);
                    break;

                case 'fix':
                    if ($dryRun) {
                        $output->writeln("<warning>dry-run模式下不执行修复操作</warning>");
                        $this->checkDataConsistency($resolver, $output, $merchantId);
                    } else {
                        $this->fixDataConsistency($resolver, $output, $merchantId, $force);
                    }
                    break;

                case 'clean':
                    if ($dryRun) {
                        $output->writeln("<warning>dry-run模式下不执行清理操作</warning>");
                        $this->checkExpiredData($resolver, $output);
                    } else {
                        $this->cleanExpiredData($resolver, $output, $force);
                    }
                    break;

                default:
                    $output->writeln("<error>无效的操作类型: {$action}</error>");
                    $output->writeln("<info>可用操作: check, fix, clean</info>");
                    return 1;
            }

            $output->writeln("");
            $output->writeln("<info>操作完成</info>");
            return 0;

        } catch (\Exception $e) {
            $output->writeln("<error>操作失败: " . $e->getMessage() . "</error>");
            return 1;
        }
    }

    /**
     * 检查数据一致性
     */
    private function checkDataConsistency(AlipayAuthConfigResolver $resolver, Output $output, $merchantId = null)
    {
        $output->writeln("<info>检查数据一致性...</info>");

        if ($merchantId) {
            // 检查单个商户
            $summary = $resolver->getAuthStatusSummary($merchantId);
            $this->displayMerchantStatus($output, $summary);
        } else {
            // 批量检查
            $results = $resolver->batchValidateAndFixDataConsistency();
            $this->displayBatchResults($output, $results, 'check');
        }
    }

    /**
     * 修复数据一致性
     */
    private function fixDataConsistency(AlipayAuthConfigResolver $resolver, Output $output, $merchantId = null, $force = false)
    {
        if (!$force) {
            $output->writeln("<warning>数据修复操作可能影响业务，请使用 --force 参数确认执行</warning>");
            return;
        }

        $output->writeln("<info>修复数据一致性...</info>");

        if ($merchantId) {
            // 修复单个商户
            $authInfo = app()->make(\app\common\repositories\merchant\MerchantAlipayAuthRepository::class)->getAuthInfo($merchantId);
            if ($authInfo) {
                $merchantInfo = $resolver->getMerchantAlipayInfo($merchantId);
                $resolver->validateAndFixDataConsistency($merchantId, $authInfo, $merchantInfo);
                $output->writeln("<info>商户 {$merchantId} 数据修复完成</info>");
            } else {
                $output->writeln("<warning>商户 {$merchantId} 无授权数据</warning>");
            }
        } else {
            // 批量修复
            $results = $resolver->batchValidateAndFixDataConsistency();
            $this->displayBatchResults($output, $results, 'fix');
        }
    }

    /**
     * 检查过期数据
     */
    private function checkExpiredData(AlipayAuthConfigResolver $resolver, Output $output)
    {
        $output->writeln("<info>检查过期授权数据...</info>");

        $expiredAuths = \think\facade\Db::name('merchant_alipay_auth')
            ->where('expire_time', '<', time())
            ->where('status', 1)
            ->field('mer_id,expire_time,user_id')
            ->select();

        if (empty($expiredAuths)) {
            $output->writeln("<info>未发现过期的授权数据</info>");
            return;
        }

        $output->writeln("<warning>发现 " . count($expiredAuths) . " 条过期授权数据:</warning>");
        foreach ($expiredAuths as $auth) {
            $expireDate = date('Y-m-d H:i:s', $auth['expire_time']);
            $output->writeln("  商户ID: {$auth['mer_id']}, 过期时间: {$expireDate}, user_id: {$auth['user_id']}");
        }
    }

    /**
     * 清理过期数据
     */
    private function cleanExpiredData(AlipayAuthConfigResolver $resolver, Output $output, $force = false)
    {
        if (!$force) {
            $output->writeln("<warning>清理过期数据操作不可逆，请使用 --force 参数确认执行</warning>");
            return;
        }

        $output->writeln("<info>清理过期授权数据...</info>");

        $results = $resolver->cleanExpiredAuthData();
        $this->displayBatchResults($output, $results, 'clean');
    }

    /**
     * 显示单个商户状态
     */
    private function displayMerchantStatus(Output $output, array $summary)
    {
        $output->writeln("商户ID: {$summary['mer_id']}");
        $output->writeln("授权状态: " . ($summary['is_auth'] ? '<info>有效</info>' : '<error>无效</error>'));
        $output->writeln("授权消息: {$summary['auth_message']}");
        $output->writeln("授权应用ID: " . ($summary['auth_app_id'] ?: '<comment>空</comment>'));
        $output->writeln("二级商户ID: " . ($summary['sub_merchant_id'] ?: '<comment>空</comment>'));
        $output->writeln("配置可用: " . ($summary['config_available'] ? '<info>是</info>' : '<error>否</error>'));
        $output->writeln("有备用SMID: " . ($summary['has_fallback_smid'] ? '<info>是</info>' : '<comment>否</comment>'));

        if ($summary['auth_expires_at'] > 0) {
            $expireDate = date('Y-m-d H:i:s', $summary['auth_expires_at']);
            $isExpired = $summary['auth_expires_at'] < time();
            $status = $isExpired ? '<error>已过期</error>' : '<info>未过期</info>';
            $output->writeln("授权过期时间: {$expireDate} ({$status})");
        }
    }

    /**
     * 显示批量操作结果
     */
    private function displayBatchResults(Output $output, array $results, string $action)
    {
        $output->writeln("检查数量: {$results['checked_count']}");
        
        if ($action === 'fix') {
            $output->writeln("修复数量: {$results['fixed_count']}");
        } elseif ($action === 'clean') {
            $output->writeln("清理数量: {$results['cleaned_count']}");
        }
        
        if ($results['error_count'] > 0) {
            $output->writeln("<error>错误数量: {$results['error_count']}</error>");
        }

        if (!empty($results['details'])) {
            $output->writeln("");
            $output->writeln("<comment>详细结果:</comment>");
            
            foreach ($results['details'] as $detail) {
                $status = $detail['status'] === 'success' ? '<info>成功</info>' : '<error>失败</error>';
                $output->write("  商户ID: {$detail['mer_id']} - {$status}");
                
                if (isset($detail['action'])) {
                    $output->write(" (操作: {$detail['action']})");
                }
                
                if (isset($detail['error'])) {
                    $output->write(" - 错误: {$detail['error']}");
                }
                
                if (isset($detail['reason'])) {
                    $output->write(" - 原因: {$detail['reason']}");
                }
                
                $output->writeln("");
            }
        }
    }
}