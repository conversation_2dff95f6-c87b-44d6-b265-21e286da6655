<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Cache;

/**
 * 支付架构升级功能测试命令
 */
class PaymentArchitectureTest extends Command
{
    protected function configure()
    {
        $this->setName('payment:test')
            ->setDescription('支付架构升级功能测试');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('=== 支付架构升级功能测试 ===');
        
        try {
            // 1. 测试数据库表结构
            $this->testDatabaseTables($output);
            
            // 2. 测试支付方式状态管理
            $this->testPaymentMethodStatus($output);
            
            // 3. 测试配置审计日志
            $this->testConfigAuditLog($output);
            
            // 4. 测试支付性能指标
            $this->testPaymentMetrics($output);
            
            // 5. 测试智能路由日志
            $this->testRouteLog($output);
            
            // 6. 测试告警规则
            $this->testAlertRules($output);
            
            // 7. 测试缓存功能
            $this->testCacheFeatures($output);
            
            $output->writeln('<info>=== 测试完成 ===</info>');
            
        } catch (\Exception $e) {
            $output->writeln('<error>测试执行失败: ' . $e->getMessage() . '</error>');
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 测试数据库表结构
     */
    private function testDatabaseTables(Output $output)
    {
        $output->writeln("\n1. 测试数据库表结构...");
        
        $tables = [
            'eb_payment_config_audit_log',
            'eb_payment_config_gray_release',
            'eb_payment_metrics',
            'eb_payment_route_log',
            'eb_payment_method_status',
            'eb_payment_alert_rules',
            'eb_payment_alert_records'
        ];
        
        foreach ($tables as $table) {
            try {
                $result = Db::query("SHOW TABLES LIKE '{$table}'");
                if (!empty($result)) {
                    $output->writeln("   <info>✅ 表 {$table} 存在</info>");
                } else {
                    $output->writeln("   <error>❌ 表 {$table} 不存在</error>");
                }
            } catch (\Exception $e) {
                $output->writeln("   <error>❌ 检查表 {$table} 失败: " . $e->getMessage() . "</error>");
            }
        }
    }
    
    /**
     * 测试支付方式状态管理
     */
    private function testPaymentMethodStatus(Output $output)
    {
        $output->writeln("\n2. 测试支付方式状态管理...");
        
        try {
            // 查询支付方式状态
            $paymentMethods = Db::table('eb_payment_method_status')
                ->order('priority DESC')
                ->select();
            
            $output->writeln("   支付方式列表:");
            foreach ($paymentMethods as $method) {
                $output->writeln("   - {$method['payment_type']}: {$method['status']} (优先级: {$method['priority']}, 费率: {$method['fee_rate']})");
            }
            
            // 测试更新支付方式状态
            $updateResult = Db::table('eb_payment_method_status')
                ->where('payment_type', 'wechat')
                ->update(['updated_at' => time()]);
            
            if ($updateResult !== false) {
                $output->writeln("   <info>✅ 支付方式状态更新成功</info>");
            } else {
                $output->writeln("   <error>❌ 支付方式状态更新失败</error>");
            }
            
        } catch (\Exception $e) {
            $output->writeln("   <error>❌ 测试支付方式状态失败: " . $e->getMessage() . "</error>");
        }
    }
    
    /**
     * 测试配置审计日志
     */
    private function testConfigAuditLog(Output $output)
    {
        $output->writeln("\n3. 测试配置审计日志...");
        
        try {
            // 插入测试审计日志
            $auditData = [
                'config_type' => 'merchant',
                'target_id' => 1,
                'old_config' => json_encode(['payment_type' => 'wechat', 'status' => 'active']),
                'new_config' => json_encode(['payment_type' => 'wechat', 'status' => 'disabled']),
                'change_type' => 'update',
                'operator_id' => 1,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'ThinkPHP Test Command',
                'created_at' => time()
            ];
            
            $auditId = Db::table('eb_payment_config_audit_log')->insertGetId($auditData);
            
            if ($auditId) {
                $output->writeln("   <info>✅ 配置审计日志插入成功 (ID: {$auditId})</info>");
                
                // 查询审计日志
                $auditLog = Db::table('eb_payment_config_audit_log')
                    ->where('id', $auditId)
                    ->find();
                
                if ($auditLog) {
                    $output->writeln("   <info>✅ 配置审计日志查询成功</info>");
                    $output->writeln("   - 配置类型: {$auditLog['config_type']}");
                    $output->writeln("   - 变更类型: {$auditLog['change_type']}");
                    $output->writeln("   - 操作时间: " . date('Y-m-d H:i:s', $auditLog['created_at']));
                } else {
                    $output->writeln("   <error>❌ 配置审计日志查询失败</error>");
                }
                
                // 清理测试数据
                Db::table('eb_payment_config_audit_log')->where('id', $auditId)->delete();
                $output->writeln("   <info>✅ 测试数据已清理</info>");
                
            } else {
                $output->writeln("   <error>❌ 配置审计日志插入失败</error>");
            }
            
        } catch (\Exception $e) {
            $output->writeln("   <error>❌ 测试配置审计日志失败: " . $e->getMessage() . "</error>");
        }
    }
    
    /**
     * 测试支付性能指标
     */
    private function testPaymentMetrics(Output $output)
    {
        $output->writeln("\n4. 测试支付性能指标...");
        
        try {
            // 插入测试性能指标
            $metricsData = [
                'payment_type' => 'wechat',
                'order_no' => 'TEST_' . time(),
                'amount' => 100.00,
                'success' => 1,
                'response_time' => 1500,
                'error_code' => '',
                'error_message' => '',
                'gateway' => 'wechat',
                'created_at' => time()
            ];
            
            $metricsId = Db::table('eb_payment_metrics')->insertGetId($metricsData);
            
            if ($metricsId) {
                $output->writeln("   <info>✅ 支付性能指标插入成功 (ID: {$metricsId})</info>");
                
                // 查询性能统计
                $stats = Db::table('eb_payment_metrics')
                    ->where('payment_type', 'wechat')
                    ->where('created_at', '>=', time() - 3600)
                    ->field([
                        'COUNT(*) as total_count',
                        'SUM(success) as success_count',
                        'AVG(response_time) as avg_response_time',
                        'SUM(amount) as total_amount'
                    ])
                    ->find();
                
                if ($stats) {
                    $successRate = $stats['total_count'] > 0 ? 
                        round(($stats['success_count'] / $stats['total_count']) * 100, 2) : 0;
                    
                    $output->writeln("   <info>✅ 性能统计查询成功</info>");
                    $output->writeln("   - 总交易数: {$stats['total_count']}");
                    $output->writeln("   - 成功率: {$successRate}%");
                    $output->writeln("   - 平均响应时间: " . round($stats['avg_response_time'], 2) . "ms");
                    $output->writeln("   - 总金额: {$stats['total_amount']}");
                }
                
                // 清理测试数据
                Db::table('eb_payment_metrics')->where('id', $metricsId)->delete();
                $output->writeln("   <info>✅ 测试数据已清理</info>");
                
            } else {
                $output->writeln("   <error>❌ 支付性能指标插入失败</error>");
            }
            
        } catch (\Exception $e) {
            $output->writeln("   <error>❌ 测试支付性能指标失败: " . $e->getMessage() . "</error>");
        }
    }
    
    /**
     * 测试智能路由日志
     */
    private function testRouteLog(Output $output)
    {
        $output->writeln("\n5. 测试智能路由日志...");
        
        try {
            // 插入测试路由日志
            $routeData = [
                'order_no' => 'ROUTE_TEST_' . time(),
                'amount' => 200.00,
                'available_payments' => json_encode(['wechat', 'alipay', 'routine']),
                'selected_payment' => 'wechat',
                'strategy' => 'balanced',
                'degraded' => 0,
                'route_reason' => '基于成功率和费率的智能选择',
                'created_at' => time()
            ];
            
            $routeId = Db::table('eb_payment_route_log')->insertGetId($routeData);
            
            if ($routeId) {
                $output->writeln("   <info>✅ 智能路由日志插入成功 (ID: {$routeId})</info>");
                
                // 查询路由统计
                $routeStats = Db::table('eb_payment_route_log')
                    ->where('created_at', '>=', time() - 3600)
                    ->field([
                        'selected_payment',
                        'COUNT(*) as route_count',
                        'SUM(degraded) as degraded_count'
                    ])
                    ->group('selected_payment')
                    ->select();
                
                if ($routeStats) {
                    $output->writeln("   <info>✅ 路由统计查询成功</info>");
                    foreach ($routeStats as $stat) {
                        $degradedRate = $stat['route_count'] > 0 ? 
                            round(($stat['degraded_count'] / $stat['route_count']) * 100, 2) : 0;
                        $output->writeln("   - {$stat['selected_payment']}: {$stat['route_count']}次路由, 降级率: {$degradedRate}%");
                    }
                }
                
                // 清理测试数据
                Db::table('eb_payment_route_log')->where('id', $routeId)->delete();
                $output->writeln("   <info>✅ 测试数据已清理</info>");
                
            } else {
                $output->writeln("   <error>❌ 智能路由日志插入失败</error>");
            }
            
        } catch (\Exception $e) {
            $output->writeln("   <error>❌ 测试智能路由日志失败: " . $e->getMessage() . "</error>");
        }
    }
    
    /**
     * 测试告警规则
     */
    private function testAlertRules(Output $output)
    {
        $output->writeln("\n6. 测试告警规则...");
        
        try {
            // 查询告警规则
            $alertRules = Db::table('eb_payment_alert_rules')
                ->where('is_enabled', 1)
                ->select();
            
            $output->writeln("   告警规则列表:");
            foreach ($alertRules as $rule) {
                $paymentType = $rule['payment_type'] ?: '全部';
                $output->writeln("   - {$rule['rule_name']} ({$paymentType}): {$rule['metric_type']} {$rule['operator']} {$rule['threshold_value']} ({$rule['alert_level']})");
            }
            
            // 插入测试告警记录
            $alertData = [
                'rule_id' => $alertRules[0]['id'] ?? 1,
                'payment_type' => 'wechat',
                'metric_type' => 'success_rate',
                'current_value' => 75.50,
                'threshold_value' => 80.00,
                'alert_level' => 'warning',
                'alert_message' => '微信支付成功率低于阈值',
                'alert_channels' => 'email,sms',
                'is_sent' => 0,
                'created_at' => time()
            ];
            
            $alertId = Db::table('eb_payment_alert_records')->insertGetId($alertData);
            
            if ($alertId) {
                $output->writeln("   <info>✅ 告警记录插入成功 (ID: {$alertId})</info>");
                
                // 清理测试数据
                Db::table('eb_payment_alert_records')->where('id', $alertId)->delete();
                $output->writeln("   <info>✅ 测试数据已清理</info>");
                
            } else {
                $output->writeln("   <error>❌ 告警记录插入失败</error>");
            }
            
        } catch (\Exception $e) {
            $output->writeln("   <error>❌ 测试告警规则失败: " . $e->getMessage() . "</error>");
        }
    }
    
    /**
     * 测试缓存功能
     */
    private function testCacheFeatures(Output $output)
    {
        $output->writeln("\n7. 测试缓存功能...");
        
        try {
            // 测试配置缓存
            $testConfig = [
                'payment_type' => 'wechat',
                'app_id' => 'test_app_id',
                'mch_id' => 'test_mch_id',
                'key' => 'test_key'
            ];
            
            // 设置缓存
            $cacheKey = 'partner_payment_config_test_1_wechat';
            Cache::set($cacheKey, $testConfig, 1800);
            
            // 获取缓存
            $cachedConfig = Cache::get($cacheKey);
            
            if ($cachedConfig && $cachedConfig['app_id'] === 'test_app_id') {
                $output->writeln("   <info>✅ 配置缓存功能正常</info>");
                $output->writeln("   - 缓存键: {$cacheKey}");
                $output->writeln("   - 配置类型: {$cachedConfig['payment_type']}");
                $output->writeln("   - APP ID: {$cachedConfig['app_id']}");
            } else {
                $output->writeln("   <error>❌ 配置缓存功能异常</error>");
            }
            
            // 清理测试缓存
            Cache::delete($cacheKey);
            $output->writeln("   <info>✅ 测试缓存已清理</info>");
            
            // 测试性能指标缓存
            $metricsKey = 'smart_payment_router_metrics_wechat';
            $metricsData = [
                'success_rate' => 95.5,
                'avg_response_time' => 1200,
                'total_count' => 100
            ];
            
            Cache::set($metricsKey, $metricsData, 300);
            $cachedMetrics = Cache::get($metricsKey);
            
            if ($cachedMetrics && $cachedMetrics['success_rate'] === 95.5) {
                $output->writeln("   <info>✅ 性能指标缓存功能正常</info>");
                $output->writeln("   - 成功率: {$cachedMetrics['success_rate']}%");
                $output->writeln("   - 平均响应时间: {$cachedMetrics['avg_response_time']}ms");
            } else {
                $output->writeln("   <error>❌ 性能指标缓存功能异常</error>");
            }
            
            Cache::delete($metricsKey);
            $output->writeln("   <info>✅ 性能指标缓存已清理</info>");
            
        } catch (\Exception $e) {
            $output->writeln("   <error>❌ 测试缓存功能失败: " . $e->getMessage() . "</error>");
        }
    }
} 