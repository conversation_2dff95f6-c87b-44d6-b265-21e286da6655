<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\repositories\api\ApiPartnerRepository;
use app\common\repositories\api\ApiOrderRepository;
use app\common\repositories\BaseRepository;

class TestApi extends Command
{
    protected function configure()
    {
        $this->setName("test:api")
            ->setDescription("测试API数据结构");
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始测试API数据结构...");
        
        // 测试合作伙伴列表
        $this->testPartnerList($output);
        
        // 测试订单列表
        $this->testOrderList($output);
        
        $output->writeln("测试完成");
    }
    
    protected function testPartnerList(Output $output)
    {
        $output->writeln("测试合作伙伴列表...");
        
        try {
            $repository = app()->make(ApiPartnerRepository::class);
            $result = $repository->getList([], 1, 10);
            
            $output->writeln("合作伙伴列表数据结构:");
            $output->writeln(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            if (!empty($result["list"])) {
                $output->writeln("第一条记录字段:");
                $output->writeln(json_encode(array_keys($result["list"][0]), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                
                // 测试编辑功能
                $firstId = $result["list"][0]['id'];
                $output->writeln("测试编辑ID: {$firstId}");
                
                $updateData = [
                    'partner_name' => '测试更新_' . time(),
                    'phone' => '***********',
                    'email' => '<EMAIL>',
                    'address' => '测试地址',
                    'bank_name' => '测试银行',
                    'bank_account' => '*************',
                    'bank_holder' => '测试用户',
                    'commission_rate' => 5,
                    'notify_url' => 'http://example.com/notify',
                    'return_url' => 'http://example.com/return',
                    'status' => 1,
                    'remark' => '测试备注'
                ];
                
                $updateResult = $repository->update($firstId, $updateData);
                $output->writeln('更新结果:');
                $output->writeln(json_encode($updateResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                
                // 验证更新是否成功
                $afterUpdate = $repository->find($firstId);
                $output->writeln('更新后数据:');
                $output->writeln(json_encode($afterUpdate, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            }
        } catch (\Exception $e) {
            $output->writeln("测试合作伙伴列表出错: " . $e->getMessage());
        }
    }
    
    protected function testOrderList(Output $output)
    {
        $output->writeln("测试订单列表...");
        
        try {
            $repository = app()->make(ApiOrderRepository::class);
            $result = $repository->getList([], 1, 10);
            
            $output->writeln("订单列表数据结构:");
            $output->writeln(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            if (!empty($result["list"])) {
                $output->writeln("第一条记录字段:");
                $output->writeln(json_encode(array_keys($result["list"][0]), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                
                // 测试订单详情
                $firstId = $result["list"][0]['id'];
                $output->writeln("测试订单详情ID: {$firstId}");
                
                $detail = $repository->find($firstId);
                $output->writeln('订单详情:');
                $output->writeln(json_encode($detail, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            }
        } catch (\Exception $e) {
            $output->writeln("测试订单列表出错: " . $e->getMessage());
        }
    }
}
