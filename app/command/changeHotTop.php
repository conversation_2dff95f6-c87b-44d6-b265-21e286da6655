<?php
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

declare (strict_types=1);

namespace app\command;

use crmeb\jobs\SyncProductTopJob;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Queue;

class changeHotTop extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('change:hotTop')
            ->setDescription('清楚缓存：php think change:hotTop');
    }

    /**
     * 队列执行
     * @param Input $input
     * @param Output $output
     * @return int|void|null
     * <AUTHOR>
     * @day 4/24/22
     */
    protected function execute(Input $input, Output $output)
    {
        Queue::push(SyncProductTopJob::class,[]);
        $output->writeln('开始执行队列');
    }

}
