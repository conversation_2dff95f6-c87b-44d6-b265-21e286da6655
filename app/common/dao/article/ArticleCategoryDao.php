<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------


namespace app\common\dao\article;


use app\common\dao\BaseDao;
use app\common\model\article\ArticleCategory;
use app\common\model\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

/**
 * Class ArticleCategoryDao
 * @package app\common\dao\article
 * <AUTHOR>
 * @day 2020-04-20
 */
class ArticleCategoryDao extends BaseDao
{

    /**
     * @return BaseModel
     * <AUTHOR>
     * @day 2020-03-30
     */
    protected function getModel(): string
    {
        return ArticleCategory::class;
    }

    /**
     * 获取文章分类集合
     * @param int $mer_id
     * @return array
     * <AUTHOR>
     * @day 2020-04-20
     */
    public function getAllOptions($mer_id = 0)
    {
        return ArticleCategory::getDB()->where('mer_id', $mer_id)->order('sort DESC')->column('pid,title', $this->getPk());
    }

    /**
     * 获取文章分类
     * @param int $mer_id
     * @return Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @day 2020-04-20
     */
    public function getAll($mer_id = 0,$status = null)
    {
        return ArticleCategory::getDB()->where('mer_id', $mer_id)->when($status,function($query)use($status){
            $query->where('status',$status);
        })->order('sort DESC')->select();
    }

    /**
     * 查询文章分类
     * @param array $where
     * @return \think\db\BaseQuery
     * <AUTHOR>
     * @day 2020/9/18
     */
    public function search(array $where)
    {
        return ArticleCategory::getDB()->when(isset($where['status']) && $where['status'] !== '', function ($query) use ($where) {
            $query->where('status', $where['status']);
        })->when(isset($where['pid']) && $where['pid'] !== '', function ($query) use ($where) {
            $query->where('pid', $where['pid']);
        })->order('sort DESC, article_category_id DESC');
    }

    /**
     * 查询指定字段的数据是否存在
     * @param int $merId
     * @param $field
     * @param $value
     * @param null $except
     * @return bool
     * <AUTHOR>
     * @day 2020-04-20
     */
    public function merFieldExists(int $merId, $field, $value, $except = null)
    {
        return ($this->getModel())::getDB()->when($except, function ($query, $except) use ($field) {
                $query->where($field, '<>', $except);
            })->where('mer_id', $merId)->where($field, $value)->count() > 0;
    }

    /**
     * 查询主键是否存在
     * @param int $merId
     * @param int $id
     * @param null $except
     * @return bool
     * <AUTHOR>
     * @day 2020-04-20
     */
    public function merExists(int $merId, int $id, $except = null)
    {
        return $this->merFieldExists($merId, $this->getPk(), $id, $except);
    }

    /**
     * 文章分类详情
     * @param int $id
     * @param int $merId
     * @return array|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @day 2020-04-15
     */
    public function get( $id, $merId = 0)
    {
        return ($this->getModel())::getDB()->where('mer_id', 0)->find($id);
    }

    /**
     * 删除文章分类
     * @param int $id
     * @param int $merId
     * @return int
     * @throws DbException
     * <AUTHOR>
     * @day 2020-04-20
     */
    public function delete(int $id, $merId = 0)
    {
        return ($this->getModel())::getDB()->where($this->getPk(), $id)->where('mer_id', $merId)->delete();
    }
}
