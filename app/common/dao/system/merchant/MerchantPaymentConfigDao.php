<?php

namespace app\common\dao\system\merchant;

use app\common\dao\BaseDao;
use app\common\model\system\merchant\MerchantPaymentConfig;

/**
 * 商户支付方式配置数据访问层
 * Class MerchantPaymentConfigDao
 * @package app\common\dao\system\merchant
 */
class MerchantPaymentConfigDao extends BaseDao
{
    /**
     * 获取模型
     * @return string
     */
    protected function getModel(): string
    {
        return MerchantPaymentConfig::class;
    }
    
    /**
     * 获取模型实例（提供给仓库使用）
     * @return \think\Model
     */
    public function getModelObj()
    {
        return ($this->getModel())::getInstance();
    }

    /**
     * 获取商户所有支付方式列表
     * @param int $merId
     * @param array $where
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPaymentList(int $merId, array $where = [])
    {
        return ($this->getModel())::getDB()
            ->where('mer_id', $merId)
            ->where($where)
            ->order('sort', 'ASC')
            ->order('id', 'ASC')
            ->select()
            ->toArray();
    }
    
    /**
     * 获取所有商户的支付方式列表
     * @param array $where
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAllPaymentList(array $where = [])
    {
        return ($this->getModel())::getDB()
            ->where($where)
            ->order('mer_id', 'ASC')
            ->order('sort', 'ASC')
            ->order('id', 'ASC')
            ->select()
            ->toArray();
    }

    /**
     * 获取开启的支付方式
     * @param int $merId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getEnabledPayments(int $merId)
    {
        return ($this->getModel())::getDB()
            ->where('mer_id', $merId)
            ->where('status', 1)
            ->order('sort', 'ASC')
            ->order('id', 'ASC')
            ->select()
            ->toArray();
    }

    /**
     * 获取指定支付方式配置
     * @param int $merId
     * @param string $paymentType
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPaymentByType(int $merId, string $paymentType)
    {
        return ($this->getModel())::getDB()
            ->where('mer_id', $merId)
            ->where('payment_type', $paymentType)
            ->find();
    }

    /**
     * 获取默认支付方式
     * @param int $merId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getDefaultPayment(int $merId)
    {
        return ($this->getModel())::getDB()
            ->where('mer_id', $merId)
            ->where('status', 1)
            ->where('is_default', 1)
            ->find();
    }

    /**
     * 取消默认支付方式
     * @param int $merId
     * @return mixed
     */
    public function cancelDefault(int $merId)
    {
        return ($this->getModel())::getDB()
            ->where('mer_id', $merId)
            ->update(['is_default' => 0]);
    }
    
    /**
     * 保存支付方式配置
     * @param array $data
     * @return int
     */
    public function save(array $data)
    {
        $model = $this->create($data);
        return $model->id;
    }

    /**
     * 获取商户所有支付方式列表（分页）
     * @param int $merId
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPaymentListWithPage(int $merId, array $where = [], int $page = 1, int $limit = 10)
    {
        $query = ($this->getModel())::getDB()
            ->where('mer_id', $merId)
            ->where($where)
            ->order('sort', 'ASC')
            ->order('id', 'ASC');
        
        $count = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();
        
        return ['count' => $count, 'list' => $list];
    }

    /**
     * 获取所有商户的支付方式列表（分页）
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAllPaymentListWithPage(array $where = [], int $page = 1, int $limit = 10)
    {
        $query = ($this->getModel())::getDB()
            ->where($where)
            ->order('mer_id', 'ASC')
            ->order('sort', 'ASC')
            ->order('id', 'ASC');
        
        $count = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();
        
        return ['count' => $count, 'list' => $list];
    }

    /**
     * 获取所有商户的支付方式列表（分页，关联商户信息）
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAllPaymentListWithPageAndMerchant(array $where = [], int $page = 1, int $limit = 10)
    {
        $alias = 'config';
        
        $query = ($this->getModel())::alias($alias)
            ->join('eb_merchant merchant', 'merchant.mer_id = ' . $alias . '.mer_id', 'LEFT')
            ->field([
                $alias . '.*',
                'merchant.mer_name',
                'merchant.mer_phone',
                'merchant.real_name'
            ])
            ->where($where)
            ->order($alias . '.mer_id', 'ASC')
            ->order($alias . '.sort', 'ASC')
            ->order($alias . '.id', 'ASC');
        
        $count = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();
        
        return ['count' => $count, 'list' => $list];
    }
}
