<?php

namespace app\common\dao\system\merchant;

use app\common\dao\BaseDao;
use app\common\model\system\merchant\MerchantPaymentParams;

/**
 * 商户支付方式参数数据访问层
 * Class MerchantPaymentParamsDao
 * @package app\common\dao\system\merchant
 */
class MerchantPaymentParamsDao extends BaseDao
{
    /**
     * 获取模型
     * @return string
     */
    protected function getModel(): string
    {
        return MerchantPaymentParams::class;
    }

    /**
     * 获取支付方式参数
     * @param int $paymentId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getParamsByPaymentId(int $paymentId)
    {
        return ($this->getModel())::getDB()
            ->where('payment_id', $paymentId)
            ->select()
            ->toArray();
    }

    /**
     * 删除支付方式参数
     * @param int $paymentId
     * @return bool
     */
    public function deleteByPaymentId(int $paymentId)
    {
        return ($this->getModel())::getDB()
            ->where('payment_id', $paymentId)
            ->delete();
    }

    /**
     * 批量保存参数
     * @param array $data
     * @return int
     */
    public function saveAll(array $data)
    {
        return ($this->getModel())::insertAll($data);
    }
}
