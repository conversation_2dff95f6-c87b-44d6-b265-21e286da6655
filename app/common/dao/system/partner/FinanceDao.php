<?php

namespace app\common\dao\system\partner;

use app\common\dao\BaseDao;
use app\common\model\user\UserBill;

class FinanceDao extends BaseDao
{
    /**
     * FinanceDao constructor.
     * @param UserBill $model
     */
    public function __construct(UserBill $model)
    {
        $this->model = $model;
    }

    /**
     * 获取模型
     * @return string
     */
    protected function getModel(): string
    {
        return UserBill::class;
    }

    /**
     * 搜索
     * @param array $where
     * @return mixed
     */
    public function search(array $where = [])
    {
        // 合作伙伴账单类型是partner，与普通用户账单区分开
        $query = $this->model->where('category', 'partner');

        // 合作伙伴筛选
        if (!empty($where['partner_id'])) {
            $query->where('link_id', $where['partner_id']);
        }

        // 类型筛选
        if (isset($where['type']) && $where['type'] !== '') {
            if ($where['type'] == 1) {
                $query->where('pm', 1); // 收入
            } elseif ($where['type'] == 2) {
                $query->where('pm', 0); // 支出
            }
        }

        // 时间范围
        if (!empty($where['date']) && is_array($where['date'])) {
            $query->whereBetweenTime('create_time', $where['date'][0], $where['date'][1]);
        }

        return $query->order('bill_id', 'desc');
    }
}
