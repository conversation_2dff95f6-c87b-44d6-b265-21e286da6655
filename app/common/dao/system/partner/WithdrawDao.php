<?php

namespace app\common\dao\system\partner;

use app\common\dao\BaseDao;
use app\common\model\user\UserExtract;

class WithdrawDao extends BaseDao
{
    /**
     * WithdrawDao constructor.
     * @param UserExtract $model
     */
    public function __construct(UserExtract $model)
    {
        $this->model = $model;
    }

    /**
     * 获取模型
     * @return string
     */
    protected function getModel(): string
    {
        return UserExtract::class;
    }

    /**
     * 搜索
     * @param array $where
     * @return mixed
     */
    public function search(array $where = [])
    {
        // 合作伙伴提现类型是partner，与普通用户提现区分开
        $query = $this->model->where('extract_type', 'partner');

        // 合作伙伴筛选
        if (!empty($where['partner_id'])) {
            $query->where('uid', $where['partner_id']);
        }

        // 状态筛选
        if (isset($where['status']) && $where['status'] !== '') {
            $query->where('status', $where['status']);
        }

        // 时间范围
        if (!empty($where['date']) && is_array($where['date'])) {
            $query->whereBetweenTime('create_time', $where['date'][0], $where['date'][1]);
        }

        return $query->order('extract_id', 'desc');
    }
}
