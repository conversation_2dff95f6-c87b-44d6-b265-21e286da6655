<?php
declare (strict_types = 1);

namespace app\common\middleware;

use app\common\model\api\ApiPartner;
use app\Request;
use crmeb\services\api\ApiEncryptService;
use think\facade\Cache;
use think\facade\Log;
use think\Response;

class ApiAuthenticate
{
    /**
     * 自动续期的阈值（秒）
     * 当token距离过期时间小于此值时，将自动续期
     */
    const AUTO_RENEW_THRESHOLD = 7 * 86400; // 7天
    
    /**
     * 自动续期的时长（秒）
     * 自动续期时，将过期时间延长此值
     */
    const AUTO_RENEW_DURATION = 30 * 86400; // 30天
    
    /**
     * 加密服务实例
     * @var ApiEncryptService
     */
    protected $encryptService;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->encryptService = app()->make(ApiEncryptService::class);
    }
    
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next)
    {
        // 1. Bearer Token认证
        $token = $this->getBearerToken($request);
        if (!$token) {
            return $this->errorResponse(401, 'API认证失败：缺少认证令牌');
        }
        
        // 2. 防重放攻击验证
        if (!$this->checkReplayAttack($request)) {
            return $this->errorResponse(400, '请求已过期或时间戳无效');
        }
        
        // 3. 查询合作伙伴
        $partner = $this->validatePartner($token);
        if (!$partner) {
            return $this->errorResponse(401, 'API认证失败：无效的认证令牌');
        }
        
        // 4. 检查过期
        if ($this->isTokenExpired($partner)) {
            return $this->errorResponse(401, 'API认证失败：合作伙伴授权已过期');
        }
        
        // 5. IP白名单验证
        if (!$this->checkIpWhitelist($request, $partner)) {
            return $this->errorResponse(403, '请求IP不在白名单中');
        }
        
        // 6. 请求限流
        if (!$this->checkRateLimit($partner)) {
            return $this->errorResponse(429, '请求频率超过限制');
        }
        
        // 7. 绑定合作伙伴信息到请求
        $request->apiPartner = $partner;
        
        // 8. Token自动续期
        $this->autoRenewToken($partner);
        
        // 9. 解密请求数据
        $this->decryptRequestData($request, $partner);
        
        // 10. 处理请求
        $response = $next($request);
        
        // 11. 加密响应数据
        return $this->encryptResponseData($response, $partner);
    }
    
    /**
     * 获取Bearer Token
     */
    private function getBearerToken(Request $request): ?string
    {
        $authorization = $request->header('Authorization');
        if (empty($authorization)) {
            return null;
        }
        
        if (strpos($authorization, 'Bearer ') === 0) {
            return substr($authorization, 7);
        }
        
        return null;
    }
    
    /**
     * 验证合作伙伴
     */
    private function validatePartner(string $token): ?ApiPartner
    {
        return ApiPartner::where('api_token', $token)
            ->where('status', 1)
            ->find();
    }
    
    /**
     * 检查Token是否过期
     */
    private function isTokenExpired(ApiPartner $partner): bool
    {
        return $partner->expire_time && $partner->expire_time < time();
    }
    
    /**
     * 检查IP白名单
     */
    private function checkIpWhitelist(Request $request, ApiPartner $partner): bool
    {
        if (empty($partner->ip_whitelist)) {
            return true; // 没有设置白名单，允许所有IP
        }
        
        $ipWhitelist = is_array($partner->ip_whitelist) 
            ? $partner->ip_whitelist 
            : explode(',', $partner->ip_whitelist);
            
        $clientIp = $request->ip();
        
        if (!in_array($clientIp, $ipWhitelist)) {
            Log::error('API请求IP白名单验证失败', [
                'client_ip' => $clientIp,
                'partner_code' => $partner->partner_code,
                'whitelist' => $ipWhitelist
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查请求限流
     */
    private function checkRateLimit(ApiPartner $partner): bool
    {
        $requestLimit = config('api.request_limit', 60); // 每分钟60次
        $cacheKey = 'api_request_limit:' . $partner->partner_code . ':' . date('YmdHi');
        $currentCount = Cache::get($cacheKey, 0);
        
        if ($currentCount >= $requestLimit) {
            return false;
        }
        
        Cache::set($cacheKey, $currentCount + 1, 60);
        return true;
    }
    
    /**
     * 解密请求数据
     * @param Request $request
     * @param ApiPartner $partner
     */
    protected function decryptRequestData(Request $request, ApiPartner $partner)
    {
        try {
            $params = $request->param();
            
            // 检查是否有加密数据（统一使用encrypt_data字段）
            if (!empty($params['encrypt_data'])) {
                // 解密数据
                $decryptedData = $this->encryptService->decrypt($params['encrypt_data'], $partner->api_secret);
                if ($decryptedData === null) {
                    Log::error('API请求数据解密失败', [
                        'partner_code' => $partner->partner_code,
                        'encrypt_data' => $params['encrypt_data']
                    ]);
                    return;
                }
                
                // 解析JSON
                $decryptedParams = json_decode($decryptedData, true);
                if (!$decryptedParams || !is_array($decryptedParams)) {
                    Log::error('API请求解密数据格式错误', [
                        'partner_code' => $partner->partner_code,
                        'decrypted_data' => $decryptedData
                    ]);
                    return;
                }
                
                // 将解密后的参数合并到请求中，移除加密字段
                $newParams = array_merge($params, $decryptedParams);
                unset($newParams['encrypt_data']);
                $request->withInput($newParams);
                
                Log::debug('API请求数据解密成功', [
                    'partner_code' => $partner->partner_code,
                    'decrypted_params' => array_keys($decryptedParams)
                ]);
            }
        } catch (\Exception $e) {
            Log::error('API请求数据解密异常: ' . $e->getMessage(), [
                'partner_code' => $partner->partner_code,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 加密响应数据
     * @param Response $response
     * @param ApiPartner $partner
     * @return Response
     */
    protected function encryptResponseData(Response $response, ApiPartner $partner)
    {
        try {
            // 获取响应内容
            $content = $response->getContent();
            $contentArray = json_decode($content, true);
            
            // 不是JSON格式的响应不处理
            if (!$contentArray || !is_array($contentArray)) {
                return $response;
            }
            
            // 获取响应配置
            $encryptResponse = config('api.encrypt_response', true);
            
            // 如果配置了全局加密响应，则进行加密
            if ($encryptResponse && isset($contentArray['data']) && is_array($contentArray['data'])) {
                // 获取需要加密的数据
                $dataToEncrypt = $contentArray['data'];
                
                // 加密数据（统一使用api_secret密钥）
                $encryptedData = $this->encryptService->encrypt(
                    json_encode($dataToEncrypt, JSON_UNESCAPED_UNICODE), 
                    $partner->api_secret
                );
                
                // 替换响应数据（统一使用encrypt_data字段）
                $contentArray['data'] = [
                    'encrypt_data' => $encryptedData,
                    'timestamp' => time()
                ];
                
                // 设置新的响应内容
                $response->content(json_encode($contentArray, JSON_UNESCAPED_UNICODE));
                
                Log::debug('API响应数据加密成功', [
                    'partner_code' => $partner->partner_code
                ]);
            }
        } catch (\Exception $e) {
            Log::error('API响应数据加密异常: ' . $e->getMessage(), [
                'partner_code' => $partner->partner_code,
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        return $response;
    }
    
    /**
     * Token自动续期
     * @param ApiPartner $partner 合作伙伴模型
     */
    protected function autoRenewToken(ApiPartner $partner)
    {
        try {
            // 如果没有设置过期时间，不处理
            if (empty($partner->expire_time)) {
                return;
            }
            
            $now = time();
            $remainingTime = $partner->expire_time - $now;
            
            // 如果剩余时间小于阈值，自动续期
            if ($remainingTime > 0 && $remainingTime < self::AUTO_RENEW_THRESHOLD) {
                // 计算新的过期时间
                $newExpireTime = $now + self::AUTO_RENEW_DURATION;
                
                // 更新过期时间
                $partner->expire_time = $newExpireTime;
                $partner->save();
                
                // 记录续期日志
                Log::info('API Token自动续期成功', [
                    'partner_id' => $partner->id,
                    'partner_code' => $partner->partner_code,
                    'new_expire_time' => date('Y-m-d H:i:s', $newExpireTime)
                ]);
            }
        } catch (\Exception $e) {
            // 自动续期失败不影响正常流程，只记录日志
            Log::error('API Token自动续期失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查防重放攻击
     * @param Request $request
     * @return bool
     */
    private function checkReplayAttack(Request $request): bool
    {
        $params = $request->param();
        
        // 检查时间戳参数
        if (empty($params['timestamp'])) {
            Log::warning('API请求缺少时间戳参数', [
                'ip' => $request->ip(),
                'url' => $request->url()
            ]);
            return false;
        }
        
        $timestamp = (int)$params['timestamp'];
        $currentTime = time();
        
        // 获取时间窗口配置，默认5分钟
        $timeWindow = config('api.replay_time_window', 300);
        
        // 检查时间戳是否在有效范围内
        if ($currentTime - $timestamp > $timeWindow || $timestamp > $currentTime + 60) {
            Log::warning('API请求时间戳验证失败', [
                'ip' => $request->ip(),
                'url' => $request->url(),
                'timestamp' => $timestamp,
                'current_time' => $currentTime,
                'time_diff' => $currentTime - $timestamp,
                'time_window' => $timeWindow
            ]);
            return false;
        }
        
        // 生成请求唯一标识（基于关键参数）
        $requestId = $this->generateRequestId($request, $timestamp);
        
        // 检查是否为重复请求
        $cacheKey = 'api_replay_check:' . $requestId;
        if (Cache::get($cacheKey)) {
            Log::warning('检测到重放攻击', [
                'ip' => $request->ip(),
                'url' => $request->url(),
                'request_id' => $requestId,
                'timestamp' => $timestamp
            ]);
            return false;
        }
        
        // 记录请求，防止重放（缓存时间为时间窗口的2倍）
        Cache::set($cacheKey, true, $timeWindow * 2);
        
        return true;
    }
    
    /**
     * 生成请求唯一标识
     * @param Request $request
     * @param int $timestamp
     * @return string
     */
    private function generateRequestId(Request $request, int $timestamp): string
    {
        $params = $request->param();
        
        // 获取关键参数用于生成唯一标识
        $keyParams = [
            'timestamp' => $timestamp,
            'partner_code' => $params['partner_code'] ?? '',
            'method' => $request->method(),
            'path' => $request->pathinfo(),
            'ip' => $request->ip()
        ];
        
        // 如果有加密数据，也加入标识
        if (!empty($params['encrypt_data'])) {
            $keyParams['encrypt_data'] = substr(md5($params['encrypt_data']), 0, 16);
        }
        
        // 如果有签名，也加入标识
        if (!empty($params['sign'])) {
            $keyParams['sign'] = substr($params['sign'], 0, 16);
        }
        
        // 生成唯一标识
        return md5(json_encode($keyParams, JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * 返回错误响应
     */
    private function errorResponse(int $code, string $message): Response
    {
        return response()->json([
            'code' => $code === 200 ? 0 : 1,
            'msg' => $message,
            'data' => []
        ], $code);
    }
} 