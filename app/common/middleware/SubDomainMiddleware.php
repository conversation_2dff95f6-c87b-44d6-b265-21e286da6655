<?php
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\common\middleware;

use app\common\repositories\system\merchant\MerchantRepository;
use app\Request;
use think\facade\Config;
use think\facade\Cache;
use think\Response;

/**
 * 二级域名解析中间件
 * Class SubDomainMiddleware
 * @package app\common\middleware
 */
class SubDomainMiddleware extends BaseMiddleware
{
    /**
     * @var MerchantRepository
     */
    protected $merchantRepository;

    /**
     * SubDomainMiddleware constructor.
     * @param MerchantRepository $merchantRepository
     */
    public function __construct(MerchantRepository $merchantRepository)
    {
        $this->merchantRepository = $merchantRepository;
    }

    /**
     * 请求前处理
     *
     * @param Request $request
     */
    public function before(Request $request)
    {
        // 获取主域名配置
        $mainDomain = Config::get('app.main_domain', '');
        if (empty($mainDomain)) {
            return;
        }

        // 获取当前域名
        $host = $request->host();
        $hostParts = explode('.', $host);
        
        // 判断是否为二级域名
        if (count($hostParts) < 3 || !strstr($host, $mainDomain)) {
            return;
        }

        // 提取子域名部分
        $subDomain = $hostParts[0];

        // 从缓存获取商户信息
        $cacheKey = 'subdomain_merchant_' . $subDomain;
        $merId = Cache::get($cacheKey);

        if ($merId === null) {
            // 缓存中不存在，从数据库查询
            $merchant = $this->merchantRepository->dao->getWhere(['sub_domain' => $subDomain, 'is_del' => 0, 'status' => 1]);
            
            if ($merchant) {
                $merId = $merchant['mer_id'];
                // 缓存商户ID，有效期1小时
                Cache::set($cacheKey, $merId, 3600);
            } else {
                // 未找到对应商户
                return;
            }
        }

        // 将商户ID绑定到请求中
        $request->macro('merchant', function () use ($merId) {
            return $merId;
        });

        // 将商户ID添加到请求参数中
        if (!$request->has('mer_id')) {
            $request->withParam('mer_id', $merId);
        }
    }

    /**
     * 请求后处理
     *
     * @param Response $response
     */
    public function after(Response $response)
    {
        // 请求后处理逻辑，如有需要
    }
}
