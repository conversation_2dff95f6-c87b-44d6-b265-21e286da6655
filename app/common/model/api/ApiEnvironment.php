<?php

namespace app\common\model\api;

use think\Model;

/**
 * API环境配置模型
 * Class ApiEnvironment
 * @package app\common\model\api
 */
class ApiEnvironment extends Model
{
    /**
     * @var string 表名
     */
    protected $name = 'api_environment';
    
    /**
     * @var string 主键
     */
    protected $pk = 'id';
    
    /**
     * @var bool 自动写入时间戳
     */
    protected $autoWriteTimestamp = true;
    
    /**
     * @var array 允许写入的字段
     */
    protected $field = [
        'id',
        'env_name',
        'env_key',
        'is_default',
        'is_enabled',
        'api_base_url',
        'description',
        'create_time',
        'update_time'
    ];
    
    /**
     * 生产环境
     */
    const ENV_PRODUCTION = 'production';
    
    /**
     * 测试环境
     */
    const ENV_TEST = 'test';
    
    /**
     * 开发环境
     */
    const ENV_DEVELOPMENT = 'development';
    
    /**
     * 获取默认环境
     * @return array|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getDefault()
    {
        return self::where('is_default', 1)->where('is_enabled', 1)->find();
    }
    
    /**
     * 获取指定环境
     * @param string $envKey 环境标识
     * @return array|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getByKey(string $envKey)
    {
        return self::where('env_key', $envKey)->where('is_enabled', 1)->find();
    }
    
    /**
     * 获取所有启用的环境
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getEnabledEnvironments()
    {
        return self::where('is_enabled', 1)->select();
    }
} 