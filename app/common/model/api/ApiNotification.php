<?php

namespace app\common\model\api;

use think\Model;

/**
 * API通知记录模型
 * Class ApiNotification
 * @package app\common\model\api
 */
class ApiNotification extends Model
{
    /**
     * @var string 表名
     */
    protected $name = 'api_notifications';
    
    /**
     * @var string 主键
     */
    protected $pk = 'id';
    
    /**
     * @var bool 自动写入时间戳
     */
    protected $autoWriteTimestamp = true;
    
    /**
     * @var array 允许写入的字段
     */
    protected $field = [
        'id',
        'order_id',
        'partner_code',
        'notify_url',
        'notify_data',
        'status',
        'retry_count',
        'response',
        'create_time',
        'update_time'
    ];
    
    /**
     * 通知状态：失败
     */
    const STATUS_FAIL = 0;
    
    /**
     * 通知状态：成功
     */
    const STATUS_SUCCESS = 1;
    
    /**
     * 最大重试次数
     */
    const MAX_RETRY_COUNT = 8;
    
    /**
     * 重试间隔（秒）
     * 第1次：立即
     * 第2次：5分钟后
     * 第3次：15分钟后
     * 第4次：30分钟后
     * 第5次：1小时后
     * 第6次：2小时后
     * 第7次：6小时后
     * 第8次：24小时后
     */
    public static $retryIntervals = [
        0, 
        300,     // 5分钟
        900,     // 15分钟
        1800,    // 30分钟
        3600,    // 1小时
        7200,    // 2小时
        21600,   // 6小时
        86400    // 24小时
    ];
    
    /**
     * 获取下一次重试时间
     * @return int 重试时间（秒）
     */
    public function getNextRetryTime(): int
    {
        $retryCount = $this->retry_count;
        if ($retryCount >= self::MAX_RETRY_COUNT) {
            return 0; // 已达到最大重试次数
        }
        
        return self::$retryIntervals[$retryCount];
    }
} 