<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\common\model\api;

use app\common\model\BaseModel;
use app\common\model\user\User;

/**
 * API合作伙伴模型
 * Class ApiPartner
 * @package app\common\model\api
 */
class ApiPartner extends BaseModel
{
    /**
     * 表名
     * @var string
     */
    protected $name = 'api_partners';

    /**
     * 主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 自动写入时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'create_time';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'update_time';
    
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        // api_secret已移除，使其可见
    ];

    /**
     * 追加字段
     * @var array
     */
    protected $append = [
        'expire_time_text'
    ];

    /**
     * 获取表主键
     * @return string
     */
    public static function tablePk(): ?string
    {
        return 'id';
    }

    /**
     * 获取表名
     * @return string
     */
    public static function tableName(): string
    {
        return 'api_partners';
    }

    /**
     * 关联用户模型
     * @return \think\model\relation\HasOne
     */
    public function user()
    {
        return $this->hasOne(User::class, 'uid', 'uid');
    }

    /**
     * IP白名单访问器
     * @param $value
     * @return array
     */
    public function getIpWhitelistAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }

    /**
     * IP白名单修改器
     * @param $value
     * @return string
     */
    public function setIpWhitelistAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    /**
     * 生成合作伙伴编码
     * @return string
     */
    public static function generatePartnerCode(): string
    {
        return 'P' . date('Ymd') . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 6));
    }

    /**
     * 生成API Token
     * @return string
     */
    public static function generateToken(): string
    {
        return md5(uniqid(mt_rand(), true) . time());
    }

    /**
     * 生成API Secret
     * @return string
     */
    public static function generateSecret(): string
    {
        return md5(uniqid(mt_rand(), true) . microtime(true));
    }

  

    /**
     * 获取过期时间文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getExpireTimeTextAttr($value, $data)
    {
        if (isset($data['expire_time']) && $data['expire_time']) {
            return date('Y-m-d H:i:s', $data['expire_time']);
        }
        return '';
    }
}