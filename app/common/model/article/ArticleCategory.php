<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------


namespace app\common\model\article;


use app\common\model\BaseModel;

class ArticleCategory extends BaseModel
{

    /**
     * @return string
     * <AUTHOR>
     * @day 2020-03-30
     */
    public static function tablePk(): string
    {
        return 'article_category_id';
    }

    /**
     * @return string
     * <AUTHOR>
     * @day 2020-03-30
     */
    public static function tableName(): string
    {
        return 'article_category';
    }
}
