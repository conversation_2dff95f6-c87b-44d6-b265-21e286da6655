<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\common\model\merchant;

use app\common\model\BaseModel;

/**
 * 支付宝商户授权信息模型
 * Class MerchantAlipayAuth
 * @package app\common\model\merchant
 */
class MerchantAlipayAuth extends BaseModel
{
    /**
     * 获取数据表主键
     * @return string
     */
    public static function tablePk(): ?string
    {
        return 'id';
    }

    /**
     * 获取数据表名称
     * @return string
     */
    public static function tableName(): string
    {
        return 'merchant_alipay_auth';
    }

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'merchant_alipay_auth';

    /**
     * 自动写入时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 添加时间
     * @var string
     */
    protected $createTime = 'create_time';

    /**
     * 更新时间
     * @var string
     */
    protected $updateTime = 'update_time';

    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'app_auth_token',
        'refresh_token'
    ];

    /**
     * 授权状态
     * @param $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 商户ID搜索器
     * @param $query
     * @param $value
     */
    public function searchMerIdAttr($query, $value)
    {
        if ($value) {
            $query->where('mer_id', $value);
        }
    }

    /**
     * 授权方应用ID搜索器
     * @param $query
     * @param $value
     */
    public function searchAuthAppIdAttr($query, $value)
    {
        if ($value) {
            $query->where('auth_app_id', $value);
        }
    }
}
