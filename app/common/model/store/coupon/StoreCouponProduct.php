<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------


namespace app\common\model\store\coupon;


use app\common\model\BaseModel;
use app\common\model\store\product\Product;

class StoreCouponProduct extends BaseModel
{

    /**
     * @return string
     * <AUTHOR>
     * @day 2020-03-30
     */
    public static function tablePk():? string
    {
        return null;
    }

    /**
     * @return string
     * <AUTHOR>
     * @day 2020-03-30
     */
    public static function tableName(): string
    {
        return 'store_coupon_product';
    }

    public function product()
    {
        return $this->hasOne(Product::class, 'product_id', 'product_id');
    }
}
