<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------


namespace app\common\model\store\order;


use app\common\model\BaseModel;
use app\common\model\user\User;
use app\common\repositories\store\coupon\StoreCouponRepository;

class StoreGroupOrder extends BaseModel
{

    public static function tablePk(): ?string
    {
        return 'group_order_id';
    }

    public static function tableName(): string
    {
        return 'store_group_order';
    }

    public function orderList()
    {
        return $this->hasMany(StoreOrder::class, 'group_order_id', 'group_order_id');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'uid', 'uid');
    }

    public function getGiveCouponAttr()
    {
        if (count($this->give_coupon_ids))
            return app()->make(StoreCouponRepository::class)->getGiveCoupon($this->give_coupon_ids);
        return [];
    }

    public function getCancelTimeAttr()
    {
        $timer = ((int)systemConfig('auto_close_order_timer')) ?: 15;
        return date('m-d H:i', strtotime("+ $timer minutes", strtotime($this->create_time)));
    }

    public function getCancelUnixAttr()
    {
        $timer = ((int)systemConfig('auto_close_order_timer')) ?: 15;
        return strtotime("+ $timer minutes", strtotime($this->create_time));
    }

    public function getGiveCouponIdsAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }

    public function setGiveCouponIdsAttr($value)
    {
        return $value ? implode(',', $value) : '';
    }

    public function getCombinePayParams()
    {
        $params = [
            'order_sn' => $this->group_order_sn,
            'sub_orders' => [],
            'attach' => 'order',
            'body' => '订单支付',
        ];
        foreach ($this->orderList as $order) {
            if ($order->pay_price > 0) {
                $subOrder = [
                    'pay_price' => $order->pay_price,
                    'order_sn' => $order->order_sn,
                    'sub_mchid' => $order->merchant->sub_mchid,
                ];
                $params['sub_orders'][] = $subOrder;
            }
        }
        return $params;
    }
    
    /**
     * 获取支付宝服务商支付参数
     * @return array
     */
    public function getAlipayServicePayParams()
    {
        $params = [
            'out_trade_no' => $this->group_order_sn,
            'total_amount' => $this->pay_price,
            'subject' => '订单支付',
            'sub_orders' => [],
        ];
        
        foreach ($this->orderList as $order) {
            if ($order->pay_price > 0 && !empty($order->merchant->alipay_smid)) {
                $subOrder = [
                    'amount' => $order->pay_price,
                    'sub_merchant_id' => $order->merchant->alipay_smid,
                ];
                $params['sub_orders'][] = $subOrder;
            }
        }
        
        return $params;
    }

    /**
     * 获取支付参数
     * @param string $return_url 返回地址（支付宝支付使用）
     * @param string $auth_code 授权码（条码支付使用）
     * @param string $payType 支付类型（weixin, routine, h5, weixinQr, alipay, alipayApp）
     * @return array 支付参数
     */
    public function getPayParams($return_url = '', $auth_code = '', $payType = '')
    {
        $params = [
            'order_sn' => $this->group_order_sn,
            'pay_price' => $this->pay_price,
            'attach' => 'order',
            'body' => '订单支付'
        ];

        // 获取商户的支付类型配置
        $configKeys = ['pay_type'];
        
        // 根据支付类型加载相应的配置项
        $isAlipay = strpos($payType, 'alipay') !== false;
        $isWeixin = in_array($payType, ['weixin', 'weixinApp', 'weixinQr'], true);
        $isRoutine = $payType === 'routine';
        $isH5 = $payType === 'h5';
        
        // 加载微信支付相关配置
        if ($isWeixin) {
            $configKeys = array_merge($configKeys, [
                'pay_weixin_open', 'pay_weixin_mchid', 'pay_weixin_key',
                'pay_weixin_client_cert', 'pay_weixin_client_key', 'pay_weixin_serial_no'
            ]);
        }
        
        // 加载小程序支付相关配置
        if ($isRoutine) {
            $configKeys = array_merge($configKeys, [
                'pay_routine_open', 'pay_routine_mchid', 'pay_routine_key',
                'pay_routine_client_cert', 'pay_routine_client_key', 'pay_routine_serial_no'
            ]);
        }
        
        // 加载H5支付相关配置
        if ($isH5) {
            $configKeys = array_merge($configKeys, [
                'pay_weixin_open', 'pay_weixin_mchid', 'pay_weixin_key',
                'pay_weixin_client_cert', 'pay_weixin_client_key', 'pay_weixin_serial_no'
            ]);
        }
        
        // 加载支付宝支付相关配置
        if ($isAlipay) {
            $configKeys = array_merge($configKeys, [
                'alipay_open', 'alipay_appid', 'alipay_private_key', 'alipay_public_key',
                'alipay_cert_mode', 'alipay_app_public_cert_path', 'alipay_public_cert_path', 'alipay_root_cert_path',
                'alipay_service_mode'
            ]);
        }
        
        // 使用默认平台商户ID
        $merId = 0;
        
        // 尝试预加载订单列表关联
        if (!isset($this->orderList)) {
            $this->load('orderList');
        }
        
        // 尝试从订单列表中获取商户ID
        if (isset($this->orderList) && !empty($this->orderList)) {
            foreach ($this->orderList as $order) {
                if (isset($order->mer_id) && $order->mer_id > 0) {
                    $merId = (int)$order->mer_id;
                    break;
                }
            }
        }
        
        // 使用有效的商户ID获取配置
        $merchantConfig = merchantConfig($merId, $configKeys);

        // 设置商户支付配置
        $mer_config = [];
        
        // 微信支付配置
        if ($isWeixin && !empty($merchantConfig['pay_weixin_open']) && $merchantConfig['pay_weixin_open'] == '1') {
            // 微信公众号支付配置
            $mer_config['wechat'] = [
                'pay_weixin_mchid' => $merchantConfig['pay_weixin_mchid'] ?? '',
                'pay_weixin_key' => $merchantConfig['pay_weixin_key'] ?? '',
                'pay_weixin_client_cert' => $merchantConfig['pay_weixin_client_cert'] ?? '',
                'pay_weixin_client_key' => $merchantConfig['pay_weixin_client_key'] ?? '',
                'pay_weixin_serial_no' => $merchantConfig['pay_weixin_serial_no'] ?? ''
            ];
        }
        
        // 小程序支付配置
        if ($isRoutine && !empty($merchantConfig['pay_routine_open']) && $merchantConfig['pay_routine_open'] == '1') {
            // 微信小程序支付配置
            $mer_config['routine'] = [
                'pay_routine_mchid' => $merchantConfig['pay_routine_mchid'] ?? '',
                'pay_routine_key' => $merchantConfig['pay_routine_key'] ?? '',
                'pay_routine_client_cert' => $merchantConfig['pay_routine_client_cert'] ?? '',
                'pay_routine_client_key' => $merchantConfig['pay_routine_client_key'] ?? '',
                'pay_routine_serial_no' => $merchantConfig['pay_routine_serial_no'] ?? ''
            ];
        }
        
        // H5支付配置
        if ($isH5 && !empty($merchantConfig['pay_weixin_open']) && $merchantConfig['pay_weixin_open'] == '1') {
            // 微信H5支付配置
            $mer_config['h5'] = [
                'pay_weixin_mchid' => $merchantConfig['pay_weixin_mchid'] ?? '',
                'pay_weixin_key' => $merchantConfig['pay_weixin_key'] ?? '',
                'pay_weixin_client_cert' => $merchantConfig['pay_weixin_client_cert'] ?? '',
                'pay_weixin_client_key' => $merchantConfig['pay_weixin_client_key'] ?? '',
                'pay_weixin_serial_no' => $merchantConfig['pay_weixin_serial_no'] ?? ''
            ];
        }
        
        // 支付宝支付配置
        if ($isAlipay && !empty($merchantConfig['alipay_open']) && $merchantConfig['alipay_open'] == '1') {
            $mer_config['alipay'] = [
                'alipay_appid' => $merchantConfig['alipay_appid'] ?? '',
                'alipay_private_key' => $merchantConfig['alipay_private_key'] ?? '',
                'alipay_public_key' => $merchantConfig['alipay_public_key'] ?? ''
            ];
            
            // 支付宝证书模式
            if (!empty($merchantConfig['alipay_cert_mode']) && $merchantConfig['alipay_cert_mode'] == '1') {
                $mer_config['alipay']['alipay_cert_mode'] = '1';
                $mer_config['alipay']['alipay_app_public_cert_path'] = $merchantConfig['alipay_app_public_cert_path'] ?? '';
                $mer_config['alipay']['alipay_public_cert_path'] = $merchantConfig['alipay_public_cert_path'] ?? '';
                $mer_config['alipay']['alipay_root_cert_path'] = $merchantConfig['alipay_root_cert_path'] ?? '';
            }
            
            // 支付宝服务商模式
            if (!empty($merchantConfig['alipay_service_mode']) && $merchantConfig['alipay_service_mode'] == '1') {
                $mer_config['alipay']['alipay_service_mode'] = '1';
            }
        }
        
        // 服务商模式下设置二级商户ID
        if (!empty($merchantConfig['pay_type']) && $merchantConfig['pay_type'] == 1) {
            $params['sub_mchid'] = $this->merchant->sub_mchid;
            
            // 如果是支付宝直付通模式，设置二级商户ID
            if ($isAlipay && !empty($this->merchant->alipay_smid)) {
                $mer_config['alipay']['merchant_id'] = $this->merchant->alipay_smid;
            }
        }
        
        // 设置商户配置到参数中
        if (!empty($mer_config)) {
            $params['mer_config'] = $mer_config;
        }

        // 设置回调地址（支付宝支付使用）
        if ($return_url) {
            $params['return_url'] = $return_url;
        }
        
        // 设置授权码（条码支付使用）
        if ($auth_code) {
            $params['auth_code'] = $auth_code;
        }

        return $params;
    }
}
