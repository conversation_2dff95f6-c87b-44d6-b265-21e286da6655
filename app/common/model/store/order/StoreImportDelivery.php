<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------


namespace app\common\model\store\order;

use app\common\model\BaseModel;

class StoreImportDelivery extends BaseModel
{

    public static function tablePk(): ?string
    {
        return 'import_delivery_id';
    }

    public static function tableName(): string
    {
        return 'store_import_delivery';
    }

    public function getStatusAttr($value)
    {
        return $value ? '已发货' : '未发货';
    }

    public function searchImportIdAttr($query,$value)
    {
        $query->where('import_id',$value);
    }

    public function searchMerIdAttr($query,$value)
    {
        $query->where('mer_id',$value);
    }

}
