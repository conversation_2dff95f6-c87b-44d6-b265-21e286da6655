<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------


namespace app\common\model\store\order;


use app\common\model\BaseModel;

/**
 * Class StoreOrderStatus
 * @package app\common\model\store\order
 * <AUTHOR>
 * @day 2020/6/12
 */
class StoreOrderStatus extends BaseModel
{

    /**
     * @return string|null
     * <AUTHOR>
     * @day 2020/6/12
     */
    public static function tablePk(): ?string
    {
        return null;
    }

    /**
     * @return string
     * <AUTHOR>
     * @day 2020/6/12
     */
    public static function tableName(): string
    {
        return 'store_order_status';
    }
}
