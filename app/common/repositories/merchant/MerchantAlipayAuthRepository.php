<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\common\repositories\merchant;

use app\common\dao\BaseDao;
use app\common\model\merchant\MerchantAlipayAuth;
use app\common\repositories\BaseRepository;
use crmeb\services\alipay\AlipayService;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;

/**
 * 支付宝商户授权信息仓库
 * Class MerchantAlipayAuthRepository
 * @package app\common\repositories\merchant
 */
class MerchantAlipayAuthRepository extends BaseRepository
{
    /**
     * MerchantAlipayAuthRepository constructor.
     * @param MerchantAlipayAuth $dao
     */
    public function __construct(MerchantAlipayAuth $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取商户授权信息
     * @param int $merId 商户ID
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getAuthInfo(int $merId)
    {
        return $this->dao->where('mer_id', $merId)->find();
    }

    /**
     * 保存授权信息
     * @param int $merId 商户ID
     * @param array $authInfo 授权信息
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function saveAuthInfo(int $merId, array $authInfo)
    {
        $data = [
            'mer_id' => $merId,
            'app_auth_token' => $authInfo['app_auth_token'] ?? '',
            'auth_app_id' => $authInfo['auth_app_id'] ?? '',
            'user_id' => $authInfo['user_id'] ?? '',
            'expires_in' => $authInfo['expires_in'] ?? 0,
            'refresh_token' => $authInfo['refresh_token'] ?? '',
            're_expires_in' => $authInfo['re_expires_in'] ?? 0,
            'status' => 1,
            'auth_time' => time(),
            'expire_time' => time() + ($authInfo['expires_in'] ?? 0),
            'refresh_time' => time(),
        ];

        // 检查是否已存在授权记录
        $exist = $this->dao->where('mer_id', $merId)->find();
        
        Db::startTrans();
        try {
            if ($exist) {
                // 更新现有记录
                $this->dao->where('mer_id', $merId)->update($data);
            } else {
                // 创建新记录
                $this->dao->create($data);
            }
            
            // 更新商户表的alipay_smid字段
            if (!empty($authInfo['user_id'])) {
                Db::name('merchant')->where('mer_id', $merId)->update([
                    'alipay_smid' => $authInfo['user_id']
                ]);
            }
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('保存支付宝授权信息失败：' . $e->getMessage());
            throw new ValidateException('保存授权信息失败：' . $e->getMessage());
        }
    }

    /**
     * 刷新授权令牌
     * @param int $merId 商户ID
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function refreshAuthToken(int $merId)
    {
        $authInfo = $this->dao->where('mer_id', $merId)->find();
        if (!$authInfo) {
            throw new ValidateException('未找到授权信息');
        }

        if (empty($authInfo['refresh_token'])) {
            throw new ValidateException('刷新令牌为空，无法刷新授权');
        }

        // 获取支付宝配置
        $app_id = systemConfig('alipay_service_app_id');
        $private_key = systemConfig('alipay_service_private_key');

        if (empty($app_id) || empty($private_key)) {
            throw new ValidateException('支付宝服务商配置不完整');
        }

        try {
            // 调用支付宝服务刷新授权令牌
            $alipayService = new AlipayService([
                'app_id' => $app_id,
                'private_key' => $private_key
            ]);
            
            $result = $alipayService->refreshAppAuthToken($app_id, $private_key, $authInfo['refresh_token']);
            
            // 保存新的授权信息
            return $this->saveAuthInfo($merId, $result);
        } catch (\Exception $e) {
            Log::error('刷新支付宝授权令牌失败：' . $e->getMessage());
            throw new ValidateException('刷新授权令牌失败：' . $e->getMessage());
        }
    }

    /**
     * 检查授权状态
     * @param int $merId 商户ID
     * @return array 授权状态信息
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function checkAuthStatus(int $merId)
    {
        $authInfo = $this->dao->where('mer_id', $merId)->find();
        if (!$authInfo) {
            return [
                'is_auth' => false,
                'message' => '未授权'
            ];
        }

        // 检查授权是否过期
        if ($authInfo['expire_time'] < time()) {
            // 尝试使用刷新令牌
            if ($authInfo['refresh_token'] && $authInfo['re_expires_in'] > 0) {
                try {
                    $this->refreshAuthToken($merId);
                    return [
                        'is_auth' => true,
                        'message' => '授权有效（已刷新）',
                        'auth_info' => $this->dao->where('mer_id', $merId)->find()
                    ];
                } catch (\Exception $e) {
                    return [
                        'is_auth' => false,
                        'message' => '授权已过期，刷新失败：' . $e->getMessage()
                    ];
                }
            } else {
                return [
                    'is_auth' => false,
                    'message' => '授权已过期，需要重新授权'
                ];
            }
        }

        // 检查授权是否有效
        try {
            // 获取支付宝配置
            $app_id = systemConfig('alipay_service_app_id');
            $private_key = systemConfig('alipay_service_private_key');

            if (empty($app_id) || empty($private_key)) {
                throw new ValidateException('支付宝服务商配置不完整');
            }

            // 调用支付宝服务查询授权状态
            $alipayService = new AlipayService([
                'app_id' => $app_id,
                'private_key' => $private_key
            ]);
            
            $result = $alipayService->queryAppAuthToken($authInfo['app_auth_token']);
            
            // 更新授权状态
            $this->dao->where('mer_id', $merId)->update([
                'status' => $result['valid'] ? 1 : 0,
                'update_time' => date('Y-m-d H:i:s')
            ]);
            
            return [
                'is_auth' => $result['valid'],
                'message' => $result['valid'] ? '授权有效' : '授权已失效',
                'auth_info' => $result
            ];
        } catch (\Exception $e) {
            Log::error('查询支付宝授权状态失败：' . $e->getMessage());
            return [
                'is_auth' => false,
                'message' => '查询授权状态失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 取消授权
     * @param int $merId 商户ID
     * @return bool
     */
    public function cancelAuth(int $merId)
    {
        Db::startTrans();
        try {
            // 删除授权记录
            $this->dao->where('mer_id', $merId)->delete();
            
            // 清空商户表的alipay_smid字段
            Db::name('merchant')->where('mer_id', $merId)->update([
                'alipay_smid' => ''
            ]);
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('取消支付宝授权失败：' . $e->getMessage());
            throw new ValidateException('取消授权失败：' . $e->getMessage());
        }
    }

}
