<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\admin\merchant;

use app\common\repositories\merchant\MerchantAlipayAuthRepository;
use app\common\repositories\merchant\MerchantRepository;
use crmeb\basic\BaseController;
use crmeb\services\alipay\AlipayAuthService;
use think\App;

/**
 * 支付宝商户授权管理
 * Class AlipayAuth
 * @package app\controller\admin\merchant
 */
class AlipayAuth extends BaseController
{
    /**
     * @var AlipayAuthService
     */
    protected $authService;

    /**
     * @var MerchantAlipayAuthRepository
     */
    protected $repository;

    /**
     * @var MerchantRepository
     */
    protected $merchantRepository;

    /**
     * AlipayAuth constructor.
     * @param App $app
     * @param AlipayAuthService $authService
     * @param MerchantAlipayAuthRepository $repository
     * @param MerchantRepository $merchantRepository
     */
    public function __construct(App $app, AlipayAuthService $authService, MerchantAlipayAuthRepository $repository, MerchantRepository $merchantRepository)
    {
        parent::__construct($app);
        $this->authService = $authService;
        $this->repository = $repository;
        $this->merchantRepository = $merchantRepository;
    }

    /**
     * 获取商户授权状态
     * @param int $id 商户ID
     * @return mixed
     */
    public function getAuthStatus($id)
    {
        if (!$id) {
            return app('json')->fail('商户ID不能为空');
        }

        try {
            $status = $this->authService->getAuthStatus((int)$id);
            return app('json')->success($status);
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 生成授权链接
     * @param int $id 商户ID
     * @return mixed
     */
    public function generateAuthUrl($id)
    {
        if (!$id) {
            return app('json')->fail('商户ID不能为空');
        }

        try {
            $url = $this->authService->generateAuthUrl((int)$id, AlipayAuthService::SOURCE_ADMIN);
            return app('json')->success(['url' => $url]);
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 刷新授权令牌
     * @param int $id 商户ID
     * @return mixed
     */
    public function refreshAuthToken($id)
    {
        if (!$id) {
            return app('json')->fail('商户ID不能为空');
        }

        try {
            $this->authService->refreshAuthToken((int)$id);
            return app('json')->success('刷新授权令牌成功');
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 取消授权
     * @param int $id 商户ID
     * @return mixed
     */
    public function cancelAuth($id)
    {
        if (!$id) {
            return app('json')->fail('商户ID不能为空');
        }

        try {
            $this->authService->cancelAuth((int)$id);
            return app('json')->success('取消授权成功');
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 授权回调处理
     * @return mixed
     */
    public function authCallback()
    {
        $auth_code = $this->request->get('app_auth_code', '');
        $state = $this->request->get('state', '');

        if (empty($auth_code)) {
            return $this->failed('未获取到授权码');
        }

        try {
            // 获取商户ID
            $merId = (int)$state;

            // 处理授权回调
            $result = $this->authService->handleAuthCallback($auth_code, $merId, AlipayAuthService::SOURCE_ADMIN);

            return $this->success($result['message'], $result['data'], '/merchant/merchant');
        } catch (\Exception $e) {
            return $this->failed('授权失败：' . $e->getMessage());
        }
    }

    /**
     * 获取商户授权列表
     * @return mixed
     */
    public function getAuthList()
    {
        [$page, $limit] = $this->getPage();
        $where = $this->request->params(['keyword', 'status']);
        
        $list = $this->repository->dao->getList($where, $page, $limit);
        
        // 获取商户信息
        foreach ($list['list'] as &$item) {
            $merchant = $this->merchantRepository->get($item['mer_id']);
            $item['merchant_name'] = $merchant ? $merchant['mer_name'] : '';
        }
        
        return app('json')->success($list);
    }
}
