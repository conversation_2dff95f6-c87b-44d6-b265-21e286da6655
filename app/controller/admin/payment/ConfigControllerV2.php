<?php

namespace app\controller\admin\payment;

use crmeb\basic\BaseController;
use crmeb\services\payment\config\PaymentConfigCenterV2;
use crmeb\services\payment\config\LayeredPaymentFields;
use crmeb\services\payment\config\PaymentModeEnum;
use app\common\repositories\system\merchant\MerchantRepository;
use app\common\repositories\system\merchant\MerchantPaymentConfigRepository;
use think\App;
use think\facade\Db;

/**
 * 支付配置管理控制器V2 - 基于分层架构重构
 */
class ConfigControllerV2 extends BaseController
{
    /**
     * @var MerchantRepository
     */
    protected $merchantRepository;
    
    /**
     * @var MerchantPaymentConfigRepository
     */
    protected $merchantPaymentConfigRepository;
    
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->merchantRepository = app(MerchantRepository::class);
        $this->merchantPaymentConfigRepository = app(MerchantPaymentConfigRepository::class);
    }
    
    /**
     * 获取支付配置（新版本）
     */
    public function getPaymentConfig()
    {
        try {
            $merId = $this->request->param('mer_id', 0);
            $paymentType = $this->request->param('payment_type', '');
            
            if (empty($merId)) {
                return $this->fail('商户ID不能为空');
            }
            
            if (empty($paymentType)) {
                return $this->fail('支付类型不能为空');
            }
            
            // 使用新的配置中心获取配置
            $config = PaymentConfigCenterV2::getEffectiveConfig($merId, $paymentType);
            $modeInfo = PaymentConfigCenterV2::getPaymentModeInfo($merId, $paymentType);
            $validation = PaymentConfigCenterV2::validateConfig($merId, $paymentType);
            $template = PaymentConfigCenterV2::getConfigTemplate($paymentType, $merId);
            
            return $this->success([
                'config' => $config,
                'mode_info' => $modeInfo,
                'validation' => $validation,
                'template' => $template,
                'meta' => [
                    'mer_id' => $merId,
                    'payment_type' => $paymentType,
                    'config_layers' => $modeInfo['config_layers'] ?? []
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->fail('获取支付配置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新支付配置（新版本）
     */
    public function updatePaymentConfig()
    {
        try {
            $merId = $this->request->param('mer_id', 0);
            $paymentType = $this->request->param('payment_type', '');
            $configData = $this->request->param('config', []);
            $layer = $this->request->param('layer', 'base'); // base/service/merchant
            
            if (empty($merId)) {
                return $this->fail('商户ID不能为空');
            }
            
            if (empty($paymentType)) {
                return $this->fail('支付类型不能为空');
            }
            
            // 验证配置数据
            $validation = $this->validateConfigData($paymentType, $configData, $layer, $merId);
            if (!$validation['valid']) {
                return $this->fail('配置验证失败: ' . implode(', ', $validation['errors']));
            }
            
            // 根据层级保存配置
            $result = $this->saveConfigByLayer($merId, $paymentType, $configData, $layer);
            
            if ($result) {
                // 清除缓存
                PaymentConfigCenterV2::clearCache($merId, $paymentType);
                
                return $this->success([
                    'mer_id' => $merId,
                    'payment_type' => $paymentType,
                    'layer' => $layer,
                    'updated_fields' => array_keys($configData)
                ], '配置更新成功');
            } else {
                return $this->fail('配置更新失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail('更新支付配置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取支付模式信息
     */
    public function getPaymentModes()
    {
        try {
            $merId = $this->request->param('mer_id', 0);
            $paymentType = $this->request->param('payment_type', '');
            
            if (!empty($merId) && !empty($paymentType)) {
                // 获取特定商户和支付类型的模式信息
                $modeInfo = PaymentConfigCenterV2::getPaymentModeInfo($merId, $paymentType);
                return $this->success($modeInfo);
            } else {
                // 获取所有支持的支付模式
                $modes = PaymentConfigCenterV2::getSupportedPaymentModes();
                return $this->success([
                    'modes' => $modes,
                    'payment_types' => array_keys(PaymentModeEnum::PAYMENT_TYPE_TO_MODE)
                ]);
            }
            
        } catch (\Exception $e) {
            return $this->fail('获取支付模式失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取配置模板
     */
    public function getConfigTemplate()
    {
        try {
            $paymentType = $this->request->param('payment_type', '');
            $merId = $this->request->param('mer_id', 0);
            
            if (empty($paymentType)) {
                return $this->fail('支付类型不能为空');
            }
            
            $template = PaymentConfigCenterV2::getConfigTemplate($paymentType, $merId);
            
            return $this->success([
                'payment_type' => $paymentType,
                'template' => $template,
                'layers' => $this->getLayerTemplates($paymentType, $merId)
            ]);
            
        } catch (\Exception $e) {
            return $this->fail('获取配置模板失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 验证配置完整性
     */
    public function validateConfig()
    {
        try {
            $merId = $this->request->param('mer_id', 0);
            $paymentType = $this->request->param('payment_type', '');
            
            if (empty($merId)) {
                return $this->fail('商户ID不能为空');
            }
            
            if (empty($paymentType)) {
                return $this->fail('支付类型不能为空');
            }
            
            $validation = PaymentConfigCenterV2::validateConfig($merId, $paymentType);
            
            return $this->success($validation);
            
        } catch (\Exception $e) {
            return $this->fail('验证配置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 清除配置缓存
     */
    public function clearCache()
    {
        try {
            $merId = $this->request->param('mer_id');
            $paymentType = $this->request->param('payment_type');
            
            PaymentConfigCenterV2::clearCache($merId, $paymentType);
            
            return $this->success([], '缓存清除成功');
            
        } catch (\Exception $e) {
            return $this->fail('清除缓存失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 验证配置数据
     * @param string $paymentType 支付类型
     * @param array $configData 配置数据
     * @param string $layer 配置层级
     * @param int $merId 商户ID
     * @return array
     */
    private function validateConfigData(string $paymentType, array $configData, string $layer, int $merId): array
    {
        $errors = [];
        
        // 确定配置类型
        $modeInfo = PaymentConfigCenterV2::getPaymentModeInfo($merId, $paymentType);
        $configLayers = $modeInfo['config_layers'] ?? [];
        
        if (!isset($configLayers[$layer])) {
            $errors[] = "不支持的配置层级: {$layer}";
            return ['valid' => false, 'errors' => $errors];
        }
        
        $configType = $configLayers[$layer];
        $fields = LayeredPaymentFields::getConfigTypeFields($configType);
        
        // 验证必填字段
        foreach ($fields as $fieldName => $fieldDef) {
            if ($fieldDef['required'] && empty($configData[$fieldName])) {
                $errors[] = "缺少必填字段: {$fieldDef['label']}({$fieldName})";
            }
        }
        
        // 验证字段类型
        foreach ($configData as $fieldName => $value) {
            if (isset($fields[$fieldName])) {
                $fieldType = $fields[$fieldName]['type'];
                if (!$this->validateFieldType($value, $fieldType)) {
                    $errors[] = "字段类型错误: {$fieldName} 应为 {$fieldType} 类型";
                }
            }
        }
        
        return ['valid' => empty($errors), 'errors' => $errors];
    }
    
    /**
     * 验证字段类型
     * @param mixed $value 字段值
     * @param string $type 期望类型
     * @return bool
     */
    private function validateFieldType($value, string $type): bool
    {
        switch ($type) {
            case 'string':
                return is_string($value);
            case 'text':
                return is_string($value);
            case 'boolean':
                return is_bool($value) || in_array($value, [0, 1, '0', '1', 'true', 'false']);
            case 'integer':
                return is_numeric($value);
            default:
                return true;
        }
    }
    
    /**
     * 根据层级保存配置
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @param array $configData 配置数据
     * @param string $layer 配置层级
     * @return bool
     */
    private function saveConfigByLayer(int $merId, string $paymentType, array $configData, string $layer): bool
    {
        if ($layer === 'base' || $layer === 'merchant') {
            // 商户层配置保存到商户配置表
            return $this->saveMerchantConfig($merId, $paymentType, $configData);
        } elseif ($layer === 'service') {
            // 服务商层配置保存到系统配置
            return $this->saveSystemConfig($paymentType, $configData);
        }
        
        return false;
    }
    
    /**
     * 保存商户配置
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @param array $configData 配置数据
     * @return bool
     */
    private function saveMerchantConfig(int $merId, string $paymentType, array $configData): bool
    {
        try {
            Db::startTrans();
            
            // 查找或创建配置记录
            $configRecord = Db::name('merchant_payment_config')
                ->where('mer_id', $merId)
                ->where('payment_type', $paymentType)
                ->find();
            
            if (!$configRecord) {
                $configId = Db::name('merchant_payment_config')->insertGetId([
                    'mer_id' => $merId,
                    'payment_type' => $paymentType,
                    'status' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ]);
            } else {
                $configId = $configRecord['id'];
                Db::name('merchant_payment_config')
                    ->where('id', $configId)
                    ->update(['update_time' => time()]);
            }
            
            // 删除旧参数
            Db::name('merchant_payment_params')
                ->where('mer_id', $merId)
                ->where('payment_id', $configId)
                ->delete();
            
            // 插入新参数
            $params = [];
            foreach ($configData as $key => $value) {
                if (!empty($value)) {
                    $params[] = [
                        'mer_id' => $merId,
                        'payment_id' => $configId,
                        'param_name' => $key,
                        'param_value' => is_array($value) ? json_encode($value) : $value,
                        'create_time' => time()
                    ];
                }
            }
            
            if (!empty($params)) {
                Db::name('merchant_payment_params')->insertAll($params);
            }
            
            Db::commit();
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 保存系统配置
     * @param string $paymentType 支付类型
     * @param array $configData 配置数据
     * @return bool
     */
    private function saveSystemConfig(string $paymentType, array $configData): bool
    {
        // 获取系统配置映射
        $mapping = LayeredPaymentFields::getSystemConfigMapping($paymentType . '_service');
        
        foreach ($configData as $field => $value) {
            if (isset($mapping[$field])) {
                $systemKey = $mapping[$field];
                // 更新系统配置
                Db::name('system_config')
                    ->where('menu_name', $systemKey)
                    ->update(['value' => $value]);
            }
        }
        
        return true;
    }
    
    /**
     * 获取分层模板
     * @param string $paymentType 支付类型
     * @param int $merId 商户ID
     * @return array
     */
    private function getLayerTemplates(string $paymentType, int $merId): array
    {
        $modeInfo = PaymentConfigCenterV2::getPaymentModeInfo($merId, $paymentType);
        $configLayers = $modeInfo['config_layers'] ?? [];
        
        $templates = [];
        
        foreach ($configLayers as $layer => $configType) {
            $fields = LayeredPaymentFields::getConfigTypeFields($configType);
            $templates[$layer] = [
                'config_type' => $configType,
                'fields' => $fields,
                'layer_name' => $this->getLayerDisplayName($layer)
            ];
        }
        
        return $templates;
    }
    
    /**
     * 获取层级显示名称
     * @param string $layer 层级
     * @return string
     */
    private function getLayerDisplayName(string $layer): string
    {
        $names = [
            'base' => '基础配置',
            'service' => '服务商配置',
            'merchant' => '商户配置'
        ];
        
        return $names[$layer] ?? $layer;
    }
}
