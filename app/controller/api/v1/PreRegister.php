<?php
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1;

use app\common\repositories\store\order\StoreOrderRepository;
use app\common\repositories\user\UserRepository;
use app\common\repositories\system\config\ConfigRepository;
use app\common\repositories\store\product\ProductRepository;
use app\common\repositories\store\StoreCategoryRepository;
use app\common\repositories\store\coupon\StoreCouponRepository;
use app\common\repositories\store\coupon\StoreCouponUserRepository;
use app\common\repositories\wechat\WechatUserRepository;
use app\common\repositories\system\merchant\MerchantRepository;
use crmeb\basic\BaseController;
use crmeb\services\payment\UnifiedPaymentService;
use think\App;
use think\facade\Db;
use think\facade\Log;
use think\exception\ValidateException;
use think\facade\Cache;
use think\Request;

/**
 * 预注册控制器 - 完全集成统一支付架构
 * 专注于微信支付宝小程序内置支付
 */
class PreRegister extends BaseController
{
    use PreRegisterAlgorithms;
    
    protected $repository;
    protected $userRepository;
    protected $configRepository;
    protected $productRepository;
    protected $categoryRepository;
    protected $couponRepository;
    protected $couponUserRepository;
    protected $wechatUserRepository;
    protected $merchantRepository;
    protected $unifiedPaymentService;

    public function __construct(App $app, 
                              StoreOrderRepository $repository,
                              UserRepository $userRepository,
                              ConfigRepository $configRepository,
                              ProductRepository $productRepository,
                              StoreCategoryRepository $categoryRepository,
                              StoreCouponRepository $couponRepository,
                              StoreCouponUserRepository $couponUserRepository,
                              WechatUserRepository $wechatUserRepository,
        MerchantRepository $merchantRepository,
                              UnifiedPaymentService $unifiedPaymentService)
    {
        parent::__construct($app);
        $this->repository = $repository;
        $this->userRepository = $userRepository;
        $this->configRepository = $configRepository;
        $this->productRepository = $productRepository;
        $this->categoryRepository = $categoryRepository;
        $this->couponRepository = $couponRepository;
        $this->couponUserRepository = $couponUserRepository;
        $this->wechatUserRepository = $wechatUserRepository;
        $this->merchantRepository = $merchantRepository;
        $this->unifiedPaymentService = $unifiedPaymentService;
    }

    /**
     * 获取预注册支付URL - 统一支付架构
     */
    public function getPayUrl()
    {
        $orderId = $this->request->param('order_id', '');
        $payType = $this->request->param('pay_type', '');
        $merId = $this->request->param('mer_id', 0);

        if (empty($orderId)) {
            return $this->fail('订单ID不能为空');
        }

        if (empty($payType)) {
            return $this->fail('支付方式不能为空');
        }

        try {

          
            // 获取订单信息
            $order = $this->getOrderInfo($orderId);
            if (!$order) {
                return $this->fail('订单不存在');
            }

            // 检查订单状态
            if ($order['paid'] == 1) {
                return $this->fail('订单已支付');
            }

            if ($order['is_del'] == 1) {
                return $this->fail('订单已删除');
            }
          
            // 构建统一支付配置
           //$paymentConfig = $this->buildUnifiedPaymentConfig($order, $payType, $merId);
            
            // 调用统一支付服务 - 修复：按照正确的参数顺序调用
            $orderData = [
                'order_no' => $order['order_sn'],
                'amount' => $order['pay_price'],
                'subject' => '预注册开通服务',
                'body' => '预注册开通服务 - 订单号: ' . $order['order_sn'],
                'attach' => 'pre_register'
            ];

            $config = [
                'mer_id' => $merId,
                'return_url' => $this->buildReturnUrl($order['order_id']),
                'notify_url' => $this->buildNotifyUrl($order['order_id']),
                'affect' => 'pre_register',
                'order_type' => 'pre_register'
            ];

            $paymentResult = $this->unifiedPaymentService->createPayment($payType, $orderData, $config);

            // 检查支付结果 - 统一支付服务直接返回结果数组
            if (empty($paymentResult) || (isset($paymentResult['success']) && !$paymentResult['success'])) {
                $errorMessage = $paymentResult['message'] ?? '支付服务创建失败';
                Log::error('[PreRegister Payment] 统一支付创建失败', [
                    'order_id' => $orderId,
                    'error' => $errorMessage,
                    'result' => $paymentResult
                ]);
                return $this->fail($errorMessage);
            }

            // 记录支付成功创建
            Log::info('[PreRegister Payment] 统一支付创建成功', [
                'order_id' => $orderId,
                'pay_type' => $payType,
                'payment_result' => $paymentResult
            ]);

            // 返回统一格式的支付结果
            return $this->success([
                'unified_result' => $paymentResult['unified_result'] ?? $paymentResult,
                'order_info' => [
                    'order_id' => $order['order_id'],
                    'order_sn' => $order['order_sn'],
                    'pay_price' => $order['pay_price']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('[PreRegister Payment] 支付异常', [
                'order_id' => $orderId,
                'pay_type' => $payType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->fail('支付请求失败: ' . $e->getMessage());
        }
    }

    /**
     * 构建统一支付配置
     */
    private function buildUnifiedPaymentConfig($order, $payType, $merId = 0)
    {
        // 基础配置
        $config = [
            'service_type' => 'pre_register',
            'order_id' => $order['order_id'],
            'order_sn' => $order['order_sn'],
            'amount' => $order['pay_price'],
            'subject' => '预注册开通服务',
            'body' => '预注册开通服务 - 订单号: ' . $order['order_sn'],
            'payment_method' => $this->normalizePaymentMethod($payType),
            'mer_id' => $merId,
            'user_id' => $order['uid'] ?? 0,
            'client_ip' => $this->request->ip(),
            'user_agent' => $this->request->header('user-agent', ''),
            'platform' => $this->unifiedPaymentService->detectPlatform($this->request->header()),
            'callback_config' => [
                'notify_url' => $this->buildNotifyUrl($order['order_id']),
                'return_url' => $this->buildReturnUrl($order['order_id']),
                'success_url' => '/pages/pre_register/result?order_id=' . $order['order_id'],
                'cancel_url' => '/pages/pre_register/payment?order_id=' . $order['order_id']
            ],
            'business_config' => [
                'timeout_minutes' => 30,
                'auto_close' => true,
                'allow_retry' => true,
                'max_retry_count' => 3,
                'enable_coupon' => false,
                'enable_points' => false
            ],
            'extra_data' => [
                'order_type' => 'pre_register',
                'is_api_order' => $order['order_source'] == 1,
                'partner_order_id' => $order['partner_order_id'] ?? '',
                'partner_name' => $order['partner_name'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];

        // 小程序特殊配置
        if ($this->isMiniProgram()) {
            $config['mini_program_config'] = $this->buildMiniProgramConfig($payType);
        }

        return $config;
    }

    /**
     * 标准化支付方式 - 使用统一字段映射服务
     */
    private function normalizePaymentMethod($payType)
    {
        try {
            // 使用统一的支付方式映射服务
            return UnifiedPaymentService::mapPaymentMethod($payType);
            
        } catch (\Exception $e) {
            Log::warning('[PreRegister] 支付方法映射异常，使用默认jsapi', [
                'pay_type' => $payType,
                'error' => $e->getMessage()
            ]);
            return 'jsapi'; // 默认返回jsapi方法
        }
    }
    
    /**
     * 获取默认支付方式映射（兜底方案）
     * @deprecated 现在使用统一字段映射服务，此方法保留用于向后兼容
     */
    private function getDefaultPaymentMethodMapping($payType)
    {
        // 兜底使用jsapi方法
        return 'jsapi';
    }

   
    /**
     * 构建小程序配置
     */
    private function buildMiniProgramConfig($payType)
    {
        $config = [
            'enable_native_pay' => true,
            'auto_close_on_success' => true,
            'success_redirect' => '/pages/pre_register/result'
        ];

        // 微信小程序特殊配置
        if ($payType === 'routine' || $payType === 'weixinMini') {
            $config['wechat_config'] = [
                'enable_jsapi' => true,
                'enable_native' => true,
                'scene_info' => [
                    'payer_client_ip' => $this->request->ip(),
                    'device_id' => $this->request->header('device-id', ''),
                    'store_info' => [
                        'id' => 'pre_register',
                        'name' => '预注册服务',
                        'area_code' => '440100',
                        'address' => '在线服务'
                    ]
                ]
            ];
        }

        // 支付宝小程序特殊配置
        if ($payType === 'alipayMini' || $payType === 'alipay_mini') {
            $config['alipay_config'] = [
                'enable_native' => true,
                'product_code' => 'QUICK_MSECURITY_PAY',
                'goods_type' => '0',
                'passback_params' => json_encode([
                    'order_type' => 'pre_register',
                    'platform' => 'alipay_mini'
                ])
            ];
        }

        return $config;
    }

    /**
     * 检测是否为小程序环境
     * @return bool
     */
    private function isMiniProgram(): bool
    {
        $userAgent = $this->request->header('user-agent', '');
        $platform = $this->unifiedPaymentService->detectPlatform($this->request->header());
        
        // 检测微信小程序
        if (strpos($userAgent, 'MicroMessenger') !== false && strpos($userAgent, 'miniProgram') !== false) {
            return true;
        }
        
        // 检测支付宝小程序
        if (strpos($userAgent, 'AlipayClient') !== false) {
            return true;
        }
        
        // 检测uni-app
        if (strpos($userAgent, 'uni-app') !== false) {
            return true;
        }
        
        // 基于平台判断
        return in_array($platform, ['wechat_mini', 'alipay_mini']);
    }

    /**
     * 构建回调URL
     */
    private function buildNotifyUrl($orderId)
    {
        $baseUrl = $this->request->domain();
        return $baseUrl . '/api/pre_register/notify/' . $orderId;
    }

    /**
     * 构建返回URL
     */
    private function buildReturnUrl($orderId)
    {
        $baseUrl = $this->request->domain();
        return $baseUrl . '/pages/pre_register/result?order_id=' . $orderId;
    }

    /**
     * 获取支付方式列表 - 使用统一支付架构
     */
    public function getPayTypes()
    {
        $orderId = $this->request->get('order_id', '') ?: $this->request->param('order_id', '');
        $merId = $this->request->get('mer_id', 0) ?: $this->request->param('mer_id', 0);

        if (empty($orderId)) {
            return $this->fail('订单ID不能为空');
        }

        try {
            // 获取订单信息
            $order = $this->getOrderInfo($orderId);
            if (!$order) {
                return $this->fail('订单不存在');
            }

            // 使用统一支付架构获取支付方式
            $platform = $this->unifiedPaymentService->detectPlatform($this->request->header());
            
            // 添加详细的调试日志
            Log::info('[PreRegister] 获取支付方式 - 详细参数', [
                'order_id' => $orderId,
                'mer_id' => $merId,
                'platform' => $platform,
                'user_agent' => $this->request->header('user-agent', ''),
                'headers' => $this->request->header()
            ]);
            
            $payTypes = $this->unifiedPaymentService->getAvailablePaymentMethods([
                'platform' => $platform,
                'merchant_id' => $merId,
                'user_id' => $order['uid'] ?? 0,
                'amount' => $order['pay_price'] ?? 0,
                'order_type' => 'pre_register'
            ]);

            // 添加更详细的返回数据日志
            Log::info('[PreRegister] 获取支付方式成功 - 详细结果', [
                'order_id' => $orderId,
                'platform' => $platform,
                'pay_types_count' => count($payTypes),
                'pay_types_detail' => $payTypes,
                'unified_architecture' => true
            ]);

            return $this->success([
                'pay_types' => $payTypes,
                'platform' => $platform,
                'order_info' => [
                    'order_id' => $order['order_id'],
                    'pay_price' => $order['pay_price'],
                    'order_sn' => $order['order_sn']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('[PreRegister] 获取支付方式失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->fail('获取支付方式失败: ' . $e->getMessage());
        }
    }

    // 移除重复的支付方式获取逻辑 - 现在使用统一支付架构的 getAvailablePaymentMethods

    /**
     * 支付回调处理 - 统一支付架构
     */
    public function notify()
    {
        $orderId = $this->request->param('order_id', '');
        
        if (empty($orderId)) {
            Log::error('[PreRegister Notify] 订单ID为空');
            return 'fail';
        }

        try {
            // 获取回调数据
            $notifyData = $this->request->getContent();
            
            Log::info('[PreRegister Notify] 收到支付回调', [
                'order_id' => $orderId,
                'notify_data' => $notifyData,
                'headers' => $this->request->header()
            ]);

            // 调用统一支付服务处理回调
            $result = $this->unifiedPaymentService->handleNotify([
                'service_type' => 'pre_register',
                'order_id' => $orderId,
                'notify_data' => $notifyData,
                'headers' => $this->request->header(),
                'callback' => [$this, 'handlePaymentSuccess']
            ]);

            if ($result['success']) {
                Log::info('[PreRegister Notify] 回调处理成功', [
                    'order_id' => $orderId,
                    'result' => $result
                ]);
                return 'success';
            } else {
                Log::error('[PreRegister Notify] 回调处理失败', [
                    'order_id' => $orderId,
                    'error' => $result['message']
                ]);
                return 'fail';
            }

        } catch (\Exception $e) {
            Log::error('[PreRegister Notify] 回调异常', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 'fail';
        }
    }

    /**
     * 支付成功回调处理
     */
    public function handlePaymentSuccess($paymentData)
    {
        $orderId = $paymentData['order_id'];
        
        try {
            Db::startTrans();

            // 获取订单信息
            $order = $this->getOrderInfo($orderId);
            if (!$order) {
                throw new \Exception('订单不存在');
            }

            if ($order['paid'] == 1) {
                Log::info('[PreRegister] 订单已支付，跳过处理', ['order_id' => $orderId]);
                Db::commit();
                return true;
            }

            // 更新订单状态
            $this->updateOrderStatus($orderId, $paymentData);

            // 处理预注册业务逻辑
            $this->processPreRegisterBusiness($order, $paymentData);

            // 发送通知
            $this->sendPaymentNotification($order, $paymentData);

            Db::commit();

            Log::info('[PreRegister] 支付成功处理完成', [
                'order_id' => $orderId,
                'trade_no' => $paymentData['trade_no'] ?? ''
            ]);

            return true;

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('[PreRegister] 支付成功处理失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 查询订单状态
     */
    public function queryStatus()
    {
        $orderId = $this->request->get('order_id', '') ?: $this->request->param('order_id', '');
        
        if (empty($orderId)) {
            return $this->fail('订单ID不能为空');
        }

        try {
            $order = $this->getOrderInfo($orderId);
            if (!$order) {
                return $this->fail('订单不存在');
            }

            return $this->success([
                'order_id' => $order['order_id'],
                'order_sn' => $order['order_sn'],
                'paid' => (int)$order['paid'],
                'pay_time' => $order['pay_time'] ?? '',
                'status' => (int)$order['status'],
                'pay_price' => (float)$order['pay_price'],
                'create_time' => $order['create_time'] ?? '',
                'mer_id' => $order['mer_id'] ?? 0
            ]);

        } catch (\Exception $e) {
            Log::error('[PreRegister] 查询订单状态失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->fail('查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取订单详情
     */
    public function getOrderDetail()
    {
        $orderId = $this->request->param('order_id', '');
        
        if (empty($orderId)) {
            return $this->fail('订单ID不能为空');
        }

        try {
            $order = $this->getOrderInfo($orderId, true);
            if (!$order) {
                return $this->fail('订单不存在');
            }

            return $this->success($order);

        } catch (\Exception $e) {
            Log::error('[PreRegister] 获取订单详情失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return $this->fail('获取订单详情失败');
        }
    }

    /**
     * 商品匹配接口 - 使用trait中的算法实现
     * 
     * @return mixed
     */
    public function matchProduct()
    {
        try {
            // 1. 获取并验证输入参数 (GET方式)
            $amount = (float)$this->request->get('amount', 0);
            $keyword = $this->request->get('keyword', '');
            $category = $this->request->get('category', '');
            

            
            // 使用trait中的金额验证方法
            $this->validateAmount($amount);
            
            // 2. 系统自动选择最合适的商户
            $merId = $this->selectBestMerchant($amount, $keyword, $category);
            if (!$merId) {
                return $this->fail('暂无可用商户');
            }
            
            // 3. 获取商品数据
            $products = $this->getMatchProducts($merId, $amount, $keyword, $category);
            
            if (empty($products)) {
                return $this->fail('该商户暂无合适的商品');
            }
            
            // 4. 调用trait中的优化匹配算法
            $matchStats = [];
            $matchResult = $this->productMatchServiceOptimized($products, $amount, $matchStats);
            
            if (!$matchResult) {
                return $this->fail('未找到合适的商品组合');
            }
            
            // 5. 格式化返回结果 - 按照old版本格式
            $merchant = $this->getMerchantInfo($merId);
            $result = $this->formatMatchResult($matchResult, $merId, $merchant, $amount);
            
           
            
            return $this->success($result);
            
        } catch (ValidateException $e) {
            return $this->fail($e->getMessage());
        } catch (\Exception $e) {
            Log::error('[PreRegister] 商品匹配异常: ' . $e->getMessage());
            return $this->fail('商品匹配失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建预注册订单
     * POST /api/pre_register/create_order
     * 
     * @return mixed
     */
    public function createOrder()
    {
        try {
            // 1. 获取并验证输入参数
            $amount = (float)$this->request->post('amount', 0);
            $cartInfo = $this->request->post('cart_info', []);
            $userInfo = $this->request->post('user_info', []);
            $merId = (int)$this->request->post('mer_id', 0);
            $remark = $this->request->post('remark', '');
            
            // 验证基础参数
            if ($amount <= 0) {
                return $this->fail('订单金额必须大于0');
            }
            
            if (empty($cartInfo)) {
                return $this->fail('购物车信息不能为空');
            }
            
            // 2. 验证订单数据
            $validationResult = $this->validateOrderData($cartInfo, $merId, $amount);
            if (!$validationResult['success']) {
                return $this->fail($validationResult['message']);
            }
            
            // 3. 生成订单号
            $orderSn = $this->generateOrderSn();
            
            // 4. 构建订单数据
            $orderData = $this->buildOrderData($orderSn, $merId, $amount, $cartInfo, $userInfo, $remark);
            
            // 5. 创建订单事务
            $result = $this->createOrderTransaction($orderData);
            
            if (!$result) {
                return $this->fail('订单创建失败');
            }
            
            // 6. 记录日志
            Log::info('[PreRegister] 订单创建成功', [
                'order_id' => $result['order_id'],
                'order_sn' => $orderSn,
                'amount' => $amount,
                'mer_id' => $merId
            ]);

            return $this->success([
                'order_id' => $result['order_id'],
                'order_sn' => $orderSn,
                'pay_price' => $result['pay_price'],
                'total_price' => $result['pay_price'], // 前端兼容字段
                'total_num' => $result['total_num'],
                'mer_name' => $result['mer_name'],
                'products' => $result['products'],
                'create_time' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Exception $e) {
            Log::error('[PreRegister] 订单创建异常: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->fail('订单创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成预注册订单号
     * 
     * @return string
     */
    private function generateOrderSn(): string
    {
        do {
            // 格式：PR + 年月日 + 6位随机数
            $orderSn = 'PR' . date('Ymd') . mt_rand(100000, 999999);
            
            // 检查订单号是否已存在
            $exists = Db::name('store_order')
                ->where('order_sn', $orderSn)
                ->value('order_id');
                
        } while ($exists);
        
        return $orderSn;
    }

    /**
     * 验证订单数据
     * 
     * @param array $cartInfo 购物车信息
     * @param int $merId 商户ID
     * @param float $expectedAmount 期望金额
     * @return array
     */
    private function validateOrderData(array $cartInfo, int $merId, float $expectedAmount): array
    {
        try {
            $totalAmount = 0;
            $totalNum = 0;
            
            foreach ($cartInfo as $item) {
                $productId = (int)($item['product_id'] ?? 0);
                $quantity = (int)($item['quantity'] ?? 0);
                
                if ($productId <= 0 || $quantity <= 0) {
                    return [
                        'success' => false,
                        'message' => '商品信息不完整'
                    ];
                }
                
                // 查询商品信息
                $product = Db::name('store_product')
                    ->where('product_id', $productId)
                    ->where('mer_id', $merId)
                    ->where('is_show', 1)
                    ->where('is_del', 0)
                    ->where('status', 1)
                    ->field('product_id,store_name,price,stock,mer_id')
                    ->find();
                
                if (!$product) {
                    return [
                        'success' => false,
                        'message' => '商品不存在或已下架'
                    ];
                }
                
                // 检查库存
                if ($product['stock'] < $quantity) {
                    return [
                        'success' => false,
                        'message' => "商品「{$product['store_name']}」库存不足"
                    ];
                }
                
                $totalAmount += $product['price'] * $quantity;
                $totalNum += $quantity;
            }
            
            // 验证金额
            if (abs($totalAmount - $expectedAmount) > 0.01) {
                return [
                    'success' => false,
                    'message' => '订单金额不匹配'
                ];
            }
            
            return [
                'success' => true,
                'total_amount' => $totalAmount,
                'total_num' => $totalNum
            ];
            
        } catch (\Exception $e) {
            Log::error('[PreRegister] 订单验证失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '订单验证失败'
            ];
        }
    }

    /**
     * 构建订单数据
     * 
     * @param string $orderSn 订单号
     * @param int $merId 商户ID
     * @param float $amount 订单金额
     * @param array $cartInfo 购物车信息
     * @param array $userInfo 用户信息
     * @param string $remark 备注
     * @return array
     */
    private function buildOrderData(string $orderSn, int $merId, float $amount, array $cartInfo, array $userInfo, string $remark): array
    {
        // 获取商户信息
        $merchant = $this->getMerchantInfo($merId);
        $merName = $merchant->mer_name ?? '未知商户';
        
        // 构建基础订单数据
        $orderData = [
            'order_sn' => $orderSn,
            'uid' => 0, // 预注册订单用户ID为0
            'spread_uid' => 0,
            'top_uid' => 0,
            'real_name' => $userInfo['real_name'] ?? '预注册用户',
            'user_phone' => $userInfo['phone'] ?? '',
            'user_address' => $userInfo['address'] ?? '',
            'cart_id' => json_encode(array_column($cartInfo, 'product_id')),
            'total_num' => 0,
            'total_price' => $amount,
            'total_postage' => 0,
            'pay_price' => $amount,
            'pay_postage' => 0,
            'is_selfbuy' => 0,
            'extension_one' => 0,
            'extension_two' => 0,
            'commission_rate' => 0,
            'integral' => 0,
            'integral_price' => 0,
            'give_integral' => 0,
            'coupon_id' => '',
            'coupon_price' => 0,
            'platform_coupon_price' => 0,
            'svip_discount' => 0,
            'order_type' => 0, // 普通订单类型
             'paid' => 0,
             'pay_type' => 0,
             'status' => 0,
             'delivery_type' => 'express',
             'is_virtual' => 0, // 实体商品
            'mark' => $remark,
            'remark' => '',
            'admin_mark' => '预注册订单',
            'activity_type' => 0,
            'order_extend' => json_encode([
                'order_type' => 'pre_register',
                'created_by' => 'pre_register_api',
                'mer_name' => $merName
            ]),
                'mer_id' => $merId,
            'reconciliation_id' => 0,
            'cost' => 0,
            'is_del' => 0,
            'is_system_del' => 0,
            'verify_status' => 0,
            'refund_switch' => 1,
            'task_id' => '',
            'is_behalf' => 0,
            'order_source' => 1, // API来源
            'partner_id' => null,
            'partner_order_id' => null,
            'notify_url' => null,
            'return_url' => null,
            'create_time' => date('Y-m-d H:i:s')
        ];
        
        // 构建订单商品数据
        $orderProducts = [];
        $totalNum = 0;
        
        foreach ($cartInfo as $item) {
            $productId = (int)$item['product_id'];
            $quantity = (int)$item['quantity'];
            
            // 获取商品详细信息
            $product = Db::name('store_product')
                ->where('product_id', $productId)
                ->field('product_id,store_name,price,stock,image,mer_id')
                ->find();
            
            if ($product) {
                $orderProducts[] = [
                    'uid' => 0,
                    'cart_id' => 0,
                    'product_id' => $productId,
                    'extension_one' => 0,
                    'extension_two' => 0,
                    'integral' => 0,
                    'integral_price' => 0,
                    'integral_total' => 0,
                    'coupon_price' => 0,
                    'platform_coupon_price' => 0,
                    'svip_discount' => 0,
                    'postage_price' => 0,
                    'product_sku' => '',
                    'is_refund' => 0,
                    'product_num' => $quantity,
                    'product_type' => 0,
                    'activity_id' => 0,
                    'refund_num' => 0,
                    'is_reply' => 0,
                    'cost' => 0,
                    'product_price' => $product['price'],
                    'total_price' => $product['price'] * $quantity,
                    'cart_info' => json_encode([
                        'product_id' => $productId,
                        'product' => [
                            'product_id' => $productId,
                            'store_name' => $product['store_name'],
                            'image' => $product['image'],
                            'price' => $product['price']
                        ],
                        'productAttr' => [
                            'sku' => '预注册商品',
                            'price' => $product['price'],
                            'image' => $product['image']
                        ],
                        'cart_num' => $quantity,
                        'quantity' => $quantity,
                        'order_type' => 'pre_register'
                    ]),
                    'refund_switch' => 1,
                    'create_time' => date('Y-m-d H:i:s')
                ];
                
                $totalNum += $quantity;
            }
        }
        
        $orderData['total_num'] = $totalNum;
        
        return [
            'order' => $orderData,
            'products' => $orderProducts
        ];
    }

    /**
     * 创建订单事务
     * 
     * @param array $orderData 订单数据
     * @return array|false
     */
    private function createOrderTransaction(array $orderData)
    {
        try {
            Db::startTrans();
            
            // 1. 创建主订单
            $orderId = Db::name('store_order')->insertGetId($orderData['order']);
            
            if (!$orderId) {
                throw new \Exception('创建主订单失败');
            }
            
            // 2. 创建订单商品
            foreach ($orderData['products'] as &$product) {
                $product['order_id'] = $orderId;
            }
            
            $productResult = Db::name('store_order_product')->insertAll($orderData['products']);
            
            if (!$productResult) {
                throw new \Exception('创建订单商品失败');
            }
            
            // 3. 减少商品库存
            foreach ($orderData['products'] as $product) {
                $updateResult = Db::name('store_product')
                    ->where('product_id', $product['product_id'])
                    ->dec('stock', $product['product_num'])
                    ->update();
                    
                if (!$updateResult) {
                    Log::warning('[PreRegister] 减少库存失败', [
                        'product_id' => $product['product_id'],
                        'quantity' => $product['product_num']
                    ]);
                }
            }
            
            Db::commit();
            
            // 4. 获取商户信息
            $merchant = $this->getMerchantInfo($orderData['order']['mer_id']);
            
            // 5. 格式化商品信息
            $formattedProducts = [];
            foreach ($orderData['products'] as $product) {
                $cartInfo = json_decode($product['cart_info'], true);
                $formattedProducts[] = [
                    'product_id' => $product['product_id'],
                    'store_name' => $cartInfo['store_name'],
                    'image' => $cartInfo['image'],
                    'price' => $product['product_price'],
                    'quantity' => $product['product_num'],
                    'total_price' => $product['total_price']
                ];
            }
            
            return [
                'order_id' => $orderId,
                'pay_price' => $orderData['order']['pay_price'],
                'total_num' => $orderData['order']['total_num'],
                'mer_name' => $merchant->mer_name ?? '未知商户',
                'products' => $formattedProducts
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('[PreRegister] 订单事务失败: ' . $e->getMessage(), [
                'order_data' => $orderData,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 智能选择最佳商户
     * 根据目标金额、关键词等条件选择最合适的商户
     * 
     * @param float $amount 目标金额
     * @param string $keyword 关键词
     * @param string $category 分类
     * @return int|null 商户ID
     */
    private function selectBestMerchant(float $amount, string $keyword = '', string $category = ''): ?int
    {
        try {
           

            $query = Db::name('store_product')
             ->alias('p')
             ->join('merchant m', 'p.mer_id = m.mer_id')
             ->where('p.is_show', 1)
             ->where('p.is_del', 0)
             ->where('p.status', 1)
             ->where('p.stock', '>', 0)
             ->where('m.is_del', 0)
             ->where('m.status', 1)
             ->field('m.mer_id,m.mer_name')
             ->group('m.mer_id'); // 按商户ID分组去重

             if(!empty($keyword)){
                 $query->where('p.store_name', 'like', '%' . $keyword . '%');
             }
             $merchants = $query->select()->toArray();

                
            if (empty($merchants)) {
                return null;
            }
            
            $bestMerchant = null;
            $bestScore = 0;
            
            // 为每个商户计算适合度评分
            foreach ($merchants as $merchant) {
                $score = $this->calculateMerchantScore($merchant['mer_id'], $amount, $keyword, $category);
                
                if ($score > $bestScore) {
                    $bestScore = $score;
                    $bestMerchant = $merchant['mer_id'];
                }
            }
            
            return $bestMerchant ?: $merchants[0]['mer_id']; // 兜底返回第一个商户
            
        } catch (\Exception $e) {
            Log::error('[PreRegister] 智能选择商户失败: ' . $e->getMessage());
            
            // 兜底逻辑：返回第一个可用商户
            try {
                return Db::name('merchant')
                    ->where('is_del', 0)
                    ->where('status', 1)
                    ->value('mer_id') ?: 1;
            } catch (\Exception $e2) {
                return 1;
            }
        }
    }
    
    /**
     * 计算商户适合度评分
     * 
     * @param int $merId 商户ID
     * @param float $amount 目标金额
     * @param string $keyword 关键词
     * @param string $category 分类
     * @return float 评分
     */
    private function calculateMerchantScore(int $merId, float $amount, string $keyword = '', string $category = ''): float
    {
        $score = 0;
        
        try {
            // 基础查询条件
            $where = [
                ['mer_id', '=', $merId],
                ['is_show', '=', 1],
                ['is_del', '=', 0],
                ['status', '=', 1],
                ['stock', '>', 0]
            ];
            
            // 添加关键词条件
            if (!empty($keyword)) {
                $where[] = ['store_name', 'like', '%' . $keyword . '%'];
            }
            
            // 获取该商户的商品信息
            $products = Db::name('store_product')
                ->where($where)
                ->field('price,stock,sales')
                ->select()
                ->toArray();
                
            if (empty($products)) {
                return 0;
            }
            
            // 1. 商品数量评分 (30%)
            $productCountScore = min(count($products) / 10, 3) * 10;
            
            // 2. 价格匹配度评分 (40%)
            $priceMatchScore = 0;
            $priceRange = $this->calculatePriceRange($amount);
            
            foreach ($products as $product) {
                $price = (float)$product['price'];
                if ($price >= $priceRange['min'] && $price <= $priceRange['max']) {
                    $priceMatchScore += 5;
                    
                    // 如果价格接近目标金额，额外加分
                    if (abs($price - $amount) / $amount < 0.1) {
                        $priceMatchScore += 10;
                    }
                }
            }
            $priceMatchScore = min($priceMatchScore, 40);
            
            // 3. 商品质量评分 (30%)
            $qualityScore = 0;
            foreach ($products as $product) {
                $sales = (int)$product['sales'];
                $stock = (int)$product['stock'];
                
                // 销量评分
                if ($sales > 10) $qualityScore += 2;
                elseif ($sales > 0) $qualityScore += 1;
                
                // 库存评分
                if ($stock > 50) $qualityScore += 2;
                elseif ($stock > 10) $qualityScore += 1;
            }
            $qualityScore = min($qualityScore, 30);
            
            $score = $productCountScore + $priceMatchScore + $qualityScore;
            
        } catch (\Exception $e) {
            Log::error('[PreRegister] 计算商户评分失败: ' . $e->getMessage(), ['mer_id' => $merId]);
            $score = 0;
        }
        
        return $score;
    }
    
    /**
     * 计算价格范围
     * 
     * @param float $amount 目标金额
     * @return array 价格范围
     */
    private function calculatePriceRange(float $amount): array
    {
        return [
            'min' => $amount * 0.1,  // 最小价格：目标金额的10%
            'max' => $amount * 1.2   // 最大价格：目标金额的120%
        ];
    }

    /**
     * 获取匹配商品
     * 
     * @param int $merId 商户ID  
     * @param float $amount 目标金额
     * @param string $keyword 关键词
     * @param string $category 分类
     * @return array 商品列表
     */
    private function getMatchProducts(int $merId, float $amount, string $keyword = '', string $category = ''): array
    {
        try {
            // 构建查询条件
            $where = [
                ['mer_id', '=', $merId],
                ['is_show', '=', 1],
                ['is_del', '=', 0],
                ['status', '=', 1],
                ['stock', '>', 0],
                ['price', '>=', 1] // 过滤价格过低的商品
            ];
            
            // 添加关键词搜索
        if (!empty($keyword)) {
            $where[] = ['store_name', 'like', '%' . $keyword . '%'];
        }

            // 查询商品 - 包含算法需要的所有字段
            $products = Db::name('store_product')
            ->where($where)
                ->field('product_id,store_name,image,price,stock,sales,mer_id,cate_id,status,is_del,is_show')
                ->order('sales desc, price asc')
                ->limit(50)
            ->select()
            ->toArray();
            
            return $products;
            
        } catch (\Exception $e) {
            Log::error('[PreRegister] 获取商品失败: ' . $e->getMessage());
            return [];
        }
    }



    /**
     * 获取商户信息
     * 
     * @param int $merId 商户ID
     * @return object|null 商户信息
     */
    private function getMerchantInfo(int $merId): ?object
    {
        try {
            $merchant = Db::name('merchant')
                ->where('mer_id', $merId)
                ->field('mer_id,mer_name')
                ->find();
                
            return $merchant ? (object)$merchant : null;
        } catch (\Exception $e) {
            Log::error('[PreRegister] 获取商户信息失败: ' . $e->getMessage(), ['mer_id' => $merId]);
            return null;
        }
    }

    /**
     * 格式化匹配结果 - 按照old版本格式
     * 
     * @param array $matchResult 匹配结果
     * @param int $merId 商户ID
     * @param object|null $merchant 商户信息
     * @param float $targetAmount 目标金额
     * @return array 格式化后的结果
     */
    private function formatMatchResult(array $matchResult, int $merId, ?object $merchant, float $targetAmount): array
    {
        $formattedProducts = [];
        $totalQuantity = 0;
        
        foreach ($matchResult['products'] as $item) {
            $product = $item['product'];
            $quantity = $item['quantity'];
            
            // 按照前端期望的嵌套结构：包含product对象和quantity
            $formattedProducts[] = [
                'product' => [
                'product_id' => $product['product_id'],
                'store_name' => $product['store_name'],
                    'image' => $product['image'] ?? '',
                    'price' => (float)$product['price'],
                    'stock' => (int)($product['stock'] ?? 0),
                    'sales' => (int)($product['sales'] ?? 0)
                ],
                'quantity' => $quantity
            ];
            
            $totalQuantity += $quantity;
        }

        return [
            'mer_id' => $merId,
            'mer_name' => $merchant->mer_name ?? '未知商户',
            'target_amount' => $targetAmount,
            'matched_amount' => $matchResult['total_price'],
            'total_price' => $matchResult['total_price'], // 前端期望的字段
            'difference' => abs($matchResult['total_price'] - $targetAmount),
            'match_rate' => round(min($matchResult['total_price'], $targetAmount) / max($matchResult['total_price'], $targetAmount) * 100, 2),
            'total_quantity' => $totalQuantity,
            'products' => $formattedProducts,
            'cart_info' => array_map(function($item) {
                return [
                    'product_id' => $item['product']['product_id'],
                    'quantity' => $item['quantity']
                ];
            }, $matchResult['products'])
        ];
    }

    /**
     * 获取订单信息
     */
    private function getOrderInfo($orderId, $withDetail = false)
    {
        try {
        $query = Db::name('store_order')->where('order_id', $orderId);
        
        if ($withDetail) {
                // 包含详细信息 - 获取基础订单信息
            $order = $query->find();
                
                // 获取订单商品信息
                if ($order) {
                    $orderProducts = Db::name('store_order_product')
                        ->where('order_id', $orderId)
                        ->field('product_id,product_num,product_price,total_price,cart_info')
                        ->select()
                        ->toArray();
                        
                    $order['cart_info'] = [];
                    foreach ($orderProducts as $product) {
                        if ($product['cart_info']) {
                            $cartInfo = json_decode($product['cart_info'], true);
                            $order['cart_info'][] = [
                                'id' => $product['product_id'],
                                'product_id' => $product['product_id'],
                                'quantity' => $product['product_num'],
                                'product' => [
                                    'product_id' => $product['product_id'],
                                    'store_name' => $cartInfo['store_name'] ?? '',
                                    'image' => $cartInfo['image'] ?? '',
                                    'price' => $product['product_price']
                                ]
                            ];
                        }
                    }
            }
        } else {
                // 基础信息 - 去除不存在的字段
                $order = $query->field('order_id,order_sn,uid,pay_price,paid,status,is_del,pay_time,order_source,partner_order_id,return_url,mer_id,create_time')->find();
        }

        return $order;
        } catch (\Exception $e) {
            Log::error('[PreRegister] 获取订单信息失败: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'withDetail' => $withDetail
            ]);
            return null;
        }
    }

    /**
     * 更新订单状态
     */
    private function updateOrderStatus($orderId, $paymentData)
    {
        $updateData = [
            'paid' => 1,
            'pay_time' => date('Y-m-d H:i:s'),
            'status' => 1,
            'trade_no' => $paymentData['trade_no'] ?? '',
            'payment_method' => $paymentData['payment_method'] ?? '',
            'update_time' => date('Y-m-d H:i:s')
        ];

        return Db::name('store_order')->where('order_id', $orderId)->update($updateData);
    }

    /**
     * 处理预注册业务逻辑
     */
    private function processPreRegisterBusiness($order, $paymentData)
    {
        // 创建用户账号
        if ($order['uid'] == 0) {
            $userId = $this->createUserAccount($order);
            if ($userId) {
                Db::name('store_order')->where('order_id', $order['order_id'])->update(['uid' => $userId]);
            }
        }

        // 激活服务
        $this->activateService($order);

        // 发放优惠券等
        $this->grantBenefits($order);
    }

    /**
     * 创建用户账号
     */
    private function createUserAccount($order)
    {
        // 实现用户账号创建逻辑
        return 0;
    }

    /**
     * 激活服务
     */
    private function activateService($order)
    {
        // 实现服务激活逻辑
    }

    /**
     * 发放福利
     */
    private function grantBenefits($order)
    {
        // 实现福利发放逻辑
    }

    /**
     * 发送支付通知
     */
    private function sendPaymentNotification($order, $paymentData)
    {
        // 实现通知发送逻辑
    }

    // 移除重复的余额相关逻辑 - 现在使用统一支付架构处理

    /**
     * 验证订单
     */
    private function validateOrder($orderId)
    {
        try {
            $order = $this->getOrderInfo($orderId);
            
            if (!$order) {
                return false;
            }
            
            // 检查订单状态
            if ($order['is_del'] == 1) {
                return false;
            }
            
            return $order;
        } catch (\Exception $e) {
            Log::error('[PreRegister] 验证订单失败: ' . $e->getMessage(), ['order_id' => $orderId]);
            return false;
        }
    }

    /**
     * 处理支付请求
     * POST /api/pre_register/pay/{orderId}
     */
    public function pay(Request $request, $orderId)
    {
        try {
            Log::info('[PreRegister Pay] 开始处理支付请求', [
                'order_id' => $orderId,
                'request_data' => $request->post()
            ]);

            // 获取支付类型
            $payType = $request->post('pay_type', '');
            $merId = $request->post('mer_id', 0);

            if (empty($payType)) {
                throw new \Exception('支付方式不能为空');
            }

            // 验证订单
            $order = $this->validateOrder($orderId);
            if (!$order) {
                throw new \Exception('订单不存在或已失效');
            }

            if ($order['paid'] == 1) {
                throw new \Exception('订单已支付，请勿重复支付');
            }

            // 检测平台 - 使用统一支付架构
            $platform = $this->unifiedPaymentService->detectPlatform($this->request->header());

            // 构建统一支付配置 - 使用统一字段映射
            $unifiedConfig = [
                'mer_id' => $merId,                       // 商户ID
                'combine_pay' => false,                   // 预注册不使用组合支付
                'alipay_service_mode' => function_exists('systemConfig') ? systemConfig('alipay_service_mode') : false,
                'return_url' => '/pages/pre_register/result?order_id=' . $order['order_id'],
                'affect' => 'pre_register'                // 影响类型
            ];

            // 构建订单数据 - 标准格式
            $orderData = [
                'order_no' => $order['order_sn'],         // 订单号
                'amount' => $order['pay_price'],          // 支付金额
                'subject' => '预注册开通服务',             // 订单主题
                'body' => '预注册开通服务支付',            // 订单描述
                'return_url' => '/pages/pre_register/result?order_id=' . $order['order_id'],
                'group_order_id' => $order['order_id'],   // 订单ID
                'uid' => $order['uid'] ?? 0,              // 用户ID
                'mer_id' => $merId,                       // 商户ID
                'order_type' => 'pre_register',           // 订单类型
                'platform' => $platform                   // 平台标识
            ];

            // 使用统一支付服务 - 让UnifiedPaymentService自动处理支付类型映射
            $unifiedPayment = app(UnifiedPaymentService::class);
            $paymentResult = $unifiedPayment->createPayment($payType, $orderData, $unifiedConfig);

           
            // 返回统一格式的支付结果
            return json([
                'status' => 200,
                'message' => 'success',
                'data' => $paymentResult
            ]);

        } catch (\Exception $e) {
            Log::error('[PreRegister Pay] 支付失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return json([
                'status' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ]);
        }
    }
}
