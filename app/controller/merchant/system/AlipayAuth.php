<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\merchant\system;

use crmeb\basic\BaseController;
use crmeb\services\alipay\AlipayAuthService;
use think\App;

/**
 * 支付宝商户授权管理
 * Class AlipayAuth
 * @package app\controller\merchant\system
 */
class AlipayAuth extends BaseController
{
    /**
     * @var AlipayAuthService
     */
    protected $authService;

    /**
     * AlipayAuth constructor.
     * @param App $app
     * @param AlipayAuthService $authService
     */
    public function __construct(App $app, AlipayAuthService $authService)
    {
        parent::__construct($app);
        $this->authService = $authService;
    }

    /**
     * 获取授权状态
     * @return mixed
     */
    public function getAuthStatus()
    {
        try {
            $status = $this->authService->getAuthStatus($this->request->merId());
            return app('json')->success($status);
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 生成授权链接
     * @return mixed
     */
    public function generateAuthUrl()
    {
        try {
            $url = $this->authService->generateAuthUrl($this->request->merId(), AlipayAuthService::SOURCE_MERCHANT);
            return app('json')->success(['url' => $url]);
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 刷新授权令牌
     * @return mixed
     */
    public function refreshAuthToken()
    {
        try {
            $this->authService->refreshAuthToken($this->request->merId());
            return app('json')->success('刷新授权令牌成功');
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 取消授权
     * @return mixed
     */
    public function cancelAuth()
    {
        try {
            $this->authService->cancelAuth($this->request->merId());
            return app('json')->success('取消授权成功');
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 授权回调处理
     * @return mixed
     */
    public function authCallback()
    {
        $auth_code = $this->request->get('app_auth_code', '');
        $state = $this->request->get('state', '');

        if (empty($auth_code)) {
            return $this->failed('未获取到授权码');
        }

        try {
            // 获取商户ID
            $merId = (int)$state;

            // 处理授权回调
            $result = $this->authService->handleAuthCallback($auth_code, $merId, AlipayAuthService::SOURCE_MERCHANT);

            return $this->success($result['message'], $result['data'], '/setting/alipayAuth');
        } catch (\Exception $e) {
            return $this->failed('授权失败：' . $e->getMessage());
        }
    }
}
