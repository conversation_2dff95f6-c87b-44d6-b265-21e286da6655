<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\merchant\system;

use app\common\repositories\merchant\MerchantAlipayAuthRepository;
use crmeb\basic\BaseController;
use think\App;
use think\facade\Log;

/**
 * 支付宝商户授权管理
 * Class AlipayAuth
 * @package app\controller\merchant\system
 */
class AlipayAuth extends BaseController
{
    /**
     * @var MerchantAlipayAuthRepository
     */
    protected $repository;

    /**
     * AlipayAuth constructor.
     * @param App $app
     * @param MerchantAlipayAuthRepository $repository
     */
    public function __construct(App $app, MerchantAlipayAuthRepository $repository)
    {
        parent::__construct($app);
        $this->repository = $repository;
    }

    /**
     * 获取授权状态
     * @return mixed
     */
    public function getAuthStatus()
    {
        try {
            $status = $this->repository->checkAuthStatus($this->request->merId());
            return app('json')->success($status);
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 生成授权链接
     * @return mixed
     */
    public function generateAuthUrl()
    {
        try {
            $url = $this->repository->generateAuthUrl($this->request->merId(), 'merchant');
            return app('json')->success(['url' => $url]);
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 刷新授权令牌
     * @return mixed
     */
    public function refreshAuthToken()
    {
        try {
            $this->repository->refreshAuthToken($this->request->merId());
            return app('json')->success('刷新授权令牌成功');
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 取消授权
     * @return mixed
     */
    public function cancelAuth()
    {
        try {
            $this->repository->cancelAuth($this->request->merId());
            return app('json')->success('取消授权成功');
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }

    /**
     * 授权回调处理
     * @return mixed
     */
    public function authCallback()
    {
        $app_id = systemConfig('alipay_service_app_id');
        $private_key = systemConfig('alipay_service_private_key');

        if (empty($app_id) || empty($private_key)) {
            Log::error('支付宝授权回调失败：服务商配置不完整');
            return $this->failed('服务商配置不完整');
        }

        $auth_code = $this->request->get('app_auth_code', '');
        $state = $this->request->get('state', '');

        if (empty($auth_code)) {
            Log::error('支付宝授权回调失败：未获取到授权码');
            return $this->failed('未获取到授权码');
        }

        try {
            // 获取商户ID
            $merId = (int)$state;
            if (!$merId) {
                throw new ValidateException('无效的商户ID');
            }

            // 调用支付宝服务获取授权令牌
            $alipayService = new \crmeb\services\alipay\AlipayService([
                'app_id' => $app_id,
                'private_key' => $private_key
            ]);

            $result = $alipayService->getAppAuthToken($app_id, $private_key, $auth_code);

            // 保存授权信息
            $this->repository->saveAuthInfo($merId, $result);

            // 返回成功页面或跳转
            return $this->success('授权成功', [], '/setting/alipayAuth');
        } catch (\Exception $e) {
            Log::error('支付宝授权回调处理失败：' . $e->getMessage());
            return $this->failed('授权失败：' . $e->getMessage());
        }
    }
}
