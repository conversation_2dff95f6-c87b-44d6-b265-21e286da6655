<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace crmeb\services\alipay;

use app\common\repositories\merchant\MerchantAlipayAuthRepository;
use app\common\repositories\system\merchant\SystemMerchantRepository;
use crmeb\exceptions\ValidateException;
use think\facade\Log;

/**
 * 支付宝授权服务
 * 统一处理系统后台和商户后台的支付宝授权逻辑
 * Class AlipayAuthService
 * @package crmeb\services\alipay
 */
class AlipayAuthService
{
    /**
     * @var AlipayService
     */
    protected $alipayService;

    /**
     * @var MerchantAlipayAuthRepository
     */
    protected $authRepository;

    /**
     * @var SystemMerchantRepository
     */
    protected $merchantRepository;

    /**
     * 授权来源类型
     */
    const SOURCE_ADMIN = 'admin';      // 系统后台
    const SOURCE_MERCHANT = 'merchant'; // 商户后台

    /**
     * AlipayAuthService constructor.
     */
    public function __construct()
    {
        $this->authRepository = app()->make(MerchantAlipayAuthRepository::class);
        $this->merchantRepository = app()->make(SystemMerchantRepository::class);
    }

    /**
     * 获取支付宝服务实例
     * @return AlipayService
     * @throws ValidateException
     */
    protected function getAlipayService(): AlipayService
    {
        if (!$this->alipayService) {
            $config = $this->getServiceConfig();
            $this->alipayService = new AlipayService($config);
        }
        return $this->alipayService;
    }

    /**
     * 获取服务商配置
     * @return array
     * @throws ValidateException
     */
    protected function getServiceConfig(): array
    {
        $app_id = systemConfig('alipay_service_app_id');
        $private_key = systemConfig('alipay_service_private_key');
        $public_key = systemConfig('alipay_service_public_key');

        if (empty($app_id) || empty($private_key)) {
            throw new ValidateException('支付宝服务商配置不完整');
        }

        return [
            'app_id' => $app_id,
            'private_key' => $private_key,
            'public_key' => $public_key
        ];
    }

    /**
     * 获取授权状态
     * @param int $merId 商户ID
     * @return array
     */
    public function getAuthStatus(int $merId): array
    {
        try {
            return $this->authRepository->checkAuthStatus($merId);
        } catch (\Exception $e) {
            Log::error('获取支付宝授权状态失败：' . $e->getMessage(), ['mer_id' => $merId]);
            return [
                'is_auth' => false,
                'message' => '获取授权状态失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成授权链接
     * @param int $merId 商户ID
     * @param string $source 来源：admin-系统后台，merchant-商户后台
     * @return string
     * @throws ValidateException
     */
    public function generateAuthUrl(int $merId, string $source = self::SOURCE_ADMIN): string
    {
        // 验证商户是否存在
        $merchant = $this->merchantRepository->get($merId);
        if (!$merchant) {
            throw new ValidateException('商户不存在');
        }

        // 获取回调地址
        $redirect_uri = $this->getRedirectUri($source);
        
        try {
            $alipayService = $this->getAlipayService();
            $url = $alipayService->generateAuthUrl($redirect_uri, (string)$merId);
            
            Log::info('生成支付宝授权链接成功', [
                'mer_id' => $merId,
                'source' => $source,
                'redirect_uri' => $redirect_uri,
                'url' => $url
            ]);
            
            return $url;
        } catch (\Exception $e) {
            Log::error('生成支付宝授权链接失败：' . $e->getMessage(), [
                'mer_id' => $merId,
                'source' => $source
            ]);
            throw new ValidateException('生成授权链接失败：' . $e->getMessage());
        }
    }

    /**
     * 获取回调地址
     * @param string $source 来源
     * @return string
     * @throws ValidateException
     */
    protected function getRedirectUri(string $source): string
    {
        if ($source === self::SOURCE_MERCHANT) {
            // 商户后台回调地址
            $redirect_uri = systemConfig('alipay_merchant_auth_redirect_uri');
            if (empty($redirect_uri)) {
                // 如果没有配置商户专用回调地址，使用默认格式
                $site_url = rtrim(systemConfig('site_url'), '/');
                $redirect_uri = $site_url . '/merchant/alipay/auth/callback';
            }
        } else {
            // 系统后台回调地址
            $redirect_uri = systemConfig('alipay_auth_redirect_uri');
        }

        if (empty($redirect_uri)) {
            throw new ValidateException('未配置授权回调地址');
        }

        return $redirect_uri;
    }

    /**
     * 处理授权回调
     * @param string $auth_code 授权码
     * @param int $merId 商户ID
     * @param string $source 来源
     * @return array
     * @throws ValidateException
     */
    public function handleAuthCallback(string $auth_code, int $merId, string $source = self::SOURCE_ADMIN): array
    {
        if (empty($auth_code)) {
            throw new ValidateException('授权码不能为空');
        }

        if (!$merId) {
            throw new ValidateException('商户ID不能为空');
        }

        // 验证商户是否存在
        $merchant = $this->merchantRepository->get($merId);
        if (!$merchant) {
            throw new ValidateException('商户不存在');
        }

        try {
            // 获取授权令牌
            $alipayService = $this->getAlipayService();
            $config = $this->getServiceConfig();
            $result = $alipayService->getAppAuthToken($config['app_id'], $config['private_key'], $auth_code);
            
            // 保存授权信息
            $this->authRepository->saveAuthInfo($merId, $result);
            
            Log::info('支付宝授权回调处理成功', [
                'mer_id' => $merId,
                'source' => $source,
                'auth_app_id' => $result['auth_app_id'] ?? '',
                'user_id' => $result['user_id'] ?? ''
            ]);
            
            return [
                'success' => true,
                'message' => '授权成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            Log::error('支付宝授权回调处理失败：' . $e->getMessage(), [
                'mer_id' => $merId,
                'source' => $source,
                'auth_code' => $auth_code
            ]);
            throw new ValidateException('授权失败：' . $e->getMessage());
        }
    }

    /**
     * 刷新授权令牌
     * @param int $merId 商户ID
     * @return bool
     * @throws ValidateException
     */
    public function refreshAuthToken(int $merId): bool
    {
        try {
            return $this->authRepository->refreshAuthToken($merId);
        } catch (\Exception $e) {
            Log::error('刷新支付宝授权令牌失败：' . $e->getMessage(), ['mer_id' => $merId]);
            throw new ValidateException('刷新授权令牌失败：' . $e->getMessage());
        }
    }

    /**
     * 取消授权
     * @param int $merId 商户ID
     * @return bool
     * @throws ValidateException
     */
    public function cancelAuth(int $merId): bool
    {
        try {
            return $this->authRepository->cancelAuth($merId);
        } catch (\Exception $e) {
            Log::error('取消支付宝授权失败：' . $e->getMessage(), ['mer_id' => $merId]);
            throw new ValidateException('取消授权失败：' . $e->getMessage());
        }
    }

    /**
     * 获取授权信息
     * @param int $merId 商户ID
     * @return array|null
     */
    public function getAuthInfo(int $merId): ?array
    {
        try {
            return $this->authRepository->getAuthInfo($merId);
        } catch (\Exception $e) {
            Log::error('获取支付宝授权信息失败：' . $e->getMessage(), ['mer_id' => $merId]);
            return null;
        }
    }

    /**
     * 批量获取授权状态
     * @param array $merIds 商户ID数组
     * @return array
     */
    public function batchGetAuthStatus(array $merIds): array
    {
        $result = [];
        foreach ($merIds as $merId) {
            $result[$merId] = $this->getAuthStatus($merId);
        }
        return $result;
    }
}
