<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------


namespace crmeb\services\alipay;


use crmeb\services\payment\UnifiedPaymentService;
use think\exception\ValidateException;
use think\facade\Route;

class AlipayService
{
    /**
     * @var array
     */
    protected $config;

    /**
     * 统一支付服务
     * @var UnifiedPaymentService
     */
    protected $unifiedPaymentService;

    public function __construct(array $config)
    {
        $this->config = $config;
        // 初始化统一支付服务
        $this->unifiedPaymentService = new UnifiedPaymentService();
    }

    public static function create($type = '')
    {
        return new self(self::getConfig($type));
    }

    public static function getConfig($type = '')
    {
        $config = systemConfig(['site_url', 'alipay_app_id', 'alipay_public_key', 'alipay_private_key', 'alipay_open']);
        if (!$config['alipay_open']) throw new ValidateException('支付宝支付未开启');
        $siteUrl = $config['site_url'];
        return [
            'app_id' => $config['alipay_app_id'],
            'sign_type' => 'RSA2', // RSA  RSA2
            'limit_pay' => [
//                'balance',// 余额
//                'moneyFund',// 余额宝
//                'debitCardExpress',// 	借记卡快捷
                //'creditCard',//信用卡
                //'creditCardExpress',// 信用卡快捷
                //'creditCardCartoon',//信用卡卡通
                //'credit_group',// 信用支付类型（包含信用卡卡通、信用卡快捷、花呗、花呗分期）
            ], // 用户不可用指定渠道支付当有多个渠道时用"，"分隔

            // 支付宝公钥字符串
            'ali_public_key' => $config['alipay_public_key'],
            // 自己生成的密钥字符串
            'rsa_private_key' => $config['alipay_private_key'],
            'notify_url' => rtrim($siteUrl, '/') . Route::buildUrl('alipayNotify', ['type' => $type])->build(),
            'return_url' => $siteUrl,
        ];
    }

    /**
     * 支付宝二维码支付 - 使用统一支付架构
     */
    public function qrPaymentPrepare($out_trade_no, $total_fee, $body, $detail = '')
    {
        $result = $this->processUnifiedPayment('alipayQr', [
            'order_no' => $out_trade_no,
            'amount' => $total_fee,
            'subject' => $body,
            'body' => $detail ?: $body
        ]);
        
        if (isset($result['code_url'])) {
            return $result['code_url'];
        }
        
        throw new ValidateException('支付宝二维码生成失败');
    }

    /**
     * 支付宝条码支付 - 使用统一支付架构
     */
    public function payAlipayBarCode($out_trade_no, $total_fee, $body, $auth_code, $detail = '')
    {
        $result = $this->processUnifiedPayment('alipayBarCode', [
            'order_no' => $out_trade_no,
            'amount' => $total_fee,
            'subject' => $body,
            'body' => $detail ?: $body,
            'auth_code' => $auth_code
        ]);
        
        return [
            'paid' => $result['paid'] ?? 0,
            'message' => $result['message'] ?? '支付处理中',
            'transaction_id' => $result['transaction_id'] ?? '',
            'payInfo' => $result
        ];
    }

    /**
     * 支付宝APP支付 - 使用统一支付架构
     */
    public function appPaymentPrepare($out_trade_no, $total_fee, $body, $detail = '')
    {
        return $this->processUnifiedPayment('alipayApp', [
            'order_no' => $out_trade_no,
            'amount' => $total_fee,
            'subject' => $body,
            'body' => $detail ?: $body
        ]);
    }

    /**
     * 支付宝H5支付 - 使用统一支付架构
     */
    public function wapPaymentPrepare($out_trade_no, $total_fee, $body, $return_url = '', $detail = '')
    {
        return $this->processUnifiedPayment('alipay', [
            'order_no' => $out_trade_no,
            'amount' => $total_fee,
            'subject' => $body,
            'body' => $detail ?: $body,
            'return_url' => $return_url
        ]);
    }

    /**
     * 支付宝退款 - 使用统一支付架构
     */
    public function payOrderRefund($trade_sn, array $data)
    {
        return $this->unifiedPaymentService->refund(
            'alipay',
            $trade_sn,
            $data['refund_price'],
            [
                'refund_id' => $data['refund_id'],
                'reason' => $data['refund_id']
            ]
        );
    }

    /**
     * 支付宝服务商支付 - 使用统一支付架构
     * @param string $out_trade_no 订单号
     * @param float $total_fee 支付金额
     * @param string $body 商品描述
     * @param string $return_url 返回URL
     * @param array $subMerchantInfo 子商户信息
     * @return array
     */
    public function servicePaymentPrepare($out_trade_no, $total_fee, $body, $return_url = '', $subMerchantInfo = [])
    {
        $result = $this->processUnifiedServicePayment('alipay', [
            'order_no' => $out_trade_no,
            'amount' => $total_fee,
            'subject' => $body,
            'body' => $body,
            'return_url' => $return_url,
            'sub_merchant_info' => $subMerchantInfo
        ]);
        
        return $result;
    }

    /**
     * 支付宝服务商APP支付 - 使用统一支付架构
     * @param string $out_trade_no 订单号
     * @param float $total_fee 支付金额
     * @param string $body 商品描述
     * @param array $subMerchantInfo 子商户信息
     * @return array
     */
    public function serviceAppPaymentPrepare($out_trade_no, $total_fee, $body, $subMerchantInfo = [])
    {
        $result = $this->processUnifiedServicePayment('alipayApp', [
            'order_no' => $out_trade_no,
            'amount' => $total_fee,
            'subject' => $body,
            'body' => $body,
            'sub_merchant_info' => $subMerchantInfo
        ]);
        
        return $result;
    }

    /**
     * 支付宝服务商退款 - 使用统一支付架构
     * @param string $trade_sn 交易号
     * @param array $data 退款数据
     * @return array
     */
    public function serviceRefund($trade_sn, array $data)
    {
        return $this->unifiedPaymentService->alipayServiceRefund(
            'alipay',
            $trade_sn,
            $data['refund_price'],
            [
                'refund_id' => $data['refund_id'],
                'reason' => $data['refund_id'],
                'merchant_id' => $data['merchant_id'] ?? 0
            ]
        );
    }

    /**
     * 支付宝服务商分账 - 使用统一支付架构
     * @param string $trade_sn 交易号
     * @param array $royaltyInfo 分账信息
     * @param array $config 配置信息
     * @return array
     */
    public function serviceRoyalty($trade_sn, array $royaltyInfo, array $config = [])
    {
        return $this->unifiedPaymentService->alipayServiceRoyalty(
            'alipay',
            $trade_sn,
            $royaltyInfo,
            $config
        );
    }

    /**
     * 支付宝服务商回调处理 - 使用统一支付架构
     * @param string $type 回调类型
     * @return string
     */
    public function serviceNotify($type)
    {
        // 获取回调数据
        $callbackData = request()->post();
        
        // 使用统一支付架构处理服务商回调
        $result = $this->unifiedPaymentService->handleAlipayServiceCallback('alipay', $callbackData, [
            'notify_type' => $type
        ]);
        
        // 支付宝回调响应格式
        return $result['success'] ?? false ? 'success' : 'fail';
    }

    /**
     * 统一支付架构处理
     */
    protected function processUnifiedPayment($type, $orderData)
    {
        // 构建统一支付配置
        $unifiedConfig = [
            'affect' => 'alipay',
            'order_type' => 'alipay_order'
        ];

        // 调用统一支付服务
        $result = $this->unifiedPaymentService->createPayment($type, $orderData, $unifiedConfig);

        // 如果统一架构返回了完整的支付配置，直接使用
        if (isset($result['unified_result']) && is_array($result['unified_result'])) {
            return $result['unified_result'];
        }

        throw new ValidateException('支付宝支付创建失败');
    }

    /**
     * 统一服务商支付架构处理
     * @param string $type 支付类型
     * @param array $orderData 订单数据
     * @return array
     */
    protected function processUnifiedServicePayment($type, $orderData)
    {
        // 构建统一支付配置
        $unifiedConfig = [
            'affect' => 'alipay_service',
            'order_type' => 'alipay_service_order',
            'alipay_service_mode' => true,
            'merchant_id' => $orderData['merchant_id'] ?? 0
        ];

        // 调用统一支付服务
        $result = $this->unifiedPaymentService->createPayment($type, $orderData, $unifiedConfig);

        // 如果统一架构返回了完整的支付配置，直接使用
        if (isset($result['unified_result']) && is_array($result['unified_result'])) {
            return $result['unified_result'];
        }

        throw new ValidateException('支付宝服务商支付创建失败');
    }

    /**
     * 支付宝回调处理 - 使用统一支付架构
     */
    public function notify($type)
    {
        // 获取回调数据
        $callbackData = request()->post();
        
        // 使用统一支付架构处理回调
        $result = $this->unifiedPaymentService->handleCallback('alipay', $callbackData, [
            'notify_type' => $type
        ]);
        
        // 支付宝回调响应格式
        return $result['success'] ?? false ? 'success' : 'fail';
    }

    /**
     * 支付宝订单查询 - 使用统一支付架构
     */
    public function query(string $trade_sn)
    {
        // 使用统一支付架构查询订单
        return $this->unifiedPaymentService->queryOrder('alipay', $trade_sn);
    }

    /**
     * 获取应用授权Token - 支付宝授权相关（非支付功能）
     */
    public function getAppAuthToken($app_id, $private_key, $auth_code)
    {
        try {
            $params = [
                'grant_type' => 'authorization_code',
                'code' => $auth_code
            ];

            $result = $this->executeAuthRequest('alipay.open.auth.token.app', $params, $app_id, $private_key);

            if (isset($result['code']) && $result['code'] == '10000') {
                return [
                    'app_auth_token' => $result['app_auth_token'],
                    'auth_app_id' => $result['auth_app_id'],
                    'user_id' => $result['user_id'],
                    'expires_in' => $result['expires_in'],
                    'refresh_token' => $result['app_refresh_token'],
                    're_expires_in' => $result['re_expires_in']
                ];
            } else {
                throw new ValidateException('获取授权令牌失败：' . ($result['sub_msg'] ?? $result['msg'] ?? '未知错误'));
            }
        } catch (\Exception $e) {
            Log::error('获取支付宝应用授权令牌失败：' . $e->getMessage());
            throw new ValidateException('获取授权令牌失败：' . $e->getMessage());
        }
    }

    /**
     * 刷新应用授权Token
     */
    public function refreshAppAuthToken($app_id, $private_key, $refresh_token)
    {
        try {
            $params = [
                'grant_type' => 'refresh_token',
                'refresh_token' => $refresh_token
            ];

            $result = $this->executeAuthRequest('alipay.open.auth.token.app', $params, $app_id, $private_key);

            if (isset($result['code']) && $result['code'] == '10000') {
                return [
                    'app_auth_token' => $result['app_auth_token'],
                    'auth_app_id' => $result['auth_app_id'],
                    'user_id' => $result['user_id'],
                    'expires_in' => $result['expires_in'],
                    'refresh_token' => $result['app_refresh_token'],
                    're_expires_in' => $result['re_expires_in']
                ];
            } else {
                throw new ValidateException('刷新授权令牌失败：' . ($result['sub_msg'] ?? $result['msg'] ?? '未知错误'));
            }
        } catch (\Exception $e) {
            Log::error('刷新支付宝应用授权令牌失败：' . $e->getMessage());
            throw new ValidateException('刷新授权令牌失败：' . $e->getMessage());
        }
    }

    /**
     * 查询应用授权Token状态
     */
    public function queryAppAuthToken($app_auth_token)
    {
        try {
            $params = [
                'app_auth_token' => $app_auth_token
            ];

            $result = $this->executeAuthRequest('alipay.open.auth.token.app.query', $params,
                systemConfig('alipay_service_app_id'),
                systemConfig('alipay_service_private_key')
            );

            if (isset($result['code']) && $result['code'] == '10000') {
                return [
                    'valid' => $result['status'] === 'valid',
                    'auth_app_id' => $result['auth_app_id'],
                    'user_id' => $result['user_id'],
                    'expires_in' => $result['expires_in']
                ];
            } else {
                return [
                    'valid' => false,
                    'error' => $result['sub_msg'] ?? $result['msg'] ?? '查询失败'
                ];
            }
        } catch (\Exception $e) {
            Log::error('查询支付宝应用授权令牌状态失败：' . $e->getMessage());
            return [
                'valid' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 生成授权链接
     */
    public function generateAuthUrl($redirect_uri, $state = '')
    {
        $app_id = systemConfig('alipay_service_app_id');
        if (empty($app_id)) {
            throw new ValidateException('支付宝服务商应用ID未配置');
        }

        $params = [
            'app_id' => $app_id,
            'redirect_uri' => $redirect_uri
        ];

        if (!empty($state)) {
            $params['state'] = $state;
        }

        $url = 'https://openauth.alipay.com/oauth2/appToAppAuth.htm?' . http_build_query($params);

        Log::info('生成支付宝授权链接', [
            'app_id' => $app_id,
            'redirect_uri' => $redirect_uri,
            'state' => $state,
            'url' => $url
        ]);

        return $url;
    }

    /**
     * 执行授权相关的API请求
     */
    private function executeAuthRequest($method, $params, $app_id, $private_key)
    {
        $commonParams = [
            'app_id' => $app_id,
            'method' => $method,
            'charset' => 'UTF-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'biz_content' => json_encode($params, JSON_UNESCAPED_UNICODE)
        ];

        // 生成签名
        $commonParams['sign'] = $this->generateSign($commonParams, $private_key);

        // 发送请求
        $url = 'https://openapi.alipay.com/gateway.do';
        $response = $this->httpPost($url, $commonParams);

        // 解析响应
        $result = json_decode($response, true);
        if (!$result) {
            throw new ValidateException('支付宝API响应格式错误');
        }

        // 获取具体的响应数据
        $responseKey = str_replace('.', '_', $method) . '_response';
        if (isset($result[$responseKey])) {
            return $result[$responseKey];
        }

        throw new ValidateException('支付宝API响应数据格式错误');
    }

    /**
     * 生成签名
     */
    private function generateSign($params, $private_key)
    {
        // 排序参数
        ksort($params);

        // 构建签名字符串
        $signString = '';
        foreach ($params as $key => $value) {
            if ($key !== 'sign' && $value !== '' && $value !== null) {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString = rtrim($signString, '&');

        // RSA2签名
        $private_key = "-----BEGIN RSA PRIVATE KEY-----\n" .
                      wordwrap($private_key, 64, "\n", true) .
                      "\n-----END RSA PRIVATE KEY-----";

        $key = openssl_pkey_get_private($private_key);
        if (!$key) {
            throw new ValidateException('支付宝私钥格式错误');
        }

        openssl_sign($signString, $signature, $key, OPENSSL_ALGO_SHA256);
        openssl_free_key($key);

        return base64_encode($signature);
    }

    /**
     * HTTP POST请求
     */
    private function httpPost($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded;charset=UTF-8'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new ValidateException('HTTP请求失败：' . $error);
        }

        if ($httpCode !== 200) {
            throw new ValidateException('HTTP请求失败，状态码：' . $httpCode);
        }

        return $response;
    }

    // ==================== 支付宝直付通服务商模式方法 ====================

    /**
     * 二级商户进件
     * @param array $data 商户信息
     * @return array
     */
    public function submitApplication(array $data)
    {
        try {
            // 构建进件参数
            $params = [
                'business_license_info' => [
                    'license_name' => $data['merchant_name'], // 营业执照上的商户名称
                    'license_no' => $data['business_license_number'], // 营业执照号
                    'license_cert_type' => 'BUSINESS_LICENCE_MERGE', // 证件类型，固定为营业执照
                    'license_cert_image' => $data['business_license_copy'], // 营业执照图片
                ],
                'shop_info' => [
                    'shop_name' => $data['merchant_shortname'], // 店铺名称
                    'shop_category' => $data['merchant_category'], // 店铺类目
                    'store_type' => $data['store_type'] ?? 'ONLINE', // 店铺类型，默认为线上店铺
                ],
                'contact_info' => [
                    'contact_name' => $data['contact_name'], // 联系人姓名
                    'contact_mobile' => $data['contact_phone'], // 联系人手机号
                    'contact_email' => $data['contact_email'] ?? '', // 联系人邮箱
                ],
                'legal_person_info' => [
                    'legal_person_name' => $data['legal_person_name'], // 法人姓名
                    'legal_person_cert_type' => 'IDENTITY_CARD', // 证件类型，固定为身份证
                    'legal_person_cert_no' => $data['legal_person_cert_no'], // 法人身份证号
                    'legal_person_cert_front' => $data['legal_person_cert_front'], // 法人身份证正面
                    'legal_person_cert_back' => $data['legal_person_cert_back'], // 法人身份证反面
                ],
                'settlement_info' => [
                    'settlement_type' => 'BANKCARD', // 结算方式，固定为银行卡
                    'bank_account_type' => 'CORPORATE_ACCOUNT', // 银行账户类型，对公账户
                    'bank_account_name' => $data['bank_account_name'], // 银行账户名称
                    'bank_account_no' => $data['bank_account_no'], // 银行账号
                    'bank_name' => $data['bank_name'], // 开户银行名称
                ],
                'service_phone' => $data['service_phone'] ?? '', // 客服电话
                'out_biz_no' => $data['out_request_no'], // 外部请求编号
            ];

            // 调用支付宝API进行商户进件
            $result = $this->unifiedPaymentService->alipayServiceApplication('alipay.open.agent.mini.create', $params);
            
            if (isset($result['code']) && $result['code'] == '10000') {
                return [
                    'sub_mchid' => $result['smid'], // 二级商户ID
                    'status' => 'SUCCESS',
                    'applyment_id' => $result['order_no'], // 支付宝进件单号
                ];
            } else {
                throw new ValidateException('进件失败：' . ($result['sub_msg'] ?? $result['msg'] ?? '未知错误'));
            }
        } catch (\Exception $e) {
            throw new ValidateException('支付宝直付通进件异常：' . $e->getMessage());
        }
    }

    /**
     * 查询进件状态
     * @param string $outBizNo 外部业务编号
     * @return array
     */
    public function queryApplicationStatus($outBizNo)
    {
        try {
            $params = [
                'out_biz_no' => $outBizNo,
            ];

            $result = $this->unifiedPaymentService->alipayServiceApplication('alipay.open.agent.mini.query', $params);
            
            if (isset($result['code']) && $result['code'] == '10000') {
                $status = '';
                switch ($result['status']) {
                    case 'AUDITING':
                        $status = 'AUDITING';
                        break;
                    case 'AUDIT_FAIL':
                        $status = 'REJECTED';
                        break;
                    case 'AUDIT_SUCCESS':
                        $status = 'APPROVED';
                        break;
                    default:
                        $status = 'PROCESSING';
                }
                
                return [
                    'sub_mchid' => $result['smid'] ?? '',
                    'status' => $status,
                    'applyment_id' => $result['order_no'] ?? '',
                    'audit_detail' => $result['reject_reason'] ?? '',
                ];
            } else {
                throw new ValidateException('查询失败：' . ($result['sub_msg'] ?? $result['msg'] ?? '未知错误'));
            }
        } catch (\Exception $e) {
            throw new ValidateException('支付宝直付通查询异常：' . $e->getMessage());
        }
    }

    /**
     * 获取二级商户信息
     * @param string $smid 二级商户ID
     * @return array
     */
    public function getSubMerchant($smid)
    {
        try {
            $params = [
                'smid' => $smid,
            ];

            $result = $this->unifiedPaymentService->alipayServiceApplication('alipay.open.mini.merchant.info.query', $params);
            
            if (isset($result['code']) && $result['code'] == '10000') {
                return [
                    'sub_mchid' => $smid,
                    'merchant_name' => $result['merchant_name'] ?? '',
                    'merchant_shortname' => $result['merchant_alias'] ?? '',
                    'service_phone' => $result['service_phone'] ?? '',
                    'status' => 'NORMAL',
                ];
            } else {
                throw new ValidateException('查询失败：' . ($result['sub_msg'] ?? $result['msg'] ?? '未知错误'));
            }
        } catch (\Exception $e) {
            throw new ValidateException('支付宝直付通查询异常：' . $e->getMessage());
        }
    }

    /**
     * 创建支付宝直付通服务商实例（兼容性方法）
     * @return AlipayService
     */
    public static function createServiceProvider()
    {
        return self::create('service');
    }
}
