<?php
/**
 * 支付宝服务商支付服务 - 统一架构版本
 * 完全基于统一支付架构重构
 */

namespace crmeb\services\alipay;

use crmeb\services\payment\UnifiedPaymentService;
use think\facade\Route;
use think\facade\Log;

class ServicePartner
{
    protected $config;
    protected $factory;

    /**
     * 统一支付服务
     * @var UnifiedPaymentService
     */
    protected $unifiedPaymentService;

    /**
     * 支付类型映射到统一架构
     * @var array
     */
    protected $paymentTypeMapping = [
        'alipay' => 'alipay',
        'alipayApp' => 'alipayApp',
        'alipayQr' => 'alipayQr',
        'alipayBarCode' => 'alipayBarCode'
    ];

    /**
     * 构造函数
     * @param string|array $paymentTypeOrConfig 支付类型或配置信息（兼容旧版本）
     * @param array $config 配置信息（可选）
     */
    public function __construct($paymentTypeOrConfig, array $config = [])
    {
       
        $this->paymentType = $paymentTypeOrConfig;
        $this->config = $config;
      
        
        // 初始化统一支付服务
        $this->unifiedPaymentService = new UnifiedPaymentService();
        
        $this->validatePaymentType();
    }

    /**
     * 验证支付类型
     */
    protected function validatePaymentType(): void
    {
        if (!isset($this->paymentTypeMapping[$this->paymentType])) {
            throw new \Exception("不支持的支付宝服务商支付方式: {$this->paymentType}");
        }
    }

    /**
     * 支付宝服务商支付 - 使用统一支付架构
     * @param \app\common\model\user\User|null $user 用户对象
     * @return array
     */
    public function pay($user = null)
    {
        try {
            // 构建统一支付配置
            $unifiedConfig = [
                'alipay_service_mode' => true, // 标识为服务商模式
                'affect' => 'alipay_service',
                'merchant_config' => $this->config,
                'notify_url' => $this->buildNotifyUrl(),
                'return_url' => $this->config['return_url'] ?? '',
            ];

            // 构建订单数据
            $orderData = [
                'order_no' => $this->config['out_trade_no'],
                'amount' => $this->config['total_amount'],
                'subject' => $this->config['subject'] ?? '订单支付',
                'body' => $this->config['body'] ?? '支付宝服务商支付',
                'return_url' => $this->config['return_url'] ?? '',
                'payment_method' => \crmeb\services\payment\UnifiedPaymentService::mapPaymentMethod($this->paymentType),
                'sub_orders' => $this->config['sub_orders'] ?? []
            ];

            // 获取映射的支付类型
            $unifiedType = $this->paymentTypeMapping[$this->paymentType];

            // 调用统一支付服务
            $result = $this->unifiedPaymentService->createPayment(
                $unifiedType,
                $orderData,
                $unifiedConfig,
                $user
            );

           

            return $result;

        } catch (\Exception $e) {
            Log::error('支付宝服务商支付创建失败', [
                'type' => $this->paymentType,
                'config' => $this->config,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 支付宝APP服务商支付 - 使用统一支付架构
     * @param \app\common\model\user\User|null $user 用户对象
     * @return array
     */
    public function appPay($user = null)
    {
        // 设置为APP支付类型并调用统一支付
        $this->paymentType = 'alipayApp';
        return $this->pay($user);
    }

    /**
     * 处理支付宝服务商回调 - 使用统一支付架构
     * @param callable $callback 回调函数
     * @return string
     */
    public function handleNotify($callback)
    {
        try {
            // 获取回调数据
            $callbackData = $this->getCallbackData();
            
            if (empty($callbackData)) {
                return 'fail';
            }

            // 使用统一支付架构处理回调
            $result = $this->unifiedPaymentService->handleCallback(
                'alipay_service',
                $callbackData,
                [
                    'alipay_service_mode' => true,
                    'merchant_config' => $this->config
                ]
            );

            if ($result['success'] ?? false) {
                // 调用业务回调函数
                $businessResult = $callback($callbackData, true);
                return $businessResult ? 'success' : 'fail';
            }

            return 'fail';

        } catch (\Exception $e) {
            Log::error('支付宝服务商回调处理异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 'fail';
        }
    }

    /**
     * 支付宝服务商退款 - 使用统一支付架构
     * @param array $options 退款参数
     * @return array
     */
    public function refund(array $options)
    {
        try {
            $config = [
                'alipay_service_mode' => true,
                'merchant_config' => $this->config
            ];

            return $this->unifiedPaymentService->refund(
                'alipay_service',
                $options['out_trade_no'],
                $options['refund_amount'],
                array_merge($config, $options)
            );

        } catch (\Exception $e) {
            Log::error('支付宝服务商退款失败', [
                'options' => $options,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 支付宝服务商分账 - 使用统一支付架构
     * @param array $options 分账参数
     * @return array
     */
    public function royalty(array $options)
    {
        try {
            return $this->unifiedPaymentService->alipayServiceRoyalty(
                'alipay_service',
                $options['trade_no'],
                $options['royalty_info'],
                [
                    'alipay_service_mode' => true,
                    'merchant_config' => $this->config,
                    'out_request_no' => $options['out_request_no'],
                    'trans_out' => $options['trans_out']
                ]
            );

        } catch (\Exception $e) {
            Log::error('支付宝服务商分账失败', [
                'options' => $options,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取回调数据
     */
    protected function getCallbackData(): array
    {
        $data = $_POST;
        if (empty($data)) {
            $data = json_decode(file_get_contents('php://input'), true) ?: [];
        }
        return $data;
    }

    /**
     * 构建通知URL
     */
    protected function buildNotifyUrl(): string
    {
        $siteUrl = systemConfig('site_url');
        return $siteUrl . '/api/payment/notify/alipay_service';
    }



    /**
     * 查询支付状态 - 使用统一支付架构
     */
    public function queryPayment(string $orderSn): array
    {
        try {
            return $this->unifiedPaymentService->queryPayment(
                'alipay_service',
                $orderSn,
                [
                    'alipay_service_mode' => true,
                    'merchant_config' => $this->config
                ]
            );

        } catch (\Exception $e) {
            Log::error('查询支付宝服务商支付状态失败', [
                'order_sn' => $orderSn,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    // ==================== 静态方法 ====================

    /**
     * 创建支付宝服务商支付实例
     */
    public static function create(string $paymentType, array $config): self
    {
        return new self($paymentType, $config);
    }

    /**
     * 验证支付宝服务商支付方式是否支持
     */
    public static function isServicePayTypeSupported(string $type): bool
    {
        $supportedTypes = ['alipay', 'alipayApp', 'alipayQr', 'alipayBarCode'];
        return in_array($type, $supportedTypes);
    }

    // ==================== Getter 方法 ====================

    /**
     * 获取支付类型
     */
    public function getPaymentType(): string
    {
        return $this->paymentType;
    }

    /**
     * 获取配置信息
     */
    public function getConfig(): array
    {
        return $this->config;
    }

    /**
     * 获取统一支付服务实例
     */
    public function getUnifiedPaymentService(): UnifiedPaymentService
    {
        return $this->unifiedPaymentService;
    }
}
