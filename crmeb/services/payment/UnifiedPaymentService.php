<?php

namespace crmeb\services\payment;

use crmeb\services\payment\factories\PaymentGatewayFactory;
use crmeb\services\payment\config\PaymentConfigCenter;
use crmeb\services\payment\config\PaymentMethodConfigService;
use crmeb\services\payment\adapters\PaymentServiceAdapter;
use crmeb\services\PayService;
use crmeb\services\api\ApiPaymentService;
use crmeb\services\CombinePayService;
use crmeb\services\alipay\ServicePartner;
use think\facade\Log;
use think\facade\Cache;

/**
 * 统一支付服务
 * 整合所有支付逻辑，提供统一的支付接口
 */
class UnifiedPaymentService
{
    /**
     * 支付网关工厂
     * @var PaymentGatewayFactory
     */
    protected $gatewayFactory;

    /**
     * 支付服务适配器
     * @var PaymentServiceAdapter
     */
    protected $serviceAdapter;

    /**
     * 支付配置中心
     * @var PaymentConfigCenter
     */
    protected $configCenter;

    /**
     * 支持的支付方式映射
     * @var array
     */
    protected $paymentMethodMap = [
        // 微信支付系列 - 添加数据库中实际的支付类型
        'wechat' => ['gateway' => 'wechat', 'method' => 'jsapi'], // 数据库中的微信支付
        'weixin' => ['gateway' => 'wechat', 'method' => 'jsapi'], // 兼容旧版本
        'weixinApp' => ['gateway' => 'wechat', 'method' => 'app'],
        'weixinQr' => ['gateway' => 'wechat', 'method' => 'native'],
        'h5' => ['gateway' => 'wechat', 'method' => 'h5'],
        'routine' => ['gateway' => 'wechat', 'method' => 'miniprogram'],
        'weixinBarCode' => ['gateway' => 'wechat', 'method' => 'barcode'],
        
        // 支付宝支付系列
        'alipay' => ['gateway' => 'alipay', 'method' => 'wap'],
        'alipayApp' => ['gateway' => 'alipay', 'method' => 'app'],
        'alipayQr' => ['gateway' => 'alipay', 'method' => 'qr'],
        'alipayBarCode' => ['gateway' => 'alipay', 'method' => 'barcode'],
        'alipay_mini' => ['gateway' => 'alipay', 'method' => 'miniprogram'],
        
        // 特殊支付方式
        'balance' => ['gateway' => 'balance', 'method' => 'balance'],
        'offline' => ['gateway' => 'offline', 'method' => 'offline'],
        'api' => ['gateway' => 'api', 'method' => 'api'],
        
        // H5拉起小程序支付 - 使用智能小程序网关
        'h5_to_mini' => ['gateway' => 'smart_miniprogram', 'method' => 'h5_to_miniprogram']
    ];

    /**
     * @var PaymentServiceRegistry
     */
    protected $registry;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->gatewayFactory = new \crmeb\services\payment\factories\PaymentGatewayFactory();
        $this->serviceAdapter = new PaymentServiceAdapter();
        $this->configCenter = new PaymentConfigCenter();
        $this->registry = PaymentServiceRegistry::getInstance();
    }

    /**
     * 创建网关适配器方法
     * @param string $gatewayType
     * @param array $config
     * @return mixed
     */
    protected function createGateway(string $gatewayType, array $config)
    {
        return $this->gatewayFactory::create($gatewayType, $config);
    }

    /**
     * 创建支付订单
     * @param string $paymentType 支付类型
     * @param array $orderData 订单数据
     * @param array $config 配置信息
     * @param object|null $user 用户对象
     * @return array
     */
    public function createPayment(string $paymentType, array $orderData, array $config = [], $user = null): array
    {
                
        try {
            
            // 获取支付方式映射
            $mapping = $this->getPaymentMapping($paymentType);
          
            if (!$mapping) {
                throw new \Exception("不支持的支付方式: {$paymentType}");
            }

            // 获取商户配置 - 兼容多种键名格式
            $merchantId = isset($config['merchant_id']) ? (int)$config['merchant_id'] : (isset($config['mer_id']) ? (int)$config['mer_id'] : 0);
            $merchantConfig = $this->getMerchantConfig($merchantId, $mapping['gateway']);

            //Log::error('merchantConfig' . json_encode($merchantConfig));
          
            // 创建网关实例
            $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create($mapping['gateway'], $merchantConfig);
         
            // 构建统一订单数据
            $unifiedOrderData = $this->buildUnifiedOrderData($orderData, $mapping['method'], $user);

            // 检查是否为支付宝服务商支付
            if ($mapping['gateway'] === 'alipay' && $this->isAlipayServiceMode($config, $merchantConfig)) {
                $result = $gateway->createServicePayment($unifiedOrderData);
            } else {
                $result = $gateway->createPayment($unifiedOrderData);
            }

            // 记录支付日志
            $this->logPaymentAction('create', $paymentType, $unifiedOrderData, $result);

            return $this->formatPaymentResult($result, $paymentType);

        } catch (\Exception $e) {
            $this->logError('创建统一支付失败', [
                'payment_type' => $paymentType,
                'order_data' => $orderData,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 查询支付状态
     * @param string $paymentType 支付类型
     * @param string $transactionId 交易ID
     * @param array $config 配置信息
     * @return array
     */
    public function queryPayment(string $paymentType, string $transactionId, array $config = []): array
    {
        try {
           
            // 获取支付方式映射
            $mapping = $this->getPaymentMapping($paymentType);
            if (!$mapping) {
                throw new \Exception("不支持的支付方式: {$paymentType}");
            }

            // 获取商户配置 - 兼容多种键名格式
            $merchantId = isset($config['merchant_id']) ? (int)$config['merchant_id'] : (isset($config['mer_id']) ? (int)$config['mer_id'] : 0);
            $merchantConfig = $this->getMerchantConfig($merchantId, $mapping['gateway']);

            // 创建网关实例
            $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create($mapping['gateway'], $merchantConfig);

            // 查询支付状态
            $result = $gateway->queryPayment($transactionId, ['payment_method' => $mapping['method']]);

            // 记录查询日志
            $this->logPaymentAction('query', $paymentType, ['transaction_id' => $transactionId], $result);

            return $result;

        } catch (\Exception $e) {
            $this->logError('查询统一支付状态失败', [
                'payment_type' => $paymentType,
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 处理支付回调
     * @param string $paymentType 支付类型
     * @param array $callbackData 回调数据
     * @param array $config 配置信息
     * @return array
     */
    public function handleCallback(string $paymentType, array $callbackData, array $config = []): array
    {
        try {
            // 获取支付方式映射
            $mapping = $this->getPaymentMapping($paymentType);
            if (!$mapping) {
                throw new \Exception("不支持的支付方式: {$paymentType}");
            }

            // 获取商户配置 - 兼容多种键名格式
            $merchantId = isset($config['merchant_id']) ? (int)$config['merchant_id'] : (isset($config['mer_id']) ? (int)$config['mer_id'] : 0);
            $merchantConfig = $this->getMerchantConfig($merchantId, $mapping['gateway']);

            // 创建网关实例
            $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create($mapping['gateway'], $merchantConfig);

            // 处理回调
            $result = $gateway->handleCallback($callbackData);

            // 检查是否为API支付，需要通知合作伙伴
            if (isset($config['notify_type']) && $config['notify_type'] === 'api_payment') {
                $this->handleApiPaymentCallback($result, $config);
            }

            // 记录回调日志
            $this->logPaymentAction('callback', $paymentType, $callbackData, $result);

            return $result;

        } catch (\Exception $e) {
            $this->logError('处理统一支付回调失败', [
                'payment_type' => $paymentType,
                'callback_data' => $callbackData,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 处理API支付回调，通知合作伙伴
     * @param array $result 支付结果
     * @param array $config 配置信息
     */
    protected function handleApiPaymentCallback(array $result, array $config): void
    {
        try {
            // 获取API支付服务
            $apiPaymentService = app()->make(\crmeb\services\api\ApiPaymentService::class);
            
            // 获取订单号
            $orderNo = $result['order_no'] ?? $config['order_no'] ?? '';
            if (!$orderNo) {
                Log::warning('API支付回调缺少订单号');
                return;
            }
            
            // 获取合作伙伴信息
            $partnerId = $config['partner_id'] ?? 0;
            if (!$partnerId) {
                Log::warning('API支付回调缺少合作伙伴ID');
                return;
            }
            
            // 更新支付状态
            $apiPaymentService->updatePaymentStatus($orderNo, $result);
            
            // 通知合作伙伴
            $partner = $this->getPartnerInfo($partnerId);
            if ($partner) {
                $apiPaymentService->notifyPartner($partner, $orderNo, $result);
            }
            
        } catch (\Exception $e) {
            Log::error('处理API支付回调通知失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取合作伙伴信息
     * @param int $partnerId
     * @return array|null
     */
    protected function getPartnerInfo(int $partnerId): ?array
    {
        try {
            $partnerRepository = app()->make(\app\common\repositories\api\ApiPartnerRepository::class);
            $partner = $partnerRepository->find($partnerId);
            return $partner ? $partner->toArray() : null;
        } catch (\Exception $e) {
            Log::error('获取合作伙伴信息失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取回调数据
     * @param string $paymentType 支付类型
     * @return array
     */
    protected function getCallbackData(string $paymentType): array
    {
        $request = request();
        
        if ($paymentType === 'alipay') {
            // 支付宝回调数据从POST获取
            return $request->post();
        } else {
            // 微信回调数据处理
            $contentType = $request->header('content-type');
            if ($contentType === 'application/json') {
                // V3版本回调
                return [
                    'headers' => $request->header(),
                    'body' => $request->getContent(),
                    'post_data' => $request->post()
                ];
            } else {
                // V2版本回调
                return $request->post();
            }
        }
    }

    /**
     * 格式化通知响应
     * @param string $paymentType 支付类型
     * @param array $result 处理结果
     * @return mixed
     */
    protected function formatNotifyResponse(string $paymentType, array $result)
    {
        if ($paymentType === 'alipay') {
            // 支付宝回调响应
            return $result['success'] ? 'success' : 'fail';
        } else {
            // 微信回调响应
            if ($result['success'] ?? false) {
                return response([
                    'code' => 'SUCCESS',
                    'message' => 'OK'
                ], 200, [], 'json');
            } else {
                return response([
                    'code' => 'FAIL',
                    'message' => $result['message'] ?? 'FAIL'
                ], 200, [], 'json');
            }
        }
    }

    /**
     * 申请退款
     * @param string $paymentType 支付类型
     * @param string $transactionId 交易ID
     * @param float $amount 退款金额
     * @param array $config 配置信息
     * @return array
     */
    public function refund(string $paymentType, string $transactionId, float $amount, array $config = []): array
    {
        try {
            

            // 获取支付方式映射
            $mapping = $this->getPaymentMapping($paymentType);
            if (!$mapping) {
                throw new \Exception("不支持的支付方式: {$paymentType}");
            }

            // 获取商户配置 - 兼容多种键名格式
            $merchantId = isset($config['merchant_id']) ? (int)$config['merchant_id'] : (isset($config['mer_id']) ? (int)$config['mer_id'] : 0);
            $merchantConfig = $this->getMerchantConfig($merchantId, $mapping['gateway']);

            // 创建网关实例
            $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create($mapping['gateway'], $merchantConfig);

            // 组装退款数据以适应更新后的网关接口
            $refundData = [
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'refund_no' => $config['refund_no'] ?? null,
                'total_fee' => $config['total_fee'] ?? 0, // 微信退款需要
                'reason' => $config['reason'] ?? '用户申请退款',
                'notify_url' => $config['refund_notify_url'] ?? null, // 微信退款结果通知地址
            ];

            // 兼容旧版，额外参数通过config传递
            if (isset($config['extra'])) {
                $refundData = array_merge($refundData, $config['extra']);
            }

            // 申请退款
            $result = $gateway->refund($refundData);

            // 记录退款日志
            $this->logPaymentAction('refund', $paymentType, [
                'transaction_id' => $transactionId,
                'amount' => $amount
            ], $result);

            return $result;

        } catch (\Exception $e) {
            $this->logError('统一支付退款失败', [
                'payment_type' => $paymentType,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取小程序支付信息（兼容旧版本接口）
     * @param string $orderSn 订单号
     * @param float $amount 支付金额
     * @param string $payType 支付类型
     * @param string $body 商品描述
     * @param array $merConfig 商户配置
     * @return array
     * @throws \Exception
     */
    public function getMiniProgramPayInfo(string $orderSn, float $amount, string $payType, string $body = '订单支付', array $merConfig = []): array
    {
        try {
            // 构建订单数据
            $orderData = [
                'order_no' => $orderSn,
                'amount' => $amount,
                'subject' => $body,
                'platform' => $payType === 'routine' ? 'wechat' : 'alipay'
            ];

            // 根据支付类型选择合适的网关和方法
            if ($payType === 'routine') {
                // 微信小程序支付
                $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create('wechat', $merConfig);
                $result = $gateway->createPayment(array_merge($orderData, [
                    'payment_method' => 'miniprogram'
                ]));
            } elseif (strpos($payType, 'alipay') !== false) {
                // 支付宝小程序支付
                $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create('alipay', $merConfig);
                $result = $gateway->createPayment(array_merge($orderData, [
                    'payment_method' => 'miniprogram'
                ]));
            } else {
                // 默认处理为H5拉起小程序支付
                $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create('wechat', $merConfig);
                $result = $gateway->createPayment(array_merge($orderData, [
                    'payment_method' => 'h5_to_miniprogram'
                ]));
            }

            return $result;

        } catch (\Exception $e) {
            $this->logError('获取小程序支付信息失败', [
                'order_sn' => $orderSn,
                'pay_type' => $payType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

  
    // ==================== 辅助方法 ====================

    /**
     * 判断是否应该使用统一架构
     */
    protected function shouldUseUnifiedArchitecture(string $paymentType, array $config): bool
    {
        // 全局开关
        $globalEnabled = systemConfig('unified_payment_enabled') ?? false;
        
        // 支付方式级别开关
        $typeEnabled = $config['use_unified_architecture'] ?? $globalEnabled;
        
        // 商户级别开关
        $merchantId = $config['merchant_id'] ?? 0;
        if ($merchantId > 0) {
            $merchantEnabled = Cache::get("merchant_{$merchantId}_unified_payment", $typeEnabled);
            return $merchantEnabled;
        }
        
        return $typeEnabled;
    }

    /**
     * 获取支付方式映射（使用统一映射）
     */
    protected function getPaymentMapping(string $paymentType): ?array
    {
        // 使用统一的支付类型映射
        $standardType = PaymentMethodConfigService::normalizePaymentType($paymentType);
        
        // 返回标准映射，保持原有接口格式
        return $this->paymentMethodMap[$paymentType] ?? [
            'gateway' => $standardType, 
            'method' => $this->getDefaultMethod($standardType)
        ];
    }
    
    /**
     * 获取支付类型的默认方法
     */
    protected function getDefaultMethod(string $standardType): string
    {
        $defaultMethods = [
            'wechat' => 'jsapi',
            'alipay' => 'wap',
            'balance' => 'balance',
            'offline' => 'offline',
            'api' => 'api',
        ];
        
        return $defaultMethods[$standardType] ?? 'default';
    }

    /**
     * 获取商户配置（重构版本 - 直接使用分层配置）
     */
    protected function getMerchantConfig(int $merchantId, string $gateway): array
    {
        // 直接获取配置，无需字段映射转换
        $config = PaymentConfigCenter::getEffectiveConfig($merchantId, $gateway) ?? [];

        if (empty($config)) {
            return [];
        }

        // 添加通用配置
        $siteUrl = rtrim(systemConfig('site_url'), '/');
        $config['notify_url'] = $siteUrl . '/api/v1/payment/notify?type=' . $gateway;
        $config['return_url'] = $siteUrl . '/pages/order/payment-success';

        return $config;
    }

    /**
     * 检查是否为服务商模式
     * @param string $gateway 支付网关
     * @param array $config 配置信息
     * @return bool
     */
    protected function isServiceMode(string $gateway, array $config): bool
    {
        switch ($gateway) {
            case 'alipay':
                // 支付宝服务商模式判断
                return !empty($config['service_mode']) || 
                       !empty($config['alipay_service_mode']) ||
                       !empty($config['app_auth_token']) ||
                       systemConfig('alipay_service_mode') == 1;
                       
            case 'wechat':
                // 微信服务商模式判断
                return !empty($config['service_mode']) || 
                       !empty($config['wechat_service_mode']) ||
                       !empty($config['sub_mch_id']) ||
                       systemConfig('wechat_service_mode') == 1;
                       
            default:
                return false;
        }
    }

    /**
     * 构建统一订单数据
     */
    protected function buildUnifiedOrderData(array $orderData, string $method, $user): array
    {
        $unifiedData = [
            'order_no' => $orderData['order_sn'] ?? $orderData['order_no'] ?? '',
            'amount' => $orderData['pay_price'] ?? $orderData['amount'] ?? 0,
            'subject' => $orderData['body'] ?? $orderData['subject'] ?? '商品支付',
            'detail' => $orderData['detail'] ?? '',
            'payment_method' => $method,
            'user_id' => $user ? ($user->uid ?? $user['uid'] ?? 0) : 0,
            'attach' => $orderData['attach'] ?? 'order'
        ];

        // 添加特定支付方式需要的参数
        if (in_array($method, ['jsapi', 'wechat_mini'])) {
            $unifiedData['openid'] = $orderData['openid'] ?? '';
        }

        if ($method === 'barcode') {
            $unifiedData['auth_code'] = $orderData['auth_code'] ?? '';
        }

        if ($method === 'h5') {
            $unifiedData['wap_url'] = $orderData['wap_url'] ?? '';
            $unifiedData['wap_name'] = $orderData['wap_name'] ?? '商户H5支付';
        }

        return $unifiedData;
    }

    /**
     * 格式化支付结果
     */
    protected function formatPaymentResult(array $result, string $paymentType): array
    {
        // 保持与旧版本的兼容性
        $formatted = [
            'config' => $result['payment_data'] ?? $result,
            'time_expire' => $result['time_expire'] ?? (time() + 15 * 60),
            'unified_result' => $result
        ];

        // 特殊处理某些支付方式的返回格式
        if ($paymentType === 'weixinQr') {
            $formatted['config'] = $result['payment_data']['code_url'] ?? $result['code_url'] ?? '';
        }

        return $formatted;
    }

    /**
     * 记录支付操作日志
     */
    protected function logPaymentAction(string $action, string $paymentType, array $requestData, array $responseData): void
    {
        Log::info("统一支付{$action}操作", [
            'action' => $action,
            'payment_type' => $paymentType,
            'request_data' => $requestData,
            'response_data' => $responseData,
            'timestamp' => time()
        ]);
    }

    /**
     * 记录错误日志
     */
    protected function logError(string $message, array $context): void
    {
        Log::error($message, array_merge($context, [
            'timestamp' => time(),
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
        ]));
    }

    /**
     * 获取支持的支付方式列表
     */
    public function getSupportedPaymentTypes(): array
    {
        return array_keys($this->paymentMethodMap);
    }

    /**
     * 检查支付方式是否支持
     */
    public function isPaymentTypeSupported(string $paymentType): bool
    {
        return isset($this->paymentMethodMap[$paymentType]);
    }

    /**
     * 检查是否为支付宝服务商模式
     * @param array $config 配置信息
     * @param array $merchantConfig 商户配置
     * @return bool
     */
    protected function isAlipayServiceMode(array $config, array $merchantConfig): bool
    {
        // 检查全局配置
        if (!empty($config['alipay_service_mode'])) {
            return true;
        }
        
        // 检查商户配置
        if (!empty($merchantConfig['service_mode']) || 
            !empty($merchantConfig['alipay_service_mode']) ||
            !empty($merchantConfig['app_auth_token'])) {
            return true;
        }
        
        // 检查系统配置
        return systemConfig('alipay_service_mode') == 1;
    }

    /**
     * 处理支付宝服务商支付回调
     * @param string $paymentType 支付类型
     * @param array $callbackData 回调数据
     * @param array $config 配置信息
     * @return array
     */
    public function handleAlipayServiceCallback(string $paymentType, array $callbackData, array $config = []): array
    {
        try {
            // 获取支付方式映射
            $mapping = $this->getPaymentMapping($paymentType);
            if (!$mapping || $mapping['gateway'] !== 'alipay') {
                throw new \Exception("不支持的支付宝服务商回调类型: {$paymentType}");
            }

            // 获取商户配置 - 兼容多种键名格式
            $merchantId = isset($config['merchant_id']) ? (int)$config['merchant_id'] : (isset($config['mer_id']) ? (int)$config['mer_id'] : 0);
            $merchantConfig = $this->getMerchantConfig($merchantId, 'alipay');

            // 创建支付宝网关实例
            $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create('alipay', $merchantConfig);

            // 处理服务商回调
            $result = $gateway->handleServiceCallback($callbackData);

            // 记录回调日志
            $this->logPaymentAction('service_callback', $paymentType, $callbackData, $result);

            return $result;

        } catch (\Exception $e) {
            $this->logError('处理支付宝服务商回调失败', [
                'payment_type' => $paymentType,
                'callback_data' => $callbackData,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 支付宝服务商退款
     * @param string $paymentType 支付类型
     * @param string $transactionId 交易ID
     * @param float $amount 退款金额
     * @param array $config 配置信息
     * @return array
     */
    public function alipayServiceRefund(string $paymentType, string $transactionId, float $amount, array $config = []): array
    {
        try {
            // 获取支付方式映射
            $mapping = $this->getPaymentMapping($paymentType);
            if (!$mapping || $mapping['gateway'] !== 'alipay') {
                throw new \Exception("不支持的支付宝服务商退款类型: {$paymentType}");
            }

            // 获取商户配置 - 兼容多种键名格式
            $merchantId = isset($config['merchant_id']) ? (int)$config['merchant_id'] : (isset($config['mer_id']) ? (int)$config['mer_id'] : 0);
            $merchantConfig = $this->getMerchantConfig($merchantId, 'alipay');

            // 创建支付宝网关实例
            $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create('alipay', $merchantConfig);

            // 执行服务商退款
            $result = $gateway->serviceRefund($transactionId, $amount, $config);

            // 记录退款日志
            $this->logPaymentAction('service_refund', $paymentType, [
                'transaction_id' => $transactionId,
                'amount' => $amount
            ], $result);

            return $result;

        } catch (\Exception $e) {
            $this->logError('支付宝服务商退款失败', [
                'payment_type' => $paymentType,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 支付宝服务商分账
     * @param string $paymentType 支付类型
     * @param string $transactionId 交易ID
     * @param array $royaltyInfo 分账信息
     * @param array $config 配置信息
     * @return array
     */
    public function alipayServiceRoyalty(string $paymentType, string $transactionId, array $royaltyInfo, array $config = []): array
    {
        try {
            // 获取支付方式映射
            $mapping = $this->getPaymentMapping($paymentType);
            if (!$mapping || $mapping['gateway'] !== 'alipay') {
                throw new \Exception("不支持的支付宝服务商分账类型: {$paymentType}");
            }

            // 获取商户配置 - 兼容多种键名格式
            $merchantId = isset($config['merchant_id']) ? (int)$config['merchant_id'] : (isset($config['mer_id']) ? (int)$config['mer_id'] : 0);
            $merchantConfig = $this->getMerchantConfig($merchantId, 'alipay');

            // 创建支付宝网关实例
            $gateway = \crmeb\services\payment\factories\PaymentGatewayFactory::create('alipay', $merchantConfig);

            // 执行服务商分账
            $result = $gateway->serviceRoyalty($transactionId, $royaltyInfo);

            // 记录分账日志
            $this->logPaymentAction('service_royalty', $paymentType, [
                'transaction_id' => $transactionId,
                'royalty_info' => $royaltyInfo
            ], $result);

            return $result;

        } catch (\Exception $e) {
            $this->logError('支付宝服务商分账失败', [
                'payment_type' => $paymentType,
                'transaction_id' => $transactionId,
                'royalty_info' => $royaltyInfo,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取可用支付方式列表 - 统一支付架构版本
     * @param array $params 请求参数
     * @return array
     */
    public function getAvailablePaymentMethods(array $params = []): array
    {
        try {
            // 获取参数
            $platform = $params['platform'] ?? 'h5';
            $merchantId = $params['merchant_id'] ?? 0;
            $userId = $params['user_id'] ?? 0;
            $amount = $params['amount'] ?? 0;
            $orderType = $params['order_type'] ?? 'store_order';
            
            Log::info('[UnifiedPayment] 获取可用支付方式 - 开始处理', [
                'platform' => $platform,
                'merchant_id' => $merchantId,
                'order_type' => $orderType,
                'amount' => $amount,
                'user_id' => $userId
            ]);

            // 从配置服务获取启用的支付方式
            $configService = app(\crmeb\services\payment\config\PaymentMethodConfigService::class);
            $enabledMethods = $configService->getEnabledPaymentMethods();

            Log::info('[UnifiedPayment] 获取启用的支付方式', [
                'enabled_methods_count' => count($enabledMethods),
                'enabled_methods' => array_column($enabledMethods, 'code')
            ]);

            $paymentMethods = [];

            foreach ($enabledMethods as $method) {
                $paymentType = $method['code'];
                
                Log::info('[UnifiedPayment] 处理支付方式', [
                    'payment_type' => $paymentType,
                    'method_data' => $method
                ]);
                
                // 检查是否为统一支付架构支持的类型
                if (!$this->isPaymentTypeSupported($paymentType)) {
                    Log::info('[UnifiedPayment] 支付方式不被支持，跳过', [
                        'payment_type' => $paymentType
                    ]);
                    continue;
                }

                // 检查平台适配
                if (!$this->isPlatformSupported($paymentType, $platform)) {
                    Log::info('[UnifiedPayment] 平台不支持该支付方式，跳过', [
                        'payment_type' => $paymentType,
                        'platform' => $platform
                    ]);
                    continue;
                }

                // 检查商户配置（如果有指定商户）
                if ($merchantId > 0 && !$this->isMerchantPaymentEnabled($merchantId, $paymentType)) {
                    Log::info('[UnifiedPayment] 商户未启用该支付方式，跳过', [
                        'payment_type' => $paymentType,
                        'merchant_id' => $merchantId
                    ]);
                    continue;
                }

                // 构建支付方式数据
                $paymentMethodData = $this->buildPaymentMethodData($paymentType, $platform, $userId, $amount);
                if ($paymentMethodData) {
                    $paymentMethods[] = $paymentMethodData;
                    Log::info('[UnifiedPayment] 支付方式添加成功', [
                        'payment_type' => $paymentType,
                        'payment_method_data' => $paymentMethodData
                    ]);
                } else {
                    Log::info('[UnifiedPayment] 构建支付方式数据失败，跳过', [
                        'payment_type' => $paymentType
                    ]);
                }
            }

            // 按照排序权重排序
            usort($paymentMethods, function($a, $b) {
                return ($a['sort'] ?? 99) - ($b['sort'] ?? 99);
            });

            Log::info('[UnifiedPayment] 获取支付方式成功 - 最终结果', [
                'count' => count($paymentMethods),
                'methods' => array_column($paymentMethods, 'type'),
                'detailed_methods' => $paymentMethods
            ]);

            return $paymentMethods;

        } catch (\Exception $e) {
            Log::error('[UnifiedPayment] 获取支付方式失败: ' . $e->getMessage(), [
                'params' => $params,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * 检查平台是否支持该支付方式
     * @param string $paymentType 支付类型
     * @param string $platform 平台
     * @return bool
     */
    protected function isPlatformSupported(string $paymentType, string $platform): bool
    {
        // 平台支付方式适配规则 - 修复：使用数据库中实际的支付类型
        $platformRules = [
            'wechat_mini' => ['wechat', 'routine', 'weixin', 'balance', 'offline'], // 微信小程序：微信支付、小程序支付、余额、线下
            'alipay_mini' => ['alipay', 'alipay_mini', 'balance', 'offline'], // 支付宝小程序：支付宝、支付宝小程序、余额、线下  
            'h5' => ['wechat', 'alipay', 'weixin', 'routine', 'weixinApp', 'alipayApp', 'h5', 'balance', 'offline'], // H5：全支持
            'wechat_h5' => ['wechat', 'weixin', 'alipay', 'balance', 'offline'], // 微信内H5：微信优先
            'app' => ['weixinApp', 'alipayApp', 'wechat', 'alipay', 'balance', 'offline'], // APP：APP专用支付优先
            'uni_app' => ['wechat', 'alipay', 'weixin', 'routine', 'weixinApp', 'alipayApp', 'balance', 'offline'] // uni-app：全支持
        ];

        $supportedTypes = $platformRules[$platform] ?? $platformRules['h5'];
        
        // 添加调试日志
        Log::info('[UnifiedPayment] 平台支付方式检查', [
            'payment_type' => $paymentType,
            'platform' => $platform,
            'supported_types' => $supportedTypes,
            'is_supported' => in_array($paymentType, $supportedTypes)
        ]);
        
        return in_array($paymentType, $supportedTypes);
    }

    /**
     * 检查商户是否启用该支付方式
     * @param int $merchantId 商户ID
     * @param string $paymentType 支付类型  
     * @return bool
     */
    protected function isMerchantPaymentEnabled(int $merchantId, string $paymentType): bool
    {
        try {
            // 这里可以添加商户级别的支付方式配置检查
            // 暂时返回true，表示所有商户都支持所有启用的支付方式
            return true;
        } catch (\Exception $e) {
            Log::warning('[UnifiedPayment] 检查商户支付方式失败: ' . $e->getMessage());
            return true; // 默认允许
        }
    }

    /**
     * 构建支付方式数据 - 与前端组件格式兼容
     * @param string $paymentType 支付类型
     * @param string $platform 平台
     * @param int $userId 用户ID
     * @param float $amount 金额
     * @return array|null
     */
    protected function buildPaymentMethodData(string $paymentType, string $platform, int $userId, float $amount): ?array
    {
        // 支付方式基础信息映射
        $paymentInfo = [
            'weixin' => [
                'name' => '微信支付',
                'title' => '微信安全支付', 
                'icon' => 'icon-weixinzhifu',
                'sort' => 1
            ],
            'routine' => [
                'name' => '微信支付',
                'title' => '微信小程序支付',
                'icon' => 'icon-weixinzhifu', 
                'sort' => 1
            ],
            'alipay' => [
                'name' => '支付宝支付',
                'title' => '支付宝安全支付',
                'icon' => 'icon-zhifubaozhifu',
                'sort' => 2
            ],
            'alipay_mini' => [
                'name' => '支付宝支付', 
                'title' => '支付宝小程序支付',
                'icon' => 'icon-zhifubaozhifu',
                'sort' => 2
            ],
            'h5' => [
                'name' => 'H5支付',
                'title' => 'H5网页支付',
                'icon' => 'icon-h5zhifu',
                'sort' => 3
            ],
            'balance' => [
                'name' => '余额支付',
                'title' => '账户余额支付', 
                'icon' => 'icon-yue',
                'sort' => 10
            ],
            'offline' => [
                'name' => '线下支付',
                'title' => '线下转账支付',
                'icon' => 'icon-xianxiazhifu',
                'sort' => 20
            ]
        ];

        $info = $paymentInfo[$paymentType] ?? null;
        if (!$info) {
            return null;
        }

        // 构建基础数据
        $methodData = [
            'name' => $info['name'],
            'title' => $info['title'],
            'type' => $paymentType,      // 前端识别的支付类型
            'value' => $paymentType,     // 前端备用标识
            'icon' => $info['icon'],     // 前端图标类名
            'icon_url' => '',           // 自定义图标URL
            'payStatus' => true,        // 默认可用
            'sort' => $info['sort']     // 排序权重
        ];

        // 特殊处理余额支付
        if ($paymentType === 'balance' && $userId > 0) {
            $userBalance = $this->getUserBalance($userId);
            $methodData['number'] = $userBalance; // 显示可用余额
            $methodData['payStatus'] = $userBalance >= $amount; // 余额是否足够
        }

        // 平台特殊适配处理
        if ($platform === 'wechat_mini' && $paymentType === 'weixin') {
            // 微信小程序环境，前端会自动转换为routine
            $methodData['type'] = 'weixin'; // 保持weixin，前端组件会处理
        }

        if ($platform === 'alipay_mini' && $paymentType === 'alipay') {
            // 支付宝小程序环境，前端会自动转换
            $methodData['type'] = 'alipay'; // 保持alipay，前端组件会处理
        }

        return $methodData;
    }

    /**
     * 获取用户余额
     * @param int $userId 用户ID
     * @return float
     */
    protected function getUserBalance(int $userId): float
    {
        try {
            $userRepository = app(\app\common\repositories\user\UserRepository::class);
            $user = $userRepository->get($userId);
            return $user ? floatval($user->now_money) : 0.0;
        } catch (\Exception $e) {
            Log::warning('[UnifiedPayment] 获取用户余额失败: ' . $e->getMessage());
            return 0.0;
        }
    }

    /**
     * 检测平台类型 - 统一的平台检测逻辑
     * @param array $headers HTTP头信息
     * @return string
     */
    public function detectPlatform(array $headers = []): string
    {
        $userAgent = $headers['user-agent'] ?? '';
        
        if (strpos($userAgent, 'MicroMessenger') !== false) {
            if (strpos($userAgent, 'miniProgram') !== false) {
                return 'wechat_mini';
            }
            return 'wechat_h5';
        }
        
        if (strpos($userAgent, 'AlipayClient') !== false) {
            return 'alipay_mini';
        }
        
        if (strpos($userAgent, 'uni-app') !== false) {
            return 'uni_app';
        }
        
        return 'h5';
    }

    /**
     * 统一支付方式映射 - 供所有业务模块使用（使用统一映射服务）
     * 将前端支付类型映射为统一支付架构的方法名
     * 
     * @param string $paymentType 前端支付类型
     * @return string 统一支付架构方法名
     */
    public static function mapPaymentMethod(string $paymentType): string
    {
        // 先使用统一的支付类型标准化
        $standardType = PaymentMethodConfigService::normalizePaymentType($paymentType);
        
        // 统一的支付方式映射配置
        $mapping = [
            // 微信支付类型
            'weixin' => 'jsapi',           // 微信公众号支付
            'weixinApp' => 'app',          // 微信APP支付
            'routine' => 'miniprogram',    // 微信小程序支付
            'h5' => 'h5',                  // H5支付
            'weixinQr' => 'qr',           // 微信扫码支付
            'weixinBarCode' => 'barcode',  // 微信条码支付
            
            // 支付宝支付类型
            'alipay' => 'web',             // 支付宝网站支付
            'alipayApp' => 'app',          // 支付宝APP支付
            'alipayQr' => 'qr',           // 支付宝扫码支付
            'alipayBarCode' => 'barcode',  // 支付宝条码支付
            'alipay_mini' => 'miniprogram', // 支付宝小程序支付
            
            // 其他支付类型
            'balance' => 'balance',        // 余额支付
            'offline' => 'offline',        // 线下支付
            'api' => 'api',               // API支付
            
            // 特殊支付类型
            'points' => 'points',         // 积分支付
            'barcode' => 'barcode',       // 通用条码支付
            'qr' => 'qr'                  // 通用扫码支付
        ];

        // 先查找直接映射，再查找标准类型映射
        $result = $mapping[$paymentType] ?? $mapping[$standardType] ?? 'jsapi';
        
        // 记录映射日志（调试用）
        if ($paymentType !== $result && class_exists('\think\facade\Log')) {
            try {
                \think\facade\Log::debug('[UnifiedPayment] 支付方式映射', [
                    'input' => $paymentType,
                    'standard_type' => $standardType,
                    'output' => $result
                ]);
            } catch (\Exception $e) {
                // 静默处理日志错误，不影响主功能
            }
        }
        
        return $result;
    }

    /**
     * 获取支付方式映射配置
     * 返回完整的映射关系，供其他服务查询使用
     * 
     * @return array 完整的映射配置
     */
    public static function getPaymentMethodMappings(): array
    {
        return [
            'weixin' => 'jsapi',
            'weixinApp' => 'app',
            'routine' => 'miniprogram',
            'h5' => 'h5',
            'weixinQr' => 'qr',
            'weixinBarCode' => 'barcode',
            'alipay' => 'web',
            'alipayApp' => 'app',
            'alipayQr' => 'qr',
            'alipayBarCode' => 'barcode',
            'alipay_mini' => 'miniprogram',
            'balance' => 'balance',
            'offline' => 'offline',
            'api' => 'api',
            'points' => 'points',
            'barcode' => 'barcode',
            'qr' => 'qr'
        ];
    }

    /**
     * 反向映射：从统一架构方法名获取前端支付类型
     * 
     * @param string $method 统一架构方法名
     * @return array 对应的前端支付类型列表
     */
    public static function reverseMapPaymentMethod(string $method): array
    {
        $mappings = self::getPaymentMethodMappings();
        $result = [];
        
        foreach ($mappings as $paymentType => $mappedMethod) {
            if ($mappedMethod === $method) {
                $result[] = $paymentType;
            }
        }
        
        return $result;
    }

    /**
     * 验证支付方式是否支持
     * 
     * @param string $paymentType 支付类型
     * @return bool 是否支持
     */
    public static function isPaymentMethodSupported(string $paymentType): bool
    {
        return array_key_exists($paymentType, self::getPaymentMethodMappings());
    }
} 