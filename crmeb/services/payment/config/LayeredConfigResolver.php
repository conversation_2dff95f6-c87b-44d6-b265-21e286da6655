<?php

namespace crmeb\services\payment\config;

use think\facade\Db;
use think\facade\Log;

/**
 * 分层配置解析器
 * 统一解析支付配置，自动处理不同模式和层级
 */
class LayeredConfigResolver
{
    /**
     * 解析支付配置
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @return array 最终配置
     */
    public function resolveConfig(int $merId, string $paymentType): array
    {
        try {
            // 1. 确定支付模式
            $paymentMode = $this->determinePaymentMode($merId, $paymentType);
            
            // 2. 获取模式对应的配置层级
            $configLayers = PaymentModeEnum::getConfigLayers($paymentMode);
            
            // 3. 逐层合并配置
            $finalConfig = [];
            
            // 基础配置层
            if (isset($configLayers['base'])) {
                $baseConfig = $this->getBaseConfig($configLayers['base']);
                $finalConfig = array_merge($finalConfig, $baseConfig);
            }
            
            // 服务商配置层
            if (isset($configLayers['service'])) {
                $serviceConfig = $this->getServiceConfig($configLayers['service']);
                $finalConfig = array_merge($finalConfig, $serviceConfig);
            }
            
            // 商户配置层
            if (isset($configLayers['merchant'])) {
                $merchantConfig = $this->getMerchantConfig($merId, $configLayers['merchant']);
                $finalConfig = array_merge($finalConfig, $merchantConfig);
            }
            
            // 4. 添加元数据
            $finalConfig['_meta'] = [
                'payment_mode' => $paymentMode,
                'mer_id' => $merId,
                'payment_type' => $paymentType,
                'config_layers' => array_keys($configLayers),
                'generated_at' => time()
            ];
            
            return $finalConfig;
            
        } catch (\Exception $e) {
            Log::error('解析支付配置失败', [
                'mer_id' => $merId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 确定支付模式
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @return string 支付模式
     */
    public function determinePaymentMode(int $merId, string $paymentType): string
    {
        // 检查系统是否开启服务商模式
        $wechatServiceEnabled = systemConfig('open_wx_sub_mch');
        $alipayServiceEnabled = systemConfig('open_alipay_sub_mch');
        
        // 获取商户信息
        $merchant = $this->getMerchantInfo($merId);
        
        if (strpos($paymentType, 'wechat') !== false || strpos($paymentType, 'weixin') !== false || strpos($paymentType, 'routine') !== false) {
            if ($wechatServiceEnabled && !empty($merchant['sub_mchid'])) {
                return PaymentModeEnum::WECHAT_ECOMMERCE; // 电商收付通
            } elseif ($wechatServiceEnabled) {
                return PaymentModeEnum::WECHAT_SERVICE;   // 服务商模式
            } else {
                return PaymentModeEnum::WECHAT_DIRECT;    // 直连模式
            }
        }
        
        if (strpos($paymentType, 'alipay') !== false) {
            if ($alipayServiceEnabled && !empty($merchant['alipay_smid'])) {
                return PaymentModeEnum::ALIPAY_ZHIFU;     // 直付通
            } elseif ($alipayServiceEnabled) {
                return PaymentModeEnum::ALIPAY_SERVICE;   // 服务商模式
            } else {
                return PaymentModeEnum::ALIPAY_DIRECT;    // 直连模式
            }
        }
        
        if ($paymentType === 'balance') {
            return PaymentModeEnum::BALANCE_DIRECT;
        }
        
        if ($paymentType === 'offline') {
            return PaymentModeEnum::OFFLINE_DIRECT;
        }
        
        return PaymentModeEnum::WECHAT_DIRECT; // 默认
    }
    
    /**
     * 获取基础配置
     * @param string $configType 配置类型
     * @return array 基础配置
     */
    private function getBaseConfig(string $configType): array
    {
        $config = [];
        $mapping = LayeredPaymentFields::getSystemConfigMapping($configType);
        
        foreach ($mapping as $field => $systemKey) {
            $value = systemConfig($systemKey);
            if (!empty($value)) {
                $config[$field] = $value;
            }
        }
        
        return $config;
    }
    
    /**
     * 获取服务商配置
     * @param string $serviceType 服务商类型
     * @return array 服务商配置
     */
    private function getServiceConfig(string $serviceType): array
    {
        $config = [];
        $mapping = LayeredPaymentFields::getSystemConfigMapping($serviceType);
        
        foreach ($mapping as $field => $systemKey) {
            $value = systemConfig($systemKey);
            if (!empty($value)) {
                $config[$field] = $value;
            }
        }
        
        return $config;
    }
    
    /**
     * 获取商户配置
     * @param int $merId 商户ID
     * @param string $merchantType 商户类型
     * @return array 商户配置
     */
    private function getMerchantConfig(int $merId, string $merchantType): array
    {
        $config = [];
        
        if ($merchantType === 'wechat_merchant') {
            $merchant = $this->getMerchantInfo($merId);
            if (!empty($merchant['sub_mchid'])) {
                $config['sub_mch_id'] = $merchant['sub_mchid'];
            }
            
            // 获取商户专用微信配置
            $merchantConfig = $this->getMerchantPaymentConfig($merId, 'wechat');
            if ($merchantConfig) {
                $config = array_merge($config, $merchantConfig);
            }
            
        } elseif ($merchantType === 'alipay_merchant') {
            $merchant = $this->getMerchantInfo($merId);
            if (!empty($merchant['alipay_smid'])) {
                $config['alipay_smid'] = $merchant['alipay_smid'];
            }
            
            // 获取支付宝授权信息
            $authInfo = $this->getAlipayAuthInfo($merId);
            if ($authInfo) {
                $config['app_auth_token'] = $authInfo['app_auth_token'];
                $config['auth_app_id'] = $authInfo['auth_app_id'];
                $config['refresh_token'] = $authInfo['refresh_token'];
            }
        }
        
        return array_filter($config);
    }
    
    /**
     * 获取商户信息
     * @param int $merId 商户ID
     * @return array 商户信息
     */
    private function getMerchantInfo(int $merId): array
    {
        try {
            return Db::name('merchant')
                ->where('mer_id', $merId)
                ->field('mer_id,mer_name,sub_mchid,alipay_smid,alipay_auth_token')
                ->find() ?: [];
        } catch (\Exception $e) {
            Log::warning('获取商户信息失败', [
                'mer_id' => $merId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * 获取商户支付配置
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @return array 支付配置
     */
    private function getMerchantPaymentConfig(int $merId, string $paymentType): array
    {
        try {
            $configRecord = Db::name('merchant_payment_config')
                ->where('mer_id', $merId)
                ->where('payment_type', $paymentType)
                ->where('status', 1)
                ->find();
            
            if (!$configRecord) {
                return [];
            }
            
            $params = Db::name('merchant_payment_params')
                ->where('mer_id', $merId)
                ->where('payment_id', $configRecord['id'])
                ->column('param_value', 'param_name');
            
            return $params ?: [];
        } catch (\Exception $e) {
            Log::warning('获取商户支付配置失败', [
                'mer_id' => $merId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * 获取支付宝授权信息
     * @param int $merId 商户ID
     * @return array|null 授权信息
     */
    private function getAlipayAuthInfo(int $merId): ?array
    {
        try {
            return Db::name('merchant_alipay_auth')
                ->where('mer_id', $merId)
                ->where('status', 1)
                ->find();
        } catch (\Exception $e) {
            Log::warning('获取支付宝授权信息失败', [
                'mer_id' => $merId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 验证配置完整性
     * @param array $config 配置数组
     * @param string $paymentMode 支付模式
     * @return array 验证结果
     */
    public function validateConfig(array $config, string $paymentMode): array
    {
        $configLayers = PaymentModeEnum::getConfigLayers($paymentMode);
        $missingFields = [];
        
        foreach ($configLayers as $layer => $configType) {
            $fields = LayeredPaymentFields::getConfigTypeFields($configType);
            
            foreach ($fields as $fieldName => $fieldDef) {
                if ($fieldDef['required'] && empty($config[$fieldName])) {
                    $missingFields[] = [
                        'layer' => $layer,
                        'field' => $fieldName,
                        'label' => $fieldDef['label']
                    ];
                }
            }
        }
        
        return [
            'valid' => empty($missingFields),
            'missing_fields' => $missingFields
        ];
    }
}
