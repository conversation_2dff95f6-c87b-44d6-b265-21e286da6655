<?php

namespace crmeb\services\payment\config;

use think\facade\Cache;
use think\facade\Log;

/**
 * 支付配置中心 - 完全重构版本
 * 基于分层架构，移除所有冗余代码
 */
class PaymentConfigCenter
{
    const CACHE_PREFIX = 'payment_config_center_';
    const CACHE_EXPIRE = 3600;

    /**
     * @var LayeredConfigResolver
     */
    private static $resolver;

    /**
     * 获取配置解析器实例
     * @return LayeredConfigResolver
     */
    private static function getResolver(): LayeredConfigResolver
    {
        if (!self::$resolver) {
            self::$resolver = new LayeredConfigResolver();
        }
        return self::$resolver;
    }

    /**
     * 获取有效配置（主入口方法）
     * @param int $merchantId 商户ID
     * @param string $paymentType 支付类型
     * @param string $environment 环境类型（保留兼容性）
     * @return array|null
     */
    public static function getEffectiveConfig(int $merchantId, string $paymentType, string $environment = 'production'): ?array
    {
        $cacheKey = self::CACHE_PREFIX . "effective_{$merchantId}_{$paymentType}";
        
        // 尝试从缓存获取
        $config = Cache::get($cacheKey);
        if ($config !== null) {
            return $config;
        }
        
        try {
            // 使用分层解析器获取配置
            $resolver = self::getResolver();
            $config = $resolver->resolveConfig($merchantId, $paymentType);
            
            if ($config) {
                // 移除元数据，只返回配置字段
                unset($config['_meta']);
                
                // 缓存配置
                Cache::set($cacheKey, $config, self::CACHE_EXPIRE);
                
                Log::info('支付配置解析成功', [
                    'mer_id' => $merchantId,
                    'payment_type' => $paymentType
                ]);
            }
            
            return $config;
            
        } catch (\Exception $e) {
            Log::error('支付配置解析失败', [
                'mer_id' => $merchantId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * 获取商户支付配置
     * @param int $merchantId 商户ID
     * @param string $paymentType 支付类型
     * @param string $environment 环境类型（保留兼容性）
     * @return array|null
     */
    public static function getMerchantConfig(int $merchantId, string $paymentType, string $environment = 'production'): ?array
    {
        return self::getEffectiveConfig($merchantId, $paymentType, $environment);
    }

    /**
     * 获取系统默认配置
     * @param string $paymentType 支付类型
     * @param string $environment 环境类型（保留兼容性）
     * @return array|null
     */
    public static function getSystemConfig(string $paymentType, string $environment = 'production'): ?array
    {
        // 系统配置使用商户ID=0
        return self::getEffectiveConfig(0, $paymentType, $environment);
    }

    /**
     * 获取配置模板
     * @param string $paymentType 支付类型
     * @param int $merId 商户ID（用于确定模式）
     * @return array
     */
    public static function getConfigTemplate(string $paymentType, int $merId = 0): array
    {
        try {
            // 确定支付模式
            $resolver = self::getResolver();
            $paymentMode = $resolver->determinePaymentMode($merId, $paymentType);
            $configLayers = PaymentModeEnum::getConfigLayers($paymentMode);
            
            $template = [];
            
            foreach ($configLayers as $layer => $configType) {
                $fields = LayeredPaymentFields::getConfigTypeFields($configType);
                
                foreach ($fields as $fieldName => $fieldDef) {
                    $template[$fieldName] = [
                        'type' => $fieldDef['type'],
                        'required' => $fieldDef['required'],
                        'description' => $fieldDef['label'],
                        'layer' => $layer
                    ];
                }
            }
            
            return $template;
            
        } catch (\Exception $e) {
            Log::error('获取配置模板失败', [
                'payment_type' => $paymentType,
                'mer_id' => $merId,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * 验证配置
     * @param string $paymentType 支付类型
     * @param array $config 配置数据
     * @param int $merId 商户ID
     * @return bool
     */
    public static function validateConfig(string $paymentType, array $config, int $merId = 0): bool
    {
        try {
            $resolver = self::getResolver();
            $paymentMode = $resolver->determinePaymentMode($merId, $paymentType);
            $validation = $resolver->validateConfig($config, $paymentMode);
            
            return $validation['valid'];
            
        } catch (\Exception $e) {
            Log::error('配置验证失败', [
                'payment_type' => $paymentType,
                'mer_id' => $merId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 检查配置是否完整
     * @param string $paymentType 支付类型
     * @param array $config 配置数据
     * @param int $merId 商户ID
     * @return bool
     */
    public static function isConfigComplete(string $paymentType, array $config, int $merId = 0): bool
    {
        return self::validateConfig($paymentType, $config, $merId);
    }

    /**
     * 获取支付模式信息
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @return array
     */
    public static function getPaymentModeInfo(int $merId, string $paymentType): array
    {
        try {
            $resolver = self::getResolver();
            $paymentMode = $resolver->determinePaymentMode($merId, $paymentType);
            $configLayers = PaymentModeEnum::getConfigLayers($paymentMode);
            
            return [
                'payment_mode' => $paymentMode,
                'mode_name' => PaymentModeEnum::getModeDisplayName($paymentMode),
                'config_layers' => $configLayers,
                'is_service_mode' => in_array($paymentMode, [
                    PaymentModeEnum::WECHAT_SERVICE,
                    PaymentModeEnum::WECHAT_ECOMMERCE,
                    PaymentModeEnum::ALIPAY_SERVICE,
                    PaymentModeEnum::ALIPAY_ZHIFU
                ])
            ];
        } catch (\Exception $e) {
            Log::error('获取支付模式信息失败', [
                'mer_id' => $merId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            
            return [
                'payment_mode' => PaymentModeEnum::getDefaultMode($paymentType),
                'mode_name' => '未知模式',
                'config_layers' => [],
                'is_service_mode' => false
            ];
        }
    }

    /**
     * 清除配置缓存
     * @param int|null $merchantId 商户ID
     * @param string|null $paymentType 支付类型
     */
    public static function clearCache(?int $merchantId = null, ?string $paymentType = null): void
    {
        if ($merchantId === null && $paymentType === null) {
            // 清除所有缓存
            Cache::clear();
        } elseif ($merchantId !== null && $paymentType !== null) {
            // 清除特定缓存
            $cacheKey = self::CACHE_PREFIX . "effective_{$merchantId}_{$paymentType}";
            Cache::delete($cacheKey);
        } else {
            // 清除相关缓存
            Cache::clear();
        }
    }

    /**
     * 获取支持的支付类型和模式
     * @return array
     */
    public static function getSupportedPaymentModes(): array
    {
        $modes = [];
        
        foreach (PaymentModeEnum::getAllModes() as $mode) {
            $modes[$mode] = [
                'mode' => $mode,
                'name' => PaymentModeEnum::getModeDisplayName($mode),
                'layers' => PaymentModeEnum::getConfigLayers($mode)
            ];
        }
        
        return $modes;
    }
}
