<?php

namespace crmeb\services\payment\config;

use think\facade\Cache;
use think\facade\Log;

/**
 * 支付配置中心 - 基于分层架构重构
 * 与统一支付服务集成，直接使用网关字段
 */
class PaymentConfigCenter
{
    const CACHE_PREFIX = 'payment_config_center_';
    const CACHE_EXPIRE = 3600;

    /**
     * @var LayeredConfigResolver
     */
    private static $resolver;

    /**
     * 获取配置解析器实例
     * @return LayeredConfigResolver
     */
    private static function getResolver(): LayeredConfigResolver
    {
        if (!self::$resolver) {
            self::$resolver = new LayeredConfigResolver();
        }
        return self::$resolver;
    }
    
    /**
     * 支持的支付类型映射
     */
    private static $supportedPaymentTypes = [
        'wechat' => '微信支付',
        'alipay' => '支付宝',
        'routine' => '小程序支付',
        'h5' => 'H5支付',
        'weixinQr' => '微信扫码支付',
        'alipayQr' => '支付宝扫码支付',
        'weixinApp' => '微信APP支付',
        'alipayApp' => '支付宝APP支付',
        'alipaymini' => '支付宝小程序支付',
        'weixinEcom' => '微信电商收付通'
    ];

    /**
     * 终端类型映射
     */
    private static $terminalTypes = [
        'h5' => 'h5',
        'app' => 'app',
        'mini' => 'mini',
        'pc' => 'pc',
        'wechat' => 'wechat',
        'alipay' => 'alipay'
    ];

    /**
     * 系统配置键映射
     */
    private static $systemConfigMapping = [
        // 微信支付相关配置
        'wechat' => [
            // 普通支付配置
            'app_id' => 'wechat_appid',
            'mch_id' => 'wechat_mchid', 
            'key' => 'wechat_key',
            'cert_path' => 'wechat_cert_path',
            'key_path' => 'wechat_key_path',
            
            // 服务商模式配置
            'service_mode' => 'wechat_service_mode',
            'service_app_id' => 'wechat_service_appid',
            'service_mch_id' => 'wechat_service_merid',
            'service_key' => 'wechat_service_key',
            'service_v3_key' => 'wechat_service_v3key',
            'service_cert_path' => 'wechat_service_client_cert',
            'service_key_path' => 'wechat_service_client_key',
            'service_serial_no' => 'wechat_service_serial_no',
            
            // 电商收付通配置
            'ecommerce_mode' => 'open_wx_sub_mch',
            'ecommerce_mch_id' => 'wechat_service_merid',
            'ecommerce_key' => 'wechat_service_key',
            'ecommerce_v3_key' => 'wechat_service_v3key',
            'ecommerce_cert_path' => 'wechat_service_client_cert',
            'ecommerce_key_path' => 'wechat_service_client_key',
            'ecommerce_serial_no' => 'wechat_service_serial_no',
            
            // V3 API配置
            'v3_key' => 'wechat_v3_key',
            'serial_no' => 'wechat_serial_no',
            'platform_cert' => 'wechat_platform_cert'
        ],
        
        // 支付宝相关配置
        'alipay' => [
            // 普通支付基本配置
            'app_id' => 'alipay_app_id',
            'private_key' => 'alipay_private_key',
            'public_key' => 'alipay_public_key',
            
            // 普通支付证书模式
            'cert_mode' => 'alipay_cert_mode',
            'app_cert' => 'alipay_app_public_cert_path',
            'public_cert' => 'alipay_public_cert_path',
            'root_cert' => 'alipay_root_cert_path',
            
            // 服务商模式基本配置
            'service_mode' => 'alipay_service_mode',
            'multi_merchant_mode' => 'alipay_multi_merchant_mode',
            'auth_redirect_uri' => 'alipay_auth_redirect_uri',
            'service_pid' => 'alipay_service_pid',
            
            // 支付宝直付通配置
            'open_sub_mch' => 'open_alipay_sub_mch',
            'service_app_id' => 'alipay_service_app_id',
            'service_private_key' => 'alipay_service_private_key',
            'service_public_key' => 'alipay_service_public_key',
            
            // 直付通证书模式
            'service_cert_mode' => 'alipay_service_cert_mode',
            'service_app_cert' => 'alipay_service_app_cert',
            'service_public_cert' => 'alipay_service_public_cert',
            'service_root_cert' => 'alipay_service_root_cert'
        ],
        
        // 小程序支付配置
        'routine' => [
            'app_id' => 'routine_appId',
            'mch_id' => 'pay_routine_mchid',
            'key' => 'pay_routine_key',
            'cert_path' => 'pay_routine_client_cert',
            'key_path' => 'pay_routine_client_key',
            'v3_key' => 'pay_routine_v3_key',
            'serial_no' => 'pay_routine_serial_no_v3',
            'new_mch_id' => 'pay_routine_new_mchid',
            'api_version' => 'pay_routine_type',
            'public_key' => 'pay_routine_public_key',
            'public_key_id' => 'pay_routine_public_id'
        ],
        
        // 支付宝小程序支付配置
        'alipaymini' => [
            'app_id' => 'alipay_mini_app_id',
            'private_key' => 'alipay_mini_private_key',
            'public_key' => 'alipay_mini_public_key',
            'cert_mode' => 'alipay_mini_cert_mode',
            'app_cert' => 'alipay_mini_app_cert',
            'public_cert' => 'alipay_mini_public_cert',
            'root_cert' => 'alipay_mini_root_cert'
        ],
        
        // 微信APP支付配置
        'weixinApp' => [
            'app_id' => 'wechat_app_appid',
            'mch_id' => 'wechat_app_mchid',
            'key' => 'wechat_app_key',
            'cert_path' => 'wechat_app_cert_path',
            'key_path' => 'wechat_app_key_path'
        ],
        
        // 支付宝APP支付配置
        'alipayApp' => [
            'app_id' => 'alipay_app_appid',
            'private_key' => 'alipay_app_private_key',
            'public_key' => 'alipay_app_public_key',
            'cert_mode' => 'alipay_app_cert_mode',
            'app_cert' => 'alipay_app_app_cert',
            'public_cert' => 'alipay_app_public_cert',
            'root_cert' => 'alipay_app_root_cert'
        ],
        
        // 微信电商收付通支付配置
        'weixinEcom' => [
            'ecommerce_mode' => 'open_wx_sub_mch',
            'ecommerce_mch_id' => 'wechat_service_merid',
            'ecommerce_key' => 'wechat_service_key',
            'ecommerce_v3_key' => 'wechat_service_v3key',
            'ecommerce_cert_path' => 'wechat_service_client_cert',
            'ecommerce_key_path' => 'wechat_service_client_key',
            'ecommerce_serial_no' => 'wechat_service_serial_no'
        ]
    ];

    /**
     * 获取商户支付配置（重构版本）
     * @param int $merchantId 商户ID
     * @param string $paymentType 支付类型
     * @param string $environment 环境类型（保留兼容性）
     * @return array|null
     */
    public static function getMerchantConfig(int $merchantId, string $paymentType, string $environment = 'production'): ?array
    {
        return self::getEffectiveConfig($merchantId, $paymentType, $environment);
    }

    /**
     * 设置商户支付配置
     * @param int $merchantId 商户ID
     * @param string $paymentType 支付类型
     * @param array $config 配置数据
     * @param string $environment 环境类型（保留兼容性）
     * @return bool
     */
    public static function setMerchantConfig(int $merchantId, string $paymentType, array $config, string $environment = 'production'): bool
    {
        try {
            if (!self::validateConfig($paymentType, $config)) {
                throw new \Exception('配置验证失败');
            }
            
            $repository = app()->make(MerchantPaymentConfigRepository::class);
            
            // 构建保存数据
            $saveData = [
                'payment_type' => $paymentType,
                'status' => $config['status'] ?? 1,
                'is_default' => $config['is_default'] ?? 0,
                'sort' => $config['sort'] ?? 0,
                'params' => $config
            ];
            
            $result = $repository->save($merchantId, $saveData);
            
            if ($result) {
                // 清除缓存
                $cacheKey = self::CACHE_PREFIX . "merchant_{$merchantId}_{$paymentType}";
                Cache::delete($cacheKey);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('设置商户支付配置失败', [
                'merchant_id' => $merchantId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取有效配置（重构版本 - 使用分层架构）
     * @param int $merchantId 商户ID
     * @param string $paymentType 支付类型
     * @param string $environment 环境类型（保留兼容性）
     * @return array|null
     */
    public static function getEffectiveConfig(int $merchantId, string $paymentType, string $environment = 'production'): ?array
    {
        $cacheKey = self::CACHE_PREFIX . "effective_{$merchantId}_{$paymentType}";

        // 尝试从缓存获取
        $config = Cache::get($cacheKey);
        if ($config !== null) {
            return $config;
        }

        try {
            // 使用分层解析器获取配置
            $resolver = self::getResolver();
            $config = $resolver->resolveConfig($merchantId, $paymentType);

            if ($config) {
                // 移除元数据，只返回配置字段
                unset($config['_meta']);

                // 缓存配置
                Cache::set($cacheKey, $config, self::CACHE_EXPIRE);

                Log::info('支付配置解析成功', [
                    'merchant_id' => $merchantId,
                    'payment_type' => $paymentType
                ]);
            }

            return $config;

        } catch (\Exception $e) {
            Log::error('支付配置解析失败', [
                'merchant_id' => $merchantId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * 获取系统默认配置（重构版本）
     * @param string $paymentType 支付类型
     * @param string $environment 环境类型（保留兼容性）
     * @return array|null
     */
    public static function getSystemConfig(string $paymentType, string $environment = 'production'): ?array
    {
        // 系统配置使用商户ID=0
        return self::getEffectiveConfig(0, $paymentType, $environment);
    }

    /**
     * 获取配置模板（重构版本）
     * @param string $paymentType 支付类型
     * @param int $merId 商户ID（用于确定模式）
     * @return array
     */
    public static function getConfigTemplate(string $paymentType, int $merId = 0): array
    {
        try {
            // 确定支付模式
            $resolver = self::getResolver();
            $paymentMode = $resolver->determinePaymentMode($merId, $paymentType);
            $configLayers = PaymentModeEnum::getConfigLayers($paymentMode);

            $template = [];

            foreach ($configLayers as $layer => $configType) {
                $fields = LayeredPaymentFields::getConfigTypeFields($configType);

                foreach ($fields as $fieldName => $fieldDef) {
                    $template[$fieldName] = [
                        'type' => $fieldDef['type'],
                        'required' => $fieldDef['required'],
                        'description' => $fieldDef['label'],
                        'layer' => $layer
                    ];
                }
            }

            return $template;

        } catch (\Exception $e) {
            Log::error('获取配置模板失败', [
                'payment_type' => $paymentType,
                'mer_id' => $merId,
                'error' => $e->getMessage()
            ]);

            // 降级到简单模板
            return self::getSimpleTemplate($paymentType);
        }
    }

    /**
     * 获取简单模板（降级方案）
     * @param string $paymentType 支付类型
     * @return array
     */
    private static function getSimpleTemplate(string $paymentType): array
    {
        $templates = [
            'wechat' => [
                'appid' => ['type' => 'string', 'required' => true, 'description' => '微信应用ID'],
                'mch_id' => ['type' => 'string', 'required' => true, 'description' => '微信商户号'],
                'key' => ['type' => 'string', 'required' => true, 'description' => '微信商户密钥']
            ],
            'alipay' => [
                'app_id' => ['type' => 'string', 'required' => true, 'description' => '支付宝应用ID'],
                'private_key' => ['type' => 'string', 'required' => true, 'description' => '应用私钥'],
                'public_key' => ['type' => 'string', 'required' => true, 'description' => '支付宝公钥']
            ]
        ];

        return $templates[$paymentType] ?? [];
    }

    /**
     * 验证配置（重构版本）
     * @param string $paymentType 支付类型
     * @param array $config 配置数据
     * @param int $merId 商户ID
     * @return bool
     */
    public static function validateConfig(string $paymentType, array $config, int $merId = 0): bool
    {
        try {
            $resolver = self::getResolver();
            $paymentMode = $resolver->determinePaymentMode($merId, $paymentType);
            $validation = $resolver->validateConfig($config, $paymentMode);

            return $validation['valid'];

        } catch (\Exception $e) {
            Log::error('配置验证失败', [
                'payment_type' => $paymentType,
                'mer_id' => $merId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 检查配置是否完整（重构版本）
     * @param string $paymentType 支付类型
     * @param array $config 配置数据
     * @param int $merId 商户ID
     * @return bool
     */
    public static function isConfigComplete(string $paymentType, array $config, int $merId = 0): bool
    {
        return self::validateConfig($paymentType, $config, $merId);
    }

    /**
     * 获取支持的支付类型
     * @return array
     */
    public static function getSupportedPaymentTypes(): array
    {
        return self::$supportedPaymentTypes;
    }

    /**
     * 清除配置缓存
     * @param int|null $merchantId 商户ID，为null时清除所有
     * @param string|null $paymentType 支付类型，为null时清除所有
     */
    public static function clearCache(?int $merchantId = null, ?string $paymentType = null): void
    {
        if ($merchantId === null && $paymentType === null) {
            // 清除所有支付配置缓存
            $pattern = self::CACHE_PREFIX . '*';
        } elseif ($merchantId !== null && $paymentType !== null) {
            // 清除特定商户和支付类型的缓存
            $pattern = self::CACHE_PREFIX . "merchant_{$merchantId}_{$paymentType}";
            Cache::delete($pattern);
            return;
        } elseif ($merchantId !== null) {
            // 清除特定商户的所有缓存
            $pattern = self::CACHE_PREFIX . "merchant_{$merchantId}_*";
        } else {
            // 清除特定支付类型的所有缓存
            $pattern = self::CACHE_PREFIX . "*_{$paymentType}";
        }
        
        // 简化实现：清除所有相关缓存
        Cache::clear();
    }

    /**
     * 导出配置
     * @param int $merchantId 商户ID
     * @param array $paymentTypes 支付类型数组
     * @return array
     */
    public static function exportConfig(int $merchantId, array $paymentTypes = []): array
    {
        if (empty($paymentTypes)) {
            $paymentTypes = array_keys(self::$supportedPaymentTypes);
        }
        
        $export = [
            'merchant_id' => $merchantId,
            'export_time' => date('Y-m-d H:i:s'),
            'configs' => []
        ];
        
        foreach ($paymentTypes as $paymentType) {
            $config = self::getMerchantConfig($merchantId, $paymentType);
            if ($config) {
                // 敏感信息脱敏
                $config = self::maskSensitiveData($config);
                $export['configs'][$paymentType] = $config;
            }
        }
        
        return $export;
    }

    /**
     * 导入配置
     * @param array $importData 导入数据
     * @return bool
     */
    public static function importConfig(array $importData): bool
    {
        try {
            $merchantId = $importData['merchant_id'] ?? 0;
            $configs = $importData['configs'] ?? [];
            
            if (!$merchantId || empty($configs)) {
                throw new \Exception('导入数据格式错误');
            }
            
            foreach ($configs as $paymentType => $config) {
                self::setMerchantConfig($merchantId, $paymentType, $config);
            }
            
            Log::info('配置导入成功', [
                'merchant_id' => $merchantId,
                'payment_types' => array_keys($configs)
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('配置导入失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 从Repository加载商户配置
     * @param int $merchantId 商户ID
     * @param string $paymentType 支付类型
     * @return array|null
     */
    private static function loadMerchantConfigFromRepository(int $merchantId, string $paymentType): ?array
    {
        try {
            $repository = app()->make(MerchantPaymentConfigRepository::class);
            $config = $repository->getPaymentConfigByType($merchantId, $paymentType);
            
            if (empty($config) || empty($config['params'])) {
                return null;
            }
            
            // 返回参数配置
            return $config['params'];
            
        } catch (\Exception $e) {
            Log::error('加载商户支付配置失败', [
                'merchant_id' => $merchantId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 从systemConfig加载系统配置
     * @param string $paymentType 支付类型
     * @return array|null
     */
    private static function loadSystemConfigFromSystemConfig(string $paymentType): ?array
    {
        try {
            // 获取支付类型对应的配置映射
            $mapping = self::$systemConfigMapping[$paymentType] ?? [];

            // 如果没有找到对应的映射，尝试使用基础支付类型的映射
            if (empty($mapping)) {
                // 从支付类型中提取基础支付类型（例如从weixinApp提取wechat）
                $basePaymentType = self::getBasePaymentType($paymentType);
                if ($basePaymentType && $basePaymentType !== $paymentType) {
                    $mapping = self::$systemConfigMapping[$basePaymentType] ?? [];
                }
                
                if (empty($mapping)) {
                    return null;
                }
            }
            
            // 获取系统中的所有配置键
            $configKeys = array_values($mapping);
            // 批量获取系统配置
            $systemConfigs = systemConfig($configKeys);
            // 检查是否启用了对应的支付方式
            $enableKey = 'enable_' . strtolower($paymentType) . '_pay';
            $isEnabled = systemConfig($enableKey);
            // 如果明确禁用了该支付方式，则返回null
            if ($isEnabled === '0' || $isEnabled === 0 || $isEnabled === false) {
                Log::info("支付方式 {$paymentType} 已被禁用");
                return null;
            }
            
            // 映射回标准键名
            $configs = [];
            foreach ($mapping as $standardKey => $systemKey) {
                if (isset($systemConfigs[$systemKey]) && !empty($systemConfigs[$systemKey])) {
                    $configs[$standardKey] = $systemConfigs[$systemKey];
                }
            }
            // 根据支付类型和配置判断是否需要特殊处理
            $configs = self::processSpecialConfigs($paymentType, $configs, $systemConfigs);
            
            // 验证配置是否有效
            if (!self::isConfigComplete($paymentType, $configs)) {
                Log::warning("支付方式 {$paymentType} 配置不完整", ['config_keys' => array_keys($configs)]);
                return null;
            }
            
            return !empty($configs) ? $configs : null;
            
        } catch (\Exception $e) {
            Log::error('加载系统支付配置失败', [
                'payment_type' => $paymentType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }
    
    /**
     * 获取基础支付类型
     * @param string $paymentType 支付类型
     * @return string|null
     */
    private static function getBasePaymentType(string $paymentType): ?string
    {
        if (strpos($paymentType, 'weixin') === 0 || $paymentType === 'routine') {
            return 'wechat';
        } elseif (strpos($paymentType, 'alipay') === 0) {
            return 'alipay';
        }
        return null;
    }
    
    /**
     * 处理特殊配置
     * @param string $paymentType 支付类型
     * @param array $configs 当前配置
     * @param array $systemConfigs 系统配置
     * @return array 处理后的配置
     */
    private static function processSpecialConfigs(string $paymentType, array $configs, array $systemConfigs): array
    {
        // 处理支付宝证书模式
        if (strpos($paymentType, 'alipay') === 0) {
            // 检查是否启用证书模式
            $certMode = $configs['cert_mode'] ?? false;
            if ($certMode && $certMode !== '0') {
                // 确保证书路径配置存在
                if (empty($configs['app_cert']) || empty($configs['public_cert']) || empty($configs['root_cert'])) {
                    Log::warning("支付宝证书模式启用，但证书配置不完整", [
                        'payment_type' => $paymentType,
                        'cert_fields' => [
                            'app_cert' => !empty($configs['app_cert']),
                            'public_cert' => !empty($configs['public_cert']),
                            'root_cert' => !empty($configs['root_cert'])
                        ]
                    ]);
                }
                
                // 明确设置证书模式为启用
                $configs['cert_mode'] = true;
                
                // 为支付宝SDK准备证书模式配置
                $configs['app_cert_path'] = $configs['app_cert'] ?? '';
                $configs['alipay_cert_path'] = $configs['public_cert'] ?? '';
                $configs['root_cert_path'] = $configs['root_cert'] ?? '';
            } else {
                // 非证书模式，确保公钥存在
                if (empty($configs['public_key'])) {
                    Log::warning("支付宝非证书模式，但公钥配置缺失", [
                        'payment_type' => $paymentType
                    ]);
                }
                
                // 明确设置证书模式为禁用
                $configs['cert_mode'] = false;
            }
            
            // 处理服务商模式
            $serviceMode = $configs['service_mode'] ?? false;
            if ($serviceMode && $serviceMode !== '0') {
                // 确保服务商相关配置存在
                if (empty($configs['service_app_id']) || empty($configs['service_private_key'])) {
                    Log::warning("支付宝服务商模式启用，但服务商配置不完整", [
                        'payment_type' => $paymentType,
                        'service_fields' => [
                            'service_app_id' => !empty($configs['service_app_id']),
                            'service_private_key' => !empty($configs['service_private_key'])
                        ]
                    ]);
                }
                
                // 明确设置服务商模式为启用
                $configs['service_mode'] = true;
                
                // 检查服务商证书模式
                $serviceCertMode = $configs['service_cert_mode'] ?? false;
                if ($serviceCertMode && $serviceCertMode !== '0') {
                    // 确保服务商证书配置存在
                    if (empty($configs['service_app_cert']) || empty($configs['service_public_cert']) || empty($configs['service_root_cert'])) {
                        Log::warning("支付宝服务商证书模式启用，但证书配置不完整", [
                            'payment_type' => $paymentType,
                            'cert_fields' => [
                                'service_app_cert' => !empty($configs['service_app_cert']),
                                'service_public_cert' => !empty($configs['service_public_cert']),
                                'service_root_cert' => !empty($configs['service_root_cert'])
                            ]
                        ]);
                    }
                    
                    // 明确设置服务商证书模式为启用
                    $configs['cert_mode'] = true;
                    $configs['app_cert_path'] = $configs['service_app_cert'] ?? '';
                    $configs['alipay_cert_path'] = $configs['service_public_cert'] ?? '';
                    $configs['root_cert_path'] = $configs['service_root_cert'] ?? '';
                } else {
                    // 明确设置服务商证书模式为禁用
                    $configs['cert_mode'] = false;
                }
            } else {
                // 明确设置服务商模式为禁用
                $configs['service_mode'] = false;
            }
        }
        
        // 处理微信电商收付通
        if ($paymentType === 'weixinEcom' || ($paymentType === 'wechat' && !empty($configs['ecommerce_mode']))) {
            // 检查是否开启电商收付通
            $ecommerceMode = $configs['ecommerce_mode'] ?? false;
            if ($ecommerceMode && $ecommerceMode !== '0') {
                // 明确设置电商收付通模式为启用
                $configs['ecommerce_mode'] = '1';
                $configs['wechat_ecommerce_mode'] = true;
                
                // 确保电商收付通配置存在
                if (empty($configs['ecommerce_mch_id']) || empty($configs['ecommerce_key']) || empty($configs['ecommerce_v3_key'])) {
                    Log::warning("微信电商收付通配置不完整", [
                        'payment_type' => $paymentType,
                        'ecommerce_fields' => [
                            'ecommerce_mch_id' => !empty($configs['ecommerce_mch_id']),
                            'ecommerce_key' => !empty($configs['ecommerce_key']),
                            'ecommerce_v3_key' => !empty($configs['ecommerce_v3_key'])
                        ]
                    ]);
                }
                
                // 检查证书配置
                if (empty($configs['ecommerce_cert_path']) || empty($configs['ecommerce_key_path']) || empty($configs['ecommerce_serial_no'])) {
                    Log::warning("微信电商收付通证书配置不完整", [
                        'payment_type' => $paymentType,
                        'cert_fields' => [
                            'ecommerce_cert_path' => !empty($configs['ecommerce_cert_path']),
                            'ecommerce_key_path' => !empty($configs['ecommerce_key_path']),
                            'ecommerce_serial_no' => !empty($configs['ecommerce_serial_no'])
                        ]
                    ]);
                }
            } else {
                // 明确设置电商收付通模式为禁用
                $configs['ecommerce_mode'] = '0';
                $configs['wechat_ecommerce_mode'] = false;
            }
        }
        
        return $configs;
    }

    /**
     * 敏感数据脱敏
     * @param array $config 配置数据
     * @return array
     */
    private static function maskSensitiveData(array $config): array
    {
        $sensitiveKeys = ['key', 'private_key', 'secret', 'password', 'token', 'mch_id'];
        
        foreach ($config as $key => $value) {
            if (in_array(strtolower($key), $sensitiveKeys)) {
                $config[$key] = self::maskString($value);
            }
        }
        
        return $config;
    }

    /**
     * 字符串脱敏
     * @param string $str 原字符串
     * @return string
     */
    private static function maskString(string $str): string
    {
        $len = strlen($str);
        if ($len <= 8) {
            return str_repeat('*', $len);
        }
        
        return substr($str, 0, 4) . str_repeat('*', $len - 8) . substr($str, -4);
    }

    /**
     * 获取商户配置状态
     * @param int $merchantId 商户ID
     * @param string $paymentType 支付类型
     * @return array
     */
    public static function getMerchantConfigStatus(int $merchantId, string $paymentType): array
    {
        try {
            $repository = app()->make(MerchantPaymentConfigRepository::class);
            $config = $repository->getPaymentConfigByType($merchantId, $paymentType);
            
            return [
                'enabled' => !empty($config) && $config['status'] == 1,
                'is_default' => !empty($config) && $config['is_default'] == 1,
                'sort' => $config['sort'] ?? 0,
                'has_config' => !empty($config['params'])
            ];
            
        } catch (\Exception $e) {
            Log::error('获取商户配置状态失败', [
                'merchant_id' => $merchantId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            
            return [
                'enabled' => false,
                'is_default' => false,
                'sort' => 0,
                'has_config' => false
            ];
        }
    }

    /**
     * 批量获取商户所有支付配置
     * @param int $merchantId 商户ID
     * @return array
     */
    public static function getAllMerchantConfigs(int $merchantId): array
    {
        try {
            $repository = app()->make(MerchantPaymentConfigRepository::class);
            $configs = $repository->getEnabledPaymentsByMerId($merchantId);
            
            $result = [];
            foreach ($configs as $config) {
                $paymentType = $config['payment_type'];
                $result[$paymentType] = [
                    'config' => $config['params'] ?? [],
                    'status' => $config['status'],
                    'is_default' => $config['is_default'],
                    'sort' => $config['sort']
                ];
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('批量获取商户支付配置失败', [
                'merchant_id' => $merchantId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
}
