<?php

namespace crmeb\services\payment\config;

use think\facade\Cache;
use think\facade\Log;

/**
 * 支付配置中心V2 - 基于分层架构重构
 * 统一管理支付配置，支持服务商模式和电商收付通
 */
class PaymentConfigCenterV2
{
    const CACHE_PREFIX = 'payment_config_v2:';
    const CACHE_TTL = 3600;

    /**
     * @var LayeredConfigResolver
     */
    private static $resolver;

    /**
     * 获取配置解析器实例
     * @return LayeredConfigResolver
     */
    private static function getResolver(): LayeredConfigResolver
    {
        if (!self::$resolver) {
            self::$resolver = new LayeredConfigResolver();
        }
        return self::$resolver;
    }

    /**
     * 获取有效配置（新版本主入口）
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @return array|null
     */
    public static function getEffectiveConfig(int $merId, string $paymentType): ?array
    {
        $cacheKey = self::CACHE_PREFIX . "effective_{$merId}_{$paymentType}";
        
        // 尝试从缓存获取
        $config = Cache::get($cacheKey);
        if ($config !== null) {
            return $config;
        }
        
        try {
            // 使用分层解析器获取配置
            $resolver = self::getResolver();
            $config = $resolver->resolveConfig($merId, $paymentType);
            
            if ($config) {
                // 缓存配置
                Cache::set($cacheKey, $config, self::CACHE_TTL);
                
                Log::info('支付配置解析成功', [
                    'mer_id' => $merId,
                    'payment_type' => $paymentType,
                    'payment_mode' => $config['_meta']['payment_mode'] ?? 'unknown'
                ]);
            }
            
            return $config;
            
        } catch (\Exception $e) {
            Log::error('支付配置解析失败', [
                'mer_id' => $merId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            
            // 降级到旧版本配置中心
            return PaymentConfigCenter::getEffectiveConfig($merId, $paymentType);
        }
    }

    /**
     * 获取支付模式信息
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @return array
     */
    public static function getPaymentModeInfo(int $merId, string $paymentType): array
    {
        try {
            $resolver = self::getResolver();
            $paymentMode = $resolver->determinePaymentMode($merId, $paymentType);
            $configLayers = PaymentModeEnum::getConfigLayers($paymentMode);
            
            return [
                'payment_mode' => $paymentMode,
                'mode_name' => PaymentModeEnum::getModeDisplayName($paymentMode),
                'config_layers' => $configLayers,
                'is_service_mode' => in_array($paymentMode, [
                    PaymentModeEnum::WECHAT_SERVICE,
                    PaymentModeEnum::WECHAT_ECOMMERCE,
                    PaymentModeEnum::ALIPAY_SERVICE,
                    PaymentModeEnum::ALIPAY_ZHIFU
                ])
            ];
        } catch (\Exception $e) {
            Log::error('获取支付模式信息失败', [
                'mer_id' => $merId,
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            
            return [
                'payment_mode' => PaymentModeEnum::getDefaultMode($paymentType),
                'mode_name' => '未知模式',
                'config_layers' => [],
                'is_service_mode' => false
            ];
        }
    }

    /**
     * 验证配置完整性
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @return array
     */
    public static function validateConfig(int $merId, string $paymentType): array
    {
        try {
            $resolver = self::getResolver();
            $config = $resolver->resolveConfig($merId, $paymentType);
            
            if (!$config) {
                return [
                    'valid' => false,
                    'message' => '无法获取配置',
                    'missing_fields' => []
                ];
            }
            
            $paymentMode = $config['_meta']['payment_mode'];
            $validation = $resolver->validateConfig($config, $paymentMode);
            
            return [
                'valid' => $validation['valid'],
                'message' => $validation['valid'] ? '配置完整' : '配置不完整',
                'missing_fields' => $validation['missing_fields'] ?? [],
                'payment_mode' => $paymentMode
            ];
            
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'message' => '验证失败：' . $e->getMessage(),
                'missing_fields' => []
            ];
        }
    }

    /**
     * 获取配置字段模板
     * @param string $paymentType 支付类型
     * @param int $merId 商户ID（用于确定模式）
     * @return array
     */
    public static function getConfigTemplate(string $paymentType, int $merId = 0): array
    {
        try {
            // 确定支付模式
            $resolver = self::getResolver();
            $paymentMode = $resolver->determinePaymentMode($merId, $paymentType);
            $configLayers = PaymentModeEnum::getConfigLayers($paymentMode);
            
            $template = [];
            
            foreach ($configLayers as $layer => $configType) {
                $fields = LayeredPaymentFields::getConfigTypeFields($configType);
                
                foreach ($fields as $fieldName => $fieldDef) {
                    $template[$fieldName] = [
                        'type' => $fieldDef['type'],
                        'required' => $fieldDef['required'],
                        'label' => $fieldDef['label'],
                        'layer' => $layer,
                        'config_type' => $configType
                    ];
                }
            }
            
            return $template;
            
        } catch (\Exception $e) {
            Log::error('获取配置模板失败', [
                'payment_type' => $paymentType,
                'mer_id' => $merId,
                'error' => $e->getMessage()
            ]);
            
            // 降级到旧版本
            return PaymentConfigCenter::getConfigTemplate($paymentType);
        }
    }

    /**
     * 清除配置缓存
     * @param int|null $merId 商户ID
     * @param string|null $paymentType 支付类型
     */
    public static function clearCache(?int $merId = null, ?string $paymentType = null): void
    {
        if ($merId === null && $paymentType === null) {
            // 清除所有V2缓存
            $keys = Cache::tag('payment_config_v2')->clear();
        } elseif ($merId !== null && $paymentType !== null) {
            // 清除特定缓存
            $cacheKey = self::CACHE_PREFIX . "effective_{$merId}_{$paymentType}";
            Cache::delete($cacheKey);
        } else {
            // 清除相关缓存
            Cache::tag('payment_config_v2')->clear();
        }
        
        // 同时清除旧版本缓存
        PaymentConfigCenter::clearCache($merId, $paymentType);
    }

    /**
     * 获取支持的支付类型和模式
     * @return array
     */
    public static function getSupportedPaymentModes(): array
    {
        $modes = [];
        
        foreach (PaymentModeEnum::getAllModes() as $mode) {
            $modes[$mode] = [
                'mode' => $mode,
                'name' => PaymentModeEnum::getModeDisplayName($mode),
                'layers' => PaymentModeEnum::getConfigLayers($mode)
            ];
        }
        
        return $modes;
    }

    /**
     * 兼容性方法：获取商户配置
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @return array|null
     */
    public static function getMerchantConfig(int $merId, string $paymentType): ?array
    {
        return self::getEffectiveConfig($merId, $paymentType);
    }

    /**
     * 兼容性方法：获取系统配置
     * @param string $paymentType 支付类型
     * @return array|null
     */
    public static function getSystemConfig(string $paymentType): ?array
    {
        return self::getEffectiveConfig(0, $paymentType);
    }
}
