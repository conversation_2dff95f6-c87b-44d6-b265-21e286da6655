<?php

namespace crmeb\services\payment\config;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 支付方式配置服务 - 简化版本
 * 移除复杂的字段映射，直接使用网关字段
 */
class PaymentMethodConfigService
{
    /**
     * 缓存键前缀
     */
    const CACHE_PREFIX = 'payment_methods_config_v2';
    
    /**
     * 缓存时间（秒）
     */
    const CACHE_TTL = 3600;
    
    /**
     * 支付方式名称映射
     */
    protected static $nameMapping = [
        'wechat' => '微信支付',
        'alipay' => '支付宝',
        'routine' => '小程序',
        'weixinApp' => '微信APP',
        'alipayApp' => '支付宝APP',
        'weixinQr' => '微信扫码',
        'alipayQr' => '支付宝扫码',
        'h5' => 'H5支付',
        'balance' => '余额支付',
        'offline' => '线下支付'
    ];
    
    /**
     * 支付方式标签类型映射
     */
    protected static $tagMapping = [
        'wechat' => 'success',
        'alipay' => 'primary',
        'routine' => 'warning',
        'weixinApp' => 'success',
        'alipayApp' => 'primary',
        'weixinQr' => 'info',
        'alipayQr' => 'info',
        'h5' => 'info',
        'balance' => 'warning',
        'offline' => 'default'
    ];
    
    /**
     * 支付类型标准化映射
     */
    protected static $paymentTypeNormalization = [
        'weixin' => 'wechat',
        'weixinApp' => 'wechat',
        'weixinQr' => 'wechat', 
        'routine' => 'wechat',
        'h5' => 'wechat',
        'weixinBarCode' => 'wechat',
        'wechat' => 'wechat',
        
        'alipay' => 'alipay',
        'alipayApp' => 'alipay',
        'alipayQr' => 'alipay',
        'alipayBarCode' => 'alipay',
        'alipay_mini' => 'alipay',
        
        'balance' => 'balance',
        'offline' => 'offline'
    ];
    
    /**
     * 获取支付方式配置列表
     * @param bool $useCache 是否使用缓存
     * @param array $filters 过滤条件
     * @return array
     */
    public static function getPaymentMethodsConfig(bool $useCache = true, array $filters = []): array
    {
        try {
            $cacheKey = self::CACHE_PREFIX;
            if (!empty($filters)) {
                $cacheKey .= '_' . md5(serialize($filters));
            }
            
            // 尝试从缓存获取
            if ($useCache) {
                $methods = Cache::get($cacheKey);
                if (!empty($methods)) {
                    return $methods;
                }
            }
            
            // 从数据库中获取支付方式配置
            $query = Db::table('eb_payment_method_status')
                ->field([
                    'id',
                    'payment_type as code',
                    'payment_type',
                    'payment_name',
                    'status',
                    'priority',
                    'fee_rate',
                    'min_amount',
                    'max_amount',
                    'created_at',
                    'updated_at'
                ]);
            
            // 应用过滤条件
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }
            
            if (!empty($filters['payment_types'])) {
                $query->whereIn('payment_type', $filters['payment_types']);
            }
            
            $methods = $query->order('priority DESC')
                ->select()
                ->toArray();
            
            // 处理数据，添加名称和标签类型
            foreach ($methods as &$method) {
                $method = self::formatPaymentMethod($method);
            }
            
            // 缓存数据
            if ($useCache) {
                Cache::set($cacheKey, $methods, self::CACHE_TTL);
            }
            
            return $methods;
            
        } catch (\Exception $e) {
            Log::error('获取支付方式配置失败: ' . $e->getMessage(), [
                'filters' => $filters
            ]);
            return [];
        }
    }
    
    /**
     * 获取启用的支付方式配置
     * @param bool $useCache 是否使用缓存
     * @return array
     */
    public static function getEnabledPaymentMethods(bool $useCache = true): array
    {
        return self::getPaymentMethodsConfig($useCache, ['status' => 'active']);
    }

    /**
     * 获取活跃支付方式数量
     * @param bool $useCache 是否使用缓存
     * @return int
     */
    public static function getActivePaymentMethodsCount(bool $useCache = true): int
    {
        try {
            $methods = self::getEnabledPaymentMethods($useCache);
            return count($methods);
        } catch (\Exception $e) {
            Log::error('获取活跃支付方式数量失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * 获取单个支付方式配置
     * @param string $paymentType 支付类型
     * @param bool $useCache 是否使用缓存
     * @return array|null
     */
    public static function getPaymentMethodConfig(string $paymentType, bool $useCache = true): ?array
    {
        try {
            $methods = self::getPaymentMethodsConfig($useCache);

            foreach ($methods as $method) {
                if ($method['code'] === $paymentType || $method['payment_type'] === $paymentType) {
                    return $method;
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::error('获取支付方式配置失败', [
                'payment_type' => $paymentType,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 获取支付方式下拉选项
     * @param bool $onlyEnabled 是否只返回启用的
     * @param bool $useCache 是否使用缓存
     * @return array
     */
    public static function getPaymentMethodOptions(bool $onlyEnabled = true, bool $useCache = true): array
    {
        $filters = $onlyEnabled ? ['status' => 'active'] : [];
        $methods = self::getPaymentMethodsConfig($useCache, $filters);
        
        $options = [];
        foreach ($methods as $method) {
            $options[] = [
                'value' => $method['code'],
                'label' => $method['name'],
                'tag_type' => $method['tag_type'],
                'description' => $method['description']
            ];
        }
        
        return $options;
    }
    
    /**
     * 统一支付类型
     * @param string $paymentType 原始支付类型
     * @return string 统一后的支付类型
     */
    public static function normalizePaymentType(string $paymentType): string
    {
        return self::$paymentTypeNormalization[$paymentType] ?? $paymentType;
    }
    
    /**
     * 获取支付方式名称
     * @param string $paymentType 支付类型
     * @return string
     */
    public static function getPaymentMethodName(string $paymentType): string
    {
        return self::$nameMapping[$paymentType] ?? $paymentType;
    }
    
    /**
     * 获取支付方式标签类型
     * @param string $paymentType 支付类型
     * @return string
     */
    public static function getPaymentMethodTagType(string $paymentType): string
    {
        return self::$tagMapping[$paymentType] ?? 'default';
    }
    
    /**
     * 检查支付方式是否启用
     * @param string $paymentType 支付类型
     * @param bool $useCache 是否使用缓存
     * @return bool
     */
    public static function isPaymentMethodEnabled(string $paymentType, bool $useCache = true): bool
    {
        try {
            $method = Db::table('eb_payment_method_status')
                ->where('payment_type', $paymentType)
                ->where('status', 'active')
                ->find();
            
            return !empty($method);
            
        } catch (\Exception $e) {
            Log::error('检查支付方式状态失败: ' . $e->getMessage(), [
                'payment_type' => $paymentType
            ]);
            return false;
        }
    }
    
    /**
     * 清除支付方式配置缓存
     * @param string|null $paymentType 指定支付类型，为空则清除所有
     * @return bool
     */
    public static function clearCache(?string $paymentType = null): bool
    {
        try {
            if ($paymentType) {
                // 清除指定支付方式的缓存
                Cache::delete(self::CACHE_PREFIX . '_single_' . $paymentType);
            } else {
                // 清除所有相关缓存
                Cache::clear();
            }
            
            Log::info('支付方式配置缓存清除成功', ['payment_type' => $paymentType]);
            return true;
            
        } catch (\Exception $e) {
            Log::error('清除支付方式配置缓存失败: ' . $e->getMessage(), [
                'payment_type' => $paymentType
            ]);
            return false;
        }
    }
    
    /**
     * 格式化支付方式数据
     * @param array $method 原始数据
     * @return array 格式化后的数据
     */
    protected static function formatPaymentMethod(array $method): array
    {
        $paymentType = $method['payment_type'];
        
        // 使用数据库中的名称，如果为空则使用映射
        $method['name'] = !empty($method['payment_name']) 
            ? $method['payment_name'] 
            : (self::$nameMapping[$paymentType] ?? $paymentType);
        
        // 为前端下拉框提供字段
        $method['label'] = $method['name'];
        $method['value'] = $method['code'];
        
        // 标签类型
        $method['tag_type'] = self::$tagMapping[$paymentType] ?? 'default';
        
        // 描述信息
        $method['description'] = $method['name'] . '官方接口';
        
        // 格式化金额
        $method['min_amount'] = floatval($method['min_amount']);
        $method['max_amount'] = floatval($method['max_amount']);
        $method['fee_rate'] = floatval($method['fee_rate']);
        
        return $method;
    }
}
