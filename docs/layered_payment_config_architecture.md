# 分层支付配置架构设计

## 🎯 设计理念

考虑到服务商模式、电商收付通、直付通等复杂场景，采用**分层配置架构**：
- **基础层** - 网关原生字段
- **模式层** - 服务商/直付通模式配置
- **商户层** - 商户特定配置（子商户号、授权token等）

## 🏗️ 架构设计

### 1. 配置层次结构

```php
class LayeredPaymentConfig
{
    // 第一层：基础网关配置（直接使用网关字段）
    const BASE_CONFIG = [
        'wechat' => [
            'appid' => ['type' => 'string', 'required' => true],
            'mch_id' => ['type' => 'string', 'required' => true],
            'key' => ['type' => 'string', 'required' => true],
            'api_v3_key' => ['type' => 'string', 'required' => false],
            'serial_no' => ['type' => 'string', 'required' => false],
            'private_key' => ['type' => 'text', 'required' => false],
            'public_key' => ['type' => 'text', 'required' => false]
        ],
        'alipay' => [
            'app_id' => ['type' => 'string', 'required' => true],
            'private_key' => ['type' => 'text', 'required' => true],
            'public_key' => ['type' => 'text', 'required' => true],
            'cert_mode' => ['type' => 'boolean', 'required' => false],
            'app_cert' => ['type' => 'text', 'required' => false],
            'public_cert' => ['type' => 'text', 'required' => false],
            'root_cert' => ['type' => 'text', 'required' => false]
        ]
    ];
    
    // 第二层：服务商模式配置
    const SERVICE_CONFIG = [
        'wechat_service' => [
            'service_appid' => ['type' => 'string', 'required' => true],
            'service_mch_id' => ['type' => 'string', 'required' => true],
            'service_key' => ['type' => 'string', 'required' => true],
            'service_v3_key' => ['type' => 'string', 'required' => true],
            'service_serial_no' => ['type' => 'string', 'required' => true],
            'service_private_key' => ['type' => 'text', 'required' => true],
            'service_public_key' => ['type' => 'text', 'required' => false]
        ],
        'alipay_service' => [
            'service_app_id' => ['type' => 'string', 'required' => true],
            'service_private_key' => ['type' => 'text', 'required' => true],
            'service_public_key' => ['type' => 'text', 'required' => true],
            'service_cert_mode' => ['type' => 'boolean', 'required' => false],
            'service_app_cert' => ['type' => 'text', 'required' => false],
            'service_public_cert' => ['type' => 'text', 'required' => false],
            'service_root_cert' => ['type' => 'text', 'required' => false]
        ]
    ];
    
    // 第三层：商户特定配置
    const MERCHANT_CONFIG = [
        'wechat_merchant' => [
            'sub_mch_id' => ['type' => 'string', 'source' => 'merchant_table'],
            'sub_appid' => ['type' => 'string', 'source' => 'merchant_config']
        ],
        'alipay_merchant' => [
            'alipay_smid' => ['type' => 'string', 'source' => 'merchant_table'],
            'app_auth_token' => ['type' => 'string', 'source' => 'auth_table'],
            'auth_app_id' => ['type' => 'string', 'source' => 'auth_table']
        ]
    ];
}
```

### 2. 支付模式枚举

```php
class PaymentModeEnum
{
    // 微信支付模式
    const WECHAT_DIRECT = 'wechat_direct';           // 直连模式
    const WECHAT_SERVICE = 'wechat_service';         // 服务商模式
    const WECHAT_ECOMMERCE = 'wechat_ecommerce';     // 电商收付通
    
    // 支付宝支付模式
    const ALIPAY_DIRECT = 'alipay_direct';           // 直连模式
    const ALIPAY_SERVICE = 'alipay_service';         // 服务商模式
    const ALIPAY_ZHIFU = 'alipay_zhifu';            // 直付通模式
    
    // 模式配置映射
    const MODE_CONFIG_MAPPING = [
        self::WECHAT_DIRECT => ['base' => 'wechat'],
        self::WECHAT_SERVICE => ['base' => 'wechat', 'service' => 'wechat_service', 'merchant' => 'wechat_merchant'],
        self::WECHAT_ECOMMERCE => ['base' => 'wechat', 'service' => 'wechat_service', 'merchant' => 'wechat_merchant'],
        self::ALIPAY_DIRECT => ['base' => 'alipay'],
        self::ALIPAY_SERVICE => ['base' => 'alipay', 'service' => 'alipay_service'],
        self::ALIPAY_ZHIFU => ['base' => 'alipay', 'service' => 'alipay_service', 'merchant' => 'alipay_merchant']
    ];
}
```

### 3. 统一配置解析器

```php
class LayeredConfigResolver
{
    /**
     * 解析支付配置
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @return array
     */
    public function resolveConfig(int $merId, string $paymentType): array
    {
        // 1. 确定支付模式
        $paymentMode = $this->determinePaymentMode($merId, $paymentType);
        
        // 2. 获取模式对应的配置层级
        $configLayers = PaymentModeEnum::MODE_CONFIG_MAPPING[$paymentMode];
        
        // 3. 逐层合并配置
        $finalConfig = [];
        
        // 基础配置层
        if (isset($configLayers['base'])) {
            $baseConfig = $this->getBaseConfig($configLayers['base']);
            $finalConfig = array_merge($finalConfig, $baseConfig);
        }
        
        // 服务商配置层
        if (isset($configLayers['service'])) {
            $serviceConfig = $this->getServiceConfig($configLayers['service']);
            $finalConfig = array_merge($finalConfig, $serviceConfig);
        }
        
        // 商户配置层
        if (isset($configLayers['merchant'])) {
            $merchantConfig = $this->getMerchantConfig($merId, $configLayers['merchant']);
            $finalConfig = array_merge($finalConfig, $merchantConfig);
        }
        
        // 4. 添加模式标识
        $finalConfig['payment_mode'] = $paymentMode;
        $finalConfig['mer_id'] = $merId;
        
        return $finalConfig;
    }
    
    /**
     * 确定支付模式
     */
    private function determinePaymentMode(int $merId, string $paymentType): string
    {
        // 检查系统是否开启服务商模式
        $wechatServiceEnabled = systemConfig('open_wx_sub_mch');
        $alipayServiceEnabled = systemConfig('open_alipay_sub_mch');
        
        // 检查商户是否有授权信息
        $merchant = $this->getMerchantInfo($merId);
        
        if (strpos($paymentType, 'wechat') !== false || strpos($paymentType, 'weixin') !== false) {
            if ($wechatServiceEnabled && !empty($merchant['sub_mchid'])) {
                return PaymentModeEnum::WECHAT_ECOMMERCE; // 电商收付通
            } elseif ($wechatServiceEnabled) {
                return PaymentModeEnum::WECHAT_SERVICE;   // 服务商模式
            } else {
                return PaymentModeEnum::WECHAT_DIRECT;    // 直连模式
            }
        }
        
        if (strpos($paymentType, 'alipay') !== false) {
            if ($alipayServiceEnabled && !empty($merchant['alipay_smid'])) {
                return PaymentModeEnum::ALIPAY_ZHIFU;     // 直付通
            } elseif ($alipayServiceEnabled) {
                return PaymentModeEnum::ALIPAY_SERVICE;   // 服务商模式
            } else {
                return PaymentModeEnum::ALIPAY_DIRECT;    // 直连模式
            }
        }
        
        return PaymentModeEnum::WECHAT_DIRECT; // 默认
    }
    
    /**
     * 获取基础配置
     */
    private function getBaseConfig(string $configType): array
    {
        $config = [];
        $mapping = $this->getSystemConfigMapping($configType);
        
        foreach ($mapping as $field => $systemKey) {
            $value = systemConfig($systemKey);
            if (!empty($value)) {
                $config[$field] = $value;
            }
        }
        
        return $config;
    }
    
    /**
     * 获取服务商配置
     */
    private function getServiceConfig(string $serviceType): array
    {
        $config = [];
        
        if ($serviceType === 'wechat_service') {
            $config = [
                'service_appid' => systemConfig('wechat_service_appid'),
                'service_mch_id' => systemConfig('wechat_service_merid'),
                'service_key' => systemConfig('wechat_service_key'),
                'service_v3_key' => systemConfig('wechat_service_v3key'),
                'service_serial_no' => systemConfig('wechat_service_serial_no'),
                'service_private_key' => systemConfig('wechat_service_client_key'),
                'service_cert_path' => systemConfig('wechat_service_client_cert')
            ];
        } elseif ($serviceType === 'alipay_service') {
            $config = [
                'service_app_id' => systemConfig('alipay_service_app_id'),
                'service_private_key' => systemConfig('alipay_service_private_key'),
                'service_public_key' => systemConfig('alipay_service_public_key'),
                'service_cert_mode' => systemConfig('alipay_service_cert_mode'),
                'service_app_cert' => systemConfig('alipay_service_app_cert'),
                'service_public_cert' => systemConfig('alipay_service_public_cert'),
                'service_root_cert' => systemConfig('alipay_service_root_cert')
            ];
        }
        
        return array_filter($config); // 移除空值
    }
    
    /**
     * 获取商户配置
     */
    private function getMerchantConfig(int $merId, string $merchantType): array
    {
        $config = [];
        
        if ($merchantType === 'wechat_merchant') {
            $merchant = $this->getMerchantInfo($merId);
            $config['sub_mch_id'] = $merchant['sub_mchid'] ?? '';
            
            // 获取商户专用配置
            $merchantConfig = $this->getMerchantPaymentConfig($merId, 'wechat');
            if ($merchantConfig) {
                $config = array_merge($config, $merchantConfig);
            }
            
        } elseif ($merchantType === 'alipay_merchant') {
            $merchant = $this->getMerchantInfo($merId);
            $config['alipay_smid'] = $merchant['alipay_smid'] ?? '';
            
            // 获取支付宝授权信息
            $authInfo = $this->getAlipayAuthInfo($merId);
            if ($authInfo) {
                $config['app_auth_token'] = $authInfo['app_auth_token'];
                $config['auth_app_id'] = $authInfo['auth_app_id'];
                $config['refresh_token'] = $authInfo['refresh_token'];
            }
        }
        
        return array_filter($config);
    }
}
```

### 4. 数据库存储优化

```sql
-- 支付配置表增加模式字段
ALTER TABLE `eb_merchant_payment_config` 
ADD COLUMN `payment_mode` varchar(32) DEFAULT 'direct' COMMENT '支付模式：direct/service/ecommerce/zhifu',
ADD COLUMN `config_layer` varchar(32) DEFAULT 'base' COMMENT '配置层级：base/service/merchant';

-- 配置参数表增加层级字段
ALTER TABLE `eb_merchant_payment_params`
ADD COLUMN `config_layer` varchar(32) DEFAULT 'base' COMMENT '配置层级：base/service/merchant',
ADD COLUMN `param_source` varchar(32) DEFAULT 'manual' COMMENT '参数来源：manual/system/auth';
```

## 🎯 优势分析

### 1. 清晰的层次结构
- **基础层** - 网关原生字段，直接可用
- **服务商层** - 服务商模式专用配置
- **商户层** - 商户特定信息（子商户号、授权token）

### 2. 灵活的模式切换
```php
// 根据系统配置和商户状态自动确定支付模式
$mode = $resolver->determinePaymentMode($merId, $paymentType);
// direct -> service -> ecommerce/zhifu
```

### 3. 配置复用
- 服务商配置在系统级别配置一次，所有商户共享
- 基础配置可以在商户级别覆盖
- 授权信息自动从授权表获取

### 4. 扩展性强
```php
// 新增支付模式只需要定义配置层级
const NEW_MODE_CONFIG = [
    'new_payment_mode' => [
        'base' => 'new_gateway',
        'service' => 'new_service',
        'merchant' => 'new_merchant'
    ]
];
```

## 🚀 实施方案

### 阶段一：定义分层架构
1. 定义PaymentModeEnum和配置层级
2. 实现LayeredConfigResolver
3. 保持向后兼容

### 阶段二：重构配置获取
1. 统一使用LayeredConfigResolver
2. 重构网关适配器
3. 测试各种支付模式

### 阶段三：优化存储结构
1. 数据库表结构调整
2. 配置数据迁移
3. 清理冗余配置

这种分层架构既保持了网关字段的直接性，又很好地处理了服务商模式的复杂性。
