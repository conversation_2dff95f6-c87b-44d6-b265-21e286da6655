# 支付配置字段映射改进方案

## 🔍 问题分析

### 当前架构存在的问题

1. **字段映射不统一**
   - 系统配置和商户配置使用不同的字段名
   - 不同支付方式的相同字段使用不同的系统配置key
   - 前端、数据库、网关之间的字段映射混乱

2. **数据存储冗余**
   - 商户配置同时存储嵌套结构和扁平化字段
   - 缺乏统一的数据格式规范

3. **配置获取逻辑复杂**
   - 多个地方重复实现配置合并逻辑
   - 缺乏统一的配置解析服务

## ✅ 改进方案

### 1. 统一字段映射架构

```php
class PaymentFieldMappingV2
{
    // 支付类型标准化映射
    const PAYMENT_TYPE_MAPPING = [
        'weixin' => 'wechat',
        'weixinApp' => 'wechat',
        'routine' => 'wechat',
        'h5' => 'wechat',
        'alipay' => 'alipay',
        'alipayApp' => 'alipay',
        'alipay_mini' => 'alipay'
    ];
    
    // 标准字段定义（所有支付方式的完整字段集）
    const STANDARD_FIELDS = [
        'wechat' => [
            'app_id' => ['type' => 'string', 'required' => true],
            'mch_id' => ['type' => 'string', 'required' => true],
            'key' => ['type' => 'string', 'required' => true],
            'api_v3_key' => ['type' => 'string', 'required' => false],
            'serial_no' => ['type' => 'string', 'required' => false],
            'private_key' => ['type' => 'text', 'required' => false],
            'public_key' => ['type' => 'text', 'required' => false],
            'service_mode' => ['type' => 'boolean', 'required' => false],
            'sub_mch_id' => ['type' => 'string', 'required' => false]
        ],
        'alipay' => [
            'app_id' => ['type' => 'string', 'required' => true],
            'private_key' => ['type' => 'text', 'required' => true],
            'public_key' => ['type' => 'text', 'required' => true],
            'cert_mode' => ['type' => 'boolean', 'required' => false],
            'app_cert' => ['type' => 'text', 'required' => false],
            'public_cert' => ['type' => 'text', 'required' => false],
            'root_cert' => ['type' => 'text', 'required' => false],
            'service_mode' => ['type' => 'boolean', 'required' => false],
            'alipay_smid' => ['type' => 'string', 'required' => false],
            'alipay_auth_token' => ['type' => 'string', 'required' => false]
        ]
    ];
    
    // 系统配置映射（统一使用标准字段名作为key）
    const SYSTEM_CONFIG_MAPPING = [
        'wechat' => [
            'app_id' => 'wechat_app_id',
            'mch_id' => 'wechat_mch_id',
            'key' => 'wechat_key',
            'api_v3_key' => 'wechat_api_v3_key',
            'serial_no' => 'wechat_serial_no',
            'private_key' => 'wechat_private_key',
            'public_key' => 'wechat_public_key',
            'service_mode' => 'wechat_service_mode',
            'sub_mch_id' => 'wechat_sub_mch_id'
        ],
        'alipay' => [
            'app_id' => 'alipay_app_id',
            'private_key' => 'alipay_private_key',
            'public_key' => 'alipay_public_key',
            'cert_mode' => 'alipay_cert_mode',
            'app_cert' => 'alipay_app_cert',
            'public_cert' => 'alipay_public_cert',
            'root_cert' => 'alipay_root_cert',
            'service_mode' => 'alipay_service_mode',
            'alipay_smid' => 'alipay_smid',
            'alipay_auth_token' => 'alipay_auth_token'
        ]
    ];
    
    // 网关字段映射
    const GATEWAY_FIELD_MAPPING = [
        'wechat' => [
            'app_id' => 'appid',
            'mch_id' => 'mch_id',
            'key' => 'key',
            'api_v3_key' => 'api_v3_key',
            'serial_no' => 'serial_no',
            'private_key' => 'private_key',
            'public_key' => 'public_key'
        ],
        'alipay' => [
            'app_id' => 'app_id',
            'private_key' => 'private_key',
            'public_key' => 'public_key',
            'cert_mode' => 'cert_mode'
        ]
    ];
}
```

### 2. 统一配置解析服务

```php
class UnifiedPaymentConfigResolver
{
    public function resolveConfig(int $merId, string $paymentType): array
    {
        // 1. 标准化支付类型
        $standardType = PaymentFieldMappingV2::PAYMENT_TYPE_MAPPING[$paymentType] ?? $paymentType;
        
        // 2. 获取系统默认配置
        $systemConfig = $this->getSystemConfig($standardType);
        
        // 3. 获取商户配置
        $merchantConfig = $this->getMerchantConfig($merId, $paymentType);
        
        // 4. 合并配置（商户配置优先）
        $mergedConfig = array_merge($systemConfig, $merchantConfig);
        
        // 5. 转换为网关格式
        return $this->convertToGatewayFormat($standardType, $mergedConfig);
    }
    
    private function getSystemConfig(string $standardType): array
    {
        $mapping = PaymentFieldMappingV2::SYSTEM_CONFIG_MAPPING[$standardType] ?? [];
        $config = [];
        
        foreach ($mapping as $standardField => $systemConfigKey) {
            $value = systemConfig($systemConfigKey);
            if (!empty($value)) {
                $config[$standardField] = $value;
            }
        }
        
        return $config;
    }
    
    private function getMerchantConfig(int $merId, string $paymentType): array
    {
        // 从eb_merchant_payment_params表获取配置
        $params = Db::table('eb_merchant_payment_params')
            ->alias('p')
            ->join('eb_merchant_payment_config c', 'p.payment_id = c.id')
            ->where('c.mer_id', $merId)
            ->where('c.payment_type', $paymentType)
            ->where('c.status', 1)
            ->column('p.param_value', 'p.param_name');
        
        return $params ?: [];
    }
    
    private function convertToGatewayFormat(string $standardType, array $config): array
    {
        $mapping = PaymentFieldMappingV2::GATEWAY_FIELD_MAPPING[$standardType] ?? [];
        $gatewayConfig = [];
        
        foreach ($config as $standardField => $value) {
            if (isset($mapping[$standardField]) && !empty($value)) {
                $gatewayField = $mapping[$standardField];
                $gatewayConfig[$gatewayField] = $value;
            }
        }
        
        return $gatewayConfig;
    }
}
```

### 3. 数据库表结构优化

```sql
-- 优化商户支付参数表
ALTER TABLE `eb_merchant_payment_params` 
ADD COLUMN `param_type` varchar(20) DEFAULT 'string' COMMENT '参数类型：string/text/boolean/integer',
ADD COLUMN `is_required` tinyint(1) DEFAULT 0 COMMENT '是否必填',
ADD COLUMN `is_sensitive` tinyint(1) DEFAULT 0 COMMENT '是否敏感信息',
ADD INDEX `idx_param_type` (`param_type`);

-- 添加字段元数据表
CREATE TABLE `eb_payment_field_metadata` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `payment_type` varchar(32) NOT NULL COMMENT '支付类型',
  `field_name` varchar(64) NOT NULL COMMENT '字段名',
  `field_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '字段类型',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填',
  `is_sensitive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否敏感',
  `default_value` text COMMENT '默认值',
  `validation_rule` varchar(255) COMMENT '验证规则',
  `description` varchar(255) COMMENT '字段描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_field` (`payment_type`, `field_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付字段元数据表';
```

### 4. 控制器重构

```php
class ConfigControllerV2 extends BaseController
{
    protected $configResolver;
    
    public function __construct(UnifiedPaymentConfigResolver $resolver)
    {
        $this->configResolver = $resolver;
    }
    
    public function getPaymentConfig()
    {
        $merId = $this->request->param('mer_id');
        $paymentType = $this->request->param('payment_type');
        
        try {
            // 使用统一解析器获取配置
            $config = $this->configResolver->resolveConfig($merId, $paymentType);
            
            return $this->success([
                'config' => $config,
                'metadata' => $this->getFieldMetadata($paymentType)
            ]);
        } catch (\Exception $e) {
            return $this->fail('获取配置失败：' . $e->getMessage());
        }
    }
    
    public function updatePaymentConfig()
    {
        $data = $this->request->param();
        
        try {
            // 1. 字段验证
            $this->validateConfigFields($data);
            
            // 2. 标准化字段
            $standardConfig = PaymentFieldMappingV2::normalizeFields(
                $data['payment_type'], 
                $data['config']
            );
            
            // 3. 保存配置
            $result = $this->saveConfig($data['mer_id'], $data['payment_type'], $standardConfig);
            
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail('更新配置失败：' . $e->getMessage());
        }
    }
}
```

## 🎯 实施建议

### 阶段一：基础重构
1. 创建统一的字段映射类
2. 重构PaymentConfigCenter的系统配置映射
3. 统一商户配置存储格式

### 阶段二：服务整合
1. 实现UnifiedPaymentConfigResolver
2. 重构控制器使用统一解析器
3. 添加配置验证和元数据支持

### 阶段三：数据迁移
1. 迁移现有配置数据到新格式
2. 清理冗余字段和配置
3. 完善测试和文档

## 📊 预期效果

1. **字段映射统一** - 所有层级使用一致的字段映射规则
2. **配置获取简化** - 统一的配置解析入口
3. **数据存储规范** - 标准化的配置存储格式
4. **维护成本降低** - 减少重复代码和配置冲突
5. **扩展性增强** - 新增支付方式更容易集成
