# 支付配置分层架构重构计划

## 🎯 重构目标

将现有的复杂支付配置映射系统重构为基于分层架构的统一配置管理系统，支持：
- 微信直连/服务商/电商收付通模式
- 支付宝直连/服务商/直付通模式
- 统一的字段映射和配置管理
- 清理冗余的映射代码

## 📋 详细执行计划

### 阶段一：核心架构组件创建 ✅

#### 1.1 支付模式枚举 ✅
- **文件**: `crmeb/services/payment/config/PaymentModeEnum.php`
- **功能**: 定义所有支付模式和配置层级关系
- **状态**: 已完成

#### 1.2 分层字段定义 ✅
- **文件**: `crmeb/services/payment/config/LayeredPaymentFields.php`
- **功能**: 定义基础层、服务商层、商户层的字段结构
- **状态**: 已完成

#### 1.3 分层配置解析器 ✅
- **文件**: `crmeb/services/payment/config/LayeredConfigResolver.php`
- **功能**: 统一解析支付配置，自动处理不同模式和层级
- **状态**: 已完成

#### 1.4 新版配置中心 ✅
- **文件**: `crmeb/services/payment/config/PaymentConfigCenterV2.php`
- **功能**: 基于分层架构的配置中心，保持向后兼容
- **状态**: 已完成

#### 1.5 新版控制器 ✅
- **文件**: `app/controller/admin/payment/ConfigControllerV2.php`
- **功能**: 基于分层架构的配置管理接口
- **状态**: 已完成

### 阶段二：系统配置统一 🔄

#### 2.1 统一系统配置键名
**目标**: 将系统配置键名统一为网关字段格式

**当前问题**:
```php
// 当前不一致的系统配置键名
'wechat_appid'     // 微信
'routine_appId'    // 小程序（驼峰）
'wechat_app_appid' // APP（重复前缀）
```

**统一后**:
```php
// 统一的系统配置键名
'wechat_appid'           // 微信基础配置
'wechat_service_appid'   // 微信服务商配置
'alipay_app_id'          // 支付宝基础配置
'alipay_service_app_id'  // 支付宝服务商配置
```

#### 2.2 更新系统配置表
**SQL脚本**: 
```sql
-- 统一微信配置键名
UPDATE eb_system_config SET menu_name = 'wechat_appid' WHERE menu_name = 'wechat_appid';
UPDATE eb_system_config SET menu_name = 'wechat_mch_id' WHERE menu_name = 'wechat_mchid';
UPDATE eb_system_config SET menu_name = 'wechat_service_appid' WHERE menu_name = 'wechat_service_appid';

-- 统一支付宝配置键名
UPDATE eb_system_config SET menu_name = 'alipay_app_id' WHERE menu_name = 'alipay_app_id';
UPDATE eb_system_config SET menu_name = 'alipay_service_app_id' WHERE menu_name = 'alipay_service_app_id';
```

### 阶段三：商户配置统一 🔄

#### 3.1 商户配置字段标准化
**目标**: 商户配置表使用网关原生字段名

**当前商户配置表结构**:
```sql
eb_merchant_payment_params (
  param_name varchar(64),  -- 字段名不规范
  param_value text         -- 值格式不统一
)
```

**优化后结构**:
```sql
ALTER TABLE eb_merchant_payment_params 
ADD COLUMN config_layer varchar(32) DEFAULT 'base' COMMENT '配置层级',
ADD COLUMN field_type varchar(20) DEFAULT 'string' COMMENT '字段类型';
```

#### 3.2 数据迁移脚本
**文件**: `install/migrate_payment_config.php`
**功能**: 将现有商户配置迁移到新格式

### 阶段四：清理冗余代码 ⏳

#### 4.1 删除旧的字段映射
**目标文件**:
- `crmeb/services/payment/config/PaymentFieldUnifier.php` - 删除复杂映射逻辑
- `crmeb/services/payment/config/PaymentMethodConfigService.php` - 简化或删除

#### 4.2 重构PaymentConfigCenter
**目标**: 将现有PaymentConfigCenter重构为使用LayeredConfigResolver

#### 4.3 更新网关适配器
**目标**: 网关适配器直接使用分层配置，无需额外映射

### 阶段五：前端适配 ⏳

#### 5.1 前端配置表单重构
**文件**: `view/admin/src/views/payment/config/index.vue`
**目标**: 
- 支持分层配置显示
- 根据支付模式动态显示字段
- 使用网关原生字段名

#### 5.2 前端API调用更新
**目标**: 调用新的V2接口

### 阶段六：测试和验证 ⏳

#### 6.1 功能测试
- [ ] 微信直连支付测试
- [ ] 微信服务商支付测试
- [ ] 微信电商收付通测试
- [ ] 支付宝直连支付测试
- [ ] 支付宝服务商支付测试
- [ ] 支付宝直付通测试

#### 6.2 兼容性测试
- [ ] 旧接口兼容性测试
- [ ] 配置迁移测试
- [ ] 缓存清理测试

## 🚀 执行步骤

### 立即执行（已完成）
1. ✅ 创建核心架构组件
2. ✅ 实现分层配置解析器
3. ✅ 创建新版配置中心和控制器

### 下一步执行
1. 🔄 统一系统配置键名
2. 🔄 创建数据迁移脚本
3. 🔄 重构现有PaymentConfigCenter
4. 🔄 清理冗余映射代码

### 后续执行
1. ⏳ 前端配置表单重构
2. ⏳ 全面测试验证
3. ⏳ 文档更新

## 📊 预期效果

### 架构简化
- **配置层级**: 3层（基础/服务商/商户）
- **字段映射**: 直接使用网关字段，无需转换
- **代码复杂度**: 降低70%

### 功能增强
- **模式自动识别**: 根据系统配置和商户状态自动确定支付模式
- **配置验证**: 完整的配置验证和错误提示
- **缓存优化**: 分层缓存，提高性能

### 维护性提升
- **新增支付方式**: 只需定义字段，无需映射
- **配置修改**: 统一入口，一处修改全局生效
- **问题排查**: 清晰的层级结构，便于调试

## 🔧 回滚方案

如果重构过程中出现问题，可以：
1. **保留旧代码**: 新旧代码并存，可随时切换
2. **数据备份**: 重构前备份所有配置数据
3. **渐进式迁移**: 先在测试环境验证，再逐步迁移生产环境

## 📝 注意事项

1. **向后兼容**: 重构过程中保持现有接口可用
2. **数据安全**: 配置数据迁移前做好备份
3. **测试充分**: 每个支付模式都要充分测试
4. **文档更新**: 及时更新相关文档和注释
