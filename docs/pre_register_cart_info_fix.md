# 预注册订单商品显示问题分析与解决方案

## 🔍 问题分析

### 问题现象
系统后台订单列表中，预注册订单不显示商品图片和商品标题，显示为空白。

### 问题根源

#### 1. **前端期望的cart_info结构**
前端订单列表显示商品信息时，期望的数据结构为：

```javascript
// 前端代码：view/admin/src/views/order/list/index.vue
val.cart_info.product.image          // 商品图片
val.cart_info.product.store_name     // 商品名称  
val.cart_info.productAttr.sku        // 商品规格
```

#### 2. **预注册订单实际的cart_info结构**
预注册订单创建时使用的数据结构为：

```json
{
    "product_id": 123,
    "store_name": "商品名称",
    "image": "图片URL", 
    "price": 100,
    "quantity": 1,
    "order_type": "pre_register"
}
```

#### 3. **结构不匹配导致显示异常**
- 前端访问 `val.cart_info.product.image` 时返回 `undefined`
- 前端访问 `val.cart_info.product.store_name` 时返回 `undefined`
- 导致商品图片和标题无法正常显示

## 🛠️ 解决方案

### 方案1：修改预注册订单创建逻辑（推荐）

**优点：** 从源头解决问题，新创建的预注册订单将正常显示
**缺点：** 不影响已存在的预注册订单

#### 实现代码：

```php
// app/controller/api/v1/PreRegister.php
'cart_info' => json_encode([
    'product_id' => $productId,
    'product' => [
        'product_id' => $productId,
        'store_name' => $product['store_name'],
        'image' => $product['image'],
        'price' => $product['price']
    ],
    'productAttr' => [
        'sku' => '预注册商品',
        'price' => $product['price'],
        'image' => $product['image']
    ],
    'cart_num' => $quantity,
    'quantity' => $quantity,
    'order_type' => 'pre_register'
])
```

### 方案2：在订单列表查询时动态修复（已实现）

**优点：** 兼容所有预注册订单，包括历史订单
**缺点：** 每次查询时需要额外处理

#### 实现代码：

```php
// app/common/repositories/store/order/StoreOrderRepository.php
private function fixPreRegisterCartInfo($order): void
{
    foreach ($order->orderProduct as $orderProduct) {
        $cartInfo = $orderProduct->cart_info;
        
        // 如果已经是正确的结构，跳过
        if (isset($cartInfo['product']) && isset($cartInfo['productAttr'])) {
            continue;
        }
        
        // 构建兼容的cart_info结构
        $fixedCartInfo = [
            'product_id' => $orderProduct->product_id,
            'product' => [
                'product_id' => $orderProduct->product_id,
                'store_name' => $cartInfo['store_name'] ?? '预注册商品',
                'image' => $cartInfo['image'] ?? '',
                'price' => $orderProduct->product_price
            ],
            'productAttr' => [
                'sku' => '预注册商品',
                'price' => $orderProduct->product_price,
                'image' => $cartInfo['image'] ?? ''
            ],
            'cart_num' => $orderProduct->product_num,
            'quantity' => $orderProduct->product_num,
            'order_type' => 'pre_register'
        ];
        
        // 修改对象属性用于显示
        $orderProduct->cart_info = $fixedCartInfo;
    }
}
```

### 方案3：数据库批量修复（可选）

**用途：** 一次性修复所有历史预注册订单数据
**文件：** `install/fix_pre_register_cart_info.php`

#### 使用方法：

```bash
# 在项目根目录执行
php install/fix_pre_register_cart_info.php
```

## 📊 修复效果对比

### 修复前
```
商品信息列: [空白图片] [空白标题]
```

### 修复后  
```
商品信息列: [商品图片] 商品名称 | 预注册商品
```

## 🔧 技术实现细节

### 1. **预注册订单识别**

```php
private function isPreRegisterOrder($order): bool
{
    // 检查订单扩展信息
    if (!empty($order->order_extend)) {
        $orderExtend = json_decode($order->order_extend, true);
        if (isset($orderExtend['order_type']) && $orderExtend['order_type'] === 'pre_register') {
            return true;
        }
    }
    
    // 检查管理员备注
    if (strpos($order->admin_mark ?? '', '预注册订单') !== false) {
        return true;
    }
    
    return false;
}
```

### 2. **cart_info结构标准化**

确保所有订单的cart_info都包含以下标准结构：

```json
{
    "product": {
        "product_id": "商品ID",
        "store_name": "商品名称", 
        "image": "商品图片",
        "price": "商品价格"
    },
    "productAttr": {
        "sku": "商品规格",
        "price": "规格价格",
        "image": "规格图片"
    },
    "cart_num": "购买数量",
    "quantity": "购买数量"
}
```

## ✅ 验证方法

### 1. **前端验证**
- 进入系统后台 → 订单管理 → 订单列表
- 查看预注册订单是否正常显示商品图片和标题

### 2. **数据验证**
```sql
-- 查看预注册订单的cart_info结构
SELECT 
    op.order_product_id,
    op.cart_info,
    o.order_sn,
    o.admin_mark
FROM eb_store_order_product op
JOIN eb_store_order o ON op.order_id = o.order_id  
WHERE o.admin_mark LIKE '%预注册订单%'
   OR o.order_extend LIKE '%pre_register%'
LIMIT 5;
```

## 🚀 部署建议

### 1. **生产环境部署步骤**
1. 备份数据库
2. 部署代码修改
3. 可选：执行数据修复脚本
4. 验证功能正常

### 2. **回滚方案**
如果出现问题，可以：
1. 回滚代码到修改前版本
2. 从备份恢复数据库（如果执行了数据修复脚本）

## 📝 总结

通过以上修复方案，预注册订单的商品信息将能够在系统后台正常显示，包括：
- ✅ 商品图片正常显示
- ✅ 商品标题正常显示  
- ✅ 商品规格显示为"预注册商品"
- ✅ 兼容历史订单和新订单
- ✅ 不影响其他类型订单的显示

这个修复确保了预注册订单与系统其他订单具有一致的显示效果和用户体验。
