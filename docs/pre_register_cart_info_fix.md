# 预注册订单商品显示问题分析与解决方案

## 🔍 问题分析

### 问题现象
系统后台订单列表中，预注册订单不显示商品图片和商品标题，显示为空白。

### 问题根源

#### 1. **前端期望的cart_info结构**
前端订单列表显示商品信息时，期望的数据结构为：

```javascript
// 前端代码：view/admin/src/views/order/list/index.vue
val.cart_info.product.image          // 商品图片
val.cart_info.product.store_name     // 商品名称
val.cart_info.productAttr.sku        // 商品规格
```

#### 2. **预注册订单原有的cart_info结构**
预注册订单创建时使用的数据结构为：

```json
{
    "product_id": 123,
    "store_name": "商品名称",
    "image": "图片URL",
    "price": 100,
    "quantity": 1,
    "order_type": "pre_register"
}
```

#### 3. **结构不匹配导致显示异常**
- 前端访问 `val.cart_info.product.image` 时返回 `undefined`
- 前端访问 `val.cart_info.product.store_name` 时返回 `undefined`
- 导致商品图片和标题无法正常显示

## 🛠️ 解决方案

### 修改预注册订单创建逻辑（已实施）

**优点：** 从源头解决问题，新创建的预注册订单将正常显示
**说明：** 历史订单不做处理，只确保新订单正常显示

#### 实现代码：

```php
// app/controller/api/v1/PreRegister.php
'cart_info' => json_encode([
    'product_id' => $productId,
    'product' => [
        'product_id' => $productId,
        'store_name' => $product['store_name'],
        'image' => $product['image'],
        'price' => $product['price']
    ],
    'productAttr' => [
        'sku' => '预注册商品',
        'price' => $product['price'],
        'image' => $product['image']
    ],
    'cart_num' => $quantity,
    'quantity' => $quantity,
    'order_type' => 'pre_register'
])
```

## 📊 修复效果对比

### 修复前
```
商品信息列: [空白图片] [空白标题]
```

### 修复后
```
商品信息列: [商品图片] 商品名称 | 预注册商品
```

## 🔧 技术实现细节

### cart_info结构标准化

确保所有订单的cart_info都包含以下标准结构：

```json
{
    "product": {
        "product_id": "商品ID",
        "store_name": "商品名称", 
        "image": "商品图片",
        "price": "商品价格"
    },
    "productAttr": {
        "sku": "商品规格",
        "price": "规格价格",
        "image": "规格图片"
    },
    "cart_num": "购买数量",
    "quantity": "购买数量"
}
```

## ✅ 验证方法

### 1. **前端验证**
- 进入系统后台 → 订单管理 → 订单列表
- 查看预注册订单是否正常显示商品图片和标题

### 2. **数据验证**
```sql
-- 查看新创建的预注册订单的cart_info结构
SELECT
    op.order_product_id,
    op.cart_info,
    o.order_sn,
    o.admin_mark
FROM eb_store_order_product op
JOIN eb_store_order o ON op.order_id = o.order_id
WHERE o.admin_mark LIKE '%预注册订单%'
   OR o.order_extend LIKE '%pre_register%'
ORDER BY o.create_time DESC
LIMIT 5;
```

## 🚀 部署建议

### 生产环境部署步骤
1. 部署代码修改
2. 验证新创建的预注册订单显示正常
3. 历史订单保持现状不做处理

## 📝 总结

通过修改预注册订单创建时的cart_info结构，新创建的预注册订单将能够在系统后台正常显示：
- ✅ 商品图片正常显示
- ✅ 商品标题正常显示
- ✅ 商品规格显示为"预注册商品"
- ✅ 不影响其他类型订单的显示
- ✅ 代码简洁，无额外性能开销

这个修复确保了新的预注册订单与系统其他订单具有一致的显示效果和用户体验。
