-- 支付宝服务商配置项
-- 添加支付宝服务商相关的系统配置

-- 1. 支付宝服务商应用ID
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝服务商应用ID', 'alipay_service_app_id', 'input', '', '支付宝直付通服务商应用ID（第三方应用授权使用）', 1, 0, 10, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_service_app_id');

-- 2. 支付宝服务商应用私钥
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝服务商应用私钥', 'alipay_service_private_key', 'textarea', '', '支付宝直付通服务商应用私钥（RSA2格式）', 1, 0, 11, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_service_private_key');

-- 3. 支付宝服务商应用公钥
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝服务商应用公钥', 'alipay_service_public_key', 'textarea', '', '支付宝直付通服务商应用公钥', 1, 0, 12, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_service_public_key');

-- 4. 支付宝服务商PID
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝服务商PID', 'alipay_service_pid', 'input', '', '支付宝服务商合作伙伴ID（PID）', 1, 0, 13, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_service_pid');

-- 5. 授权回调地址（系统后台）
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '系统后台授权回调地址', 'alipay_auth_redirect_uri', 'input', '', '支付宝第三方应用授权回调地址（系统后台），格式：https://域名/admin/merchant/alipay/auth/callback', 1, 0, 14, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_auth_redirect_uri');

-- 5.1. 商户后台授权回调地址
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '商户后台授权回调地址', 'alipay_merchant_auth_redirect_uri', 'input', '', '支付宝第三方应用授权回调地址（商户后台），格式：https://域名/merchant/alipay/auth/callback', 1, 0, 15, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_merchant_auth_redirect_uri');

-- 6. 支付宝服务商模式开关
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝服务商模式', 'alipay_service_mode', 'radio', '0=>关闭\n1=>开启', '是否开启支付宝服务商模式（直付通）', 1, 0, 16, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_service_mode');

-- 7. 支付宝服务商证书模式开关
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝服务商证书模式', 'alipay_service_cert_mode', 'radio', '0=>公钥模式\n1=>证书模式', '支付宝服务商签名验证方式', 1, 0, 17, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_service_cert_mode');

-- 8. 支付宝服务商应用证书
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝服务商应用证书', 'alipay_service_app_cert', 'textarea', '', '支付宝服务商应用公钥证书内容（证书模式使用）', 1, 0, 18, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_service_app_cert');

-- 9. 支付宝服务商公钥证书
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝服务商公钥证书', 'alipay_service_public_cert', 'textarea', '', '支付宝公钥证书内容（证书模式使用）', 1, 0, 19, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_service_public_cert');

-- 10. 支付宝服务商根证书
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝服务商根证书', 'alipay_service_root_cert', 'textarea', '', '支付宝根证书内容（证书模式使用）', 1, 0, 20, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_service_root_cert');

-- 11. 支付宝多商户模式开关
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝多商户模式', 'alipay_multi_merchant_mode', 'radio', '0=>关闭\n1=>开启', '是否开启支付宝多商户授权模式', 1, 0, 21, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'alipay_multi_merchant_mode');

-- 12. 支付宝直付通开关
INSERT INTO `eb_system_config` (`config_classify_id`, `config_name`, `config_key`, `config_type`, `config_rule`, `info`, `status`, `required`, `sort`, `user_type`)
SELECT 114, '支付宝直付通', 'open_alipay_sub_mch', 'radio', '0=>关闭\n1=>开启', '是否开启支付宝直付通功能', 1, 0, 22, 0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM `eb_system_config` WHERE `config_key` = 'open_alipay_sub_mch');
