[{"config_id": 176, "config_classify_id": 70, "config_name": "网站域名", "config_key": "site_url", "config_type": "input", "config_rule": "", "config_props": "required = true", "required": 1, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-06 23:14:38", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 177, "config_classify_id": 70, "config_name": "网站名称", "config_key": "site_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 1, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-06 23:14:38", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 178, "config_classify_id": 70, "config_name": "网站开启", "config_key": "site_open", "config_type": "switches", "config_rule": "", "config_props": "", "required": 1, "info": "", "sort": 1, "user_type": 0, "status": 1, "create_time": "2020-04-06 23:14:38", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 179, "config_classify_id": 36, "config_name": "公众号名称", "config_key": "wechat_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-06 23:14:47", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 180, "config_classify_id": 42, "config_name": "退货联系电话", "config_key": "set_phone", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-04-23 02:16:17", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 182, "config_classify_id": 36, "config_name": "微信号", "config_key": "wechat_id", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-25 19:36:48", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 183, "config_classify_id": 36, "config_name": "公众号原始id", "config_key": "wechat_sourceid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-25 19:37:15", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 184, "config_classify_id": 36, "config_name": "公众号AppID", "config_key": "wechat_appid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-25 19:37:45", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 185, "config_classify_id": 36, "config_name": "公众号AppSecret", "config_key": "wechat_appsecret", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-25 19:38:11", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 186, "config_classify_id": 36, "config_name": "微信验证TOKEN", "config_key": "wechat_token", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-25 19:38:36", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 187, "config_classify_id": 36, "config_name": "微信EncodingAESKey", "config_key": "we<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "公众号消息加解密Key,在使用安全模式情况下要填写该值，请先在管理中心修改，然后填写该值，仅限服务号和认证订阅号", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-25 19:40:47", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 188, "config_classify_id": 36, "config_name": "公众号二维码", "config_key": "wechat_qrcode", "config_type": "image", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-25 19:41:35", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 189, "config_classify_id": 36, "config_name": "公众号logo", "config_key": "wechat_avatar", "config_type": "image", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-04-25 19:43:08", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 193, "config_classify_id": 36, "config_name": "消息加解密方式", "config_key": "wechat_encode", "config_type": "radio", "config_rule": "0:明文模式\n1:兼容模式\n2:安全模式", "config_props": "defaultValue = '0'", "required": 1, "info": "如需使用安全模式请在管理中心修改，仅限服务号和认证订阅号", "sort": 1, "user_type": 0, "status": 1, "create_time": "2020-04-25 20:26:20", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 194, "config_classify_id": 93, "config_name": "一级分销比例", "config_key": "extension_one_rate", "config_type": "number", "config_rule": "", "config_props": "min = 0\nmax = 1\nprecision = 4", "required": 0, "info": "订单交易成功后给上级返佣的比例，例:0.5 = 返订单金额的50%", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-05-07 23:38:03", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 195, "config_classify_id": 93, "config_name": "二级分销比例", "config_key": "extension_two_rate", "config_type": "number", "config_rule": "", "config_props": "min = 0\nmax = 1\nprecision = 4", "required": 0, "info": "订单交易成功后给上级返佣的比例，例:0.5 = 返订单金额的50%", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-05-07 23:39:03", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 196, "config_classify_id": 92, "config_name": "分销启用", "config_key": "extension_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 1, "info": "开启:商城分销功能启用；关闭:商城分销功能不可用", "sort": 5, "user_type": 0, "status": 1, "create_time": "2020-05-07 23:41:04", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 197, "config_classify_id": 42, "config_name": "警戒库存", "config_key": "mer_store_stock", "config_type": "number", "config_rule": "", "config_props": "min = 0", "required": 0, "info": "警戒库存提醒值", "sort": 6, "user_type": 1, "status": 1, "create_time": "2020-05-17 19:23:43", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 198, "config_classify_id": 0, "config_name": "短信平台账号", "config_key": "sms_account", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "设置短信平台账号", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-05-17 23:33:58", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 199, "config_classify_id": 0, "config_name": "短信平台密码", "config_key": "sms_token", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "设置短信平台密码", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-05-17 23:34:22", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 210, "config_classify_id": 80, "config_name": "Mchid", "config_key": "pay_weixin_mchid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "受理商ID，身份标识", "sort": 100, "user_type": 0, "status": 1, "create_time": "2020-06-02 17:57:49", "linked_status": 0, "linked_id": 214, "linked_value": 0}, {"config_id": 211, "config_classify_id": 80, "config_name": "微信支付证书", "config_key": "pay_weixin_client_cert", "config_type": "file", "config_rule": "", "config_props": "", "required": 0, "info": "微信支付证书，在微信商家平台中可以下载！文件名一般为apiclient_cert.pem", "sort": 80, "user_type": 0, "status": 1, "create_time": "2020-06-02 17:58:33", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 212, "config_classify_id": 80, "config_name": "微信支付证书密钥", "config_key": "pay_weixin_client_key", "config_type": "file", "config_rule": "", "config_props": "", "required": 0, "info": "微信支付证书密钥，在微信商家平台中可以下载！文件名一般为apiclient_key.pem", "sort": 80, "user_type": 0, "status": 1, "create_time": "2020-06-02 01:58:59", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 213, "config_classify_id": 80, "config_name": "Key", "config_key": "pay_weixin_key", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "商户支付密钥Key。申请的时候自己定义的密钥", "sort": 60, "user_type": 0, "status": 1, "create_time": "2020-06-02 17:59:24", "linked_status": 1, "linked_id": 532, "linked_value": 0}, {"config_id": 214, "config_classify_id": 108, "config_name": "微信支付状态", "config_key": "pay_weixin_open", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "商城微信支付功能启用或者关闭", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-02 18:00:04", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 215, "config_classify_id": 50, "config_name": "充值注意事项", "config_key": "recharge_attention", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-02 17:49:01", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 216, "config_classify_id": 76, "config_name": "订单自动关闭时间(分钟)", "config_key": "auto_close_order_timer", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "订单提交后待支付时长,0为默认15分钟", "sort": 5, "user_type": 0, "status": 1, "create_time": "2020-06-02 17:49:01", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 217, "config_classify_id": 42, "config_name": "默认退货收货地址", "config_key": "mer_refund_address", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "设置默认退货收货地址", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-06-12 00:28:41", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 218, "config_classify_id": 42, "config_name": "默认退货收货人", "config_key": "mer_refund_user", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "设置默认退货收货人", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-06-12 00:29:15", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 219, "config_classify_id": 76, "config_name": "退款理由", "config_key": "refund_message", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 1, "user_type": 0, "status": 1, "create_time": "2020-06-12 00:34:51", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 220, "config_classify_id": 76, "config_name": "商户自动处理退款订单期限（天）", "config_key": "mer_refund_order_agree", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 1, "info": "申请退款的订单超过设置天数，将自动退款处理; 默认为0，表示自动处理退款订单期限是7天。", "sort": 2, "user_type": 0, "status": 1, "create_time": "2020-06-13 06:59:35", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 225, "config_classify_id": 93, "config_name": "佣金最低提现金额(元)", "config_key": "user_extract_min", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 2", "required": 1, "info": "佣金达到设置金额可提现", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-15 22:53:52", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 226, "config_classify_id": 93, "config_name": "佣金冻结时间(天)", "config_key": "lock_brokerage_timer", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "冻结期从用户获得返佣时(确认收货后)开始计算，如设置5天，即确认收货5天后，佣金解冻可提现；如设置0天，则无冻结期", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-16 22:36:16", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 227, "config_classify_id": 86, "config_name": "快递查询密钥", "config_key": "express_app_code", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "如果选择一号通此处可不填；\n阿里云快递查询接口密钥购买地址：https://market.aliyun.com/products/56928004/cmapi021863.html", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 10:32:16", "linked_status": 1, "linked_id": 356, "linked_value": 1}, {"config_id": 228, "config_classify_id": 45, "config_name": "空间域名 Domain", "config_key": "uploadUrl", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：https://cremb.oss-cn-beijing.aliyuncs.com", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-18 02:21:19", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 229, "config_classify_id": 45, "config_name": "accessKey", "config_key": "accessKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：LTAI4Fvu3cvFqj4Ju6Uo888", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 18:21:37", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 230, "config_classify_id": 45, "config_name": "secret<PERSON>ey", "config_key": "secret<PERSON>ey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：t2EKJTahFkTe87LnLUj3lYY88888", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 18:22:40", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 231, "config_classify_id": 45, "config_name": "存储空间名称", "config_key": "storage_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-18 02:22:57", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 232, "config_classify_id": 45, "config_name": "所属地域", "config_key": "storage_region", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：oss-cn-beijing.aliyuncs.com", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-18 02:23:21", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 233, "config_classify_id": 62, "config_name": "上传类型", "config_key": "upload_type", "config_type": "radio", "config_rule": "1:本地存储\n2:七牛云存储\n3:阿里云OSS\n4:腾讯COS\n5:华为OBS\n6:Ucloud\n7:京东云OSS\n8:天翼云OSS", "config_props": "defaultValue = '1'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 22:46:29", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 237, "config_classify_id": 46, "config_name": "空间域名 Domain", "config_key": "qiniu_uploadUrl", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：qiniu.crmeb.net", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:14:29", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 238, "config_classify_id": 46, "config_name": "accessKey", "config_key": "qiniu_accessKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：2CdCKYsRcw8dkYSOs6Z92NpDmLdLWnA888888888", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:14:51", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 239, "config_classify_id": 46, "config_name": "secret<PERSON>ey", "config_key": "qiniu_secretKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：PRUPTTIuVlUZBO4Oj0I_1HeVYg4loRDU3u688888", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:15:05", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 240, "config_classify_id": 46, "config_name": "存储空间名称", "config_key": "qiniu_storage_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:15:22", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 241, "config_classify_id": 46, "config_name": "所属地域", "config_key": "qiniu_storage_region", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：qiniu.zone.Zone_z0", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:15:40", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 242, "config_classify_id": 47, "config_name": "空间域名 Domain", "config_key": "tengxun_uploadUrl", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：cos.ap-nanjing.myqcloud.com", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:40:03", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 243, "config_classify_id": 47, "config_name": "accessKey", "config_key": "tengxun_accessKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：AKIDJSO7cuUrVx96nFIUDhvud6JGwMX88888", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:40:32", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 244, "config_classify_id": 47, "config_name": "secret<PERSON>ey", "config_key": "tengxun_secretKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：AKIDJSO7cuUrVx96nFIUDhvud", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:40:48", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 245, "config_classify_id": 47, "config_name": "存储空间名称", "config_key": "tengxun_storage_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:41:06", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 246, "config_classify_id": 47, "config_name": "所属地域", "config_key": "tengxun_storage_region", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：ap-nanjing", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-17 23:41:39", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 247, "config_classify_id": 112, "config_name": "微信小程序AppID", "config_key": "routine_appId", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-18 18:07:31", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 248, "config_classify_id": 112, "config_name": "小程序AppSecret", "config_key": "routine_appsecret", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-18 18:09:32", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 249, "config_classify_id": 112, "config_name": "小程序授权logo", "config_key": "routine_logo", "config_type": "image", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-18 18:10:36", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 250, "config_classify_id": 112, "config_name": "小程序名称", "config_key": "routine_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-18 18:11:07", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 254, "config_classify_id": 81, "config_name": "Mchid", "config_key": "pay_routine_mchid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "受理商ID，身份标识", "sort": 100, "user_type": 0, "status": 1, "create_time": "2020-06-19 10:46:59", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 255, "config_classify_id": 81, "config_name": "Key", "config_key": "pay_routine_key", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-19 10:47:21", "linked_status": 1, "linked_id": 533, "linked_value": 0}, {"config_id": 256, "config_classify_id": 81, "config_name": "小程序支付证书", "config_key": "pay_routine_client_cert", "config_type": "file", "config_rule": "", "config_props": "", "required": 0, "info": "微信支付证书，在微信商家平台中可以下载！文件名一般为apiclient_cert.pem", "sort": 90, "user_type": 0, "status": 1, "create_time": "2020-06-19 10:47:46", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 257, "config_classify_id": 81, "config_name": "小程序支付证书密钥", "config_key": "pay_routine_client_key", "config_type": "file", "config_rule": "", "config_props": "", "required": 0, "info": "微信支付证书密钥，在微信商家平台中可以下载！文件名一般为apiclient_key.pem", "sort": 80, "user_type": 0, "status": 1, "create_time": "2020-06-19 10:48:02", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 258, "config_classify_id": 50, "config_name": "余额充值开关", "config_key": "recharge_switch", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 5, "user_type": 0, "status": 1, "create_time": "2020-06-19 15:47:12", "linked_status": 1, "linked_id": 260, "linked_value": 1}, {"config_id": 259, "config_classify_id": 50, "config_name": "用户最低充值金额", "config_key": "store_user_min_recharge", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 2", "required": 0, "info": "", "sort": 2, "user_type": 0, "status": 1, "create_time": "2020-06-19 15:47:40", "linked_status": 1, "linked_id": 258, "linked_value": 1}, {"config_id": 260, "config_classify_id": 50, "config_name": "余额功能", "config_key": "balance_func_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "商城余额功能启用或者关闭", "sort": 10, "user_type": 0, "status": 1, "create_time": "2020-06-19 15:54:16", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 261, "config_classify_id": 108, "config_name": "余额支付状态", "config_key": "yue_pay_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "开启余额支付，请确认:平台后台>营销>余额充值>余额设置>余额功能已开启", "sort": 3, "user_type": 0, "status": 1, "create_time": "2020-06-19 00:02:16", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 264, "config_classify_id": 92, "config_name": "分销说明", "config_key": "promoter_explain", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-25 07:32:21", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 265, "config_classify_id": 95, "config_name": "商户设置礼包最大数量", "config_key": "max_bag_number", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "允许每个商户设置分销礼包的最大数量", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-06-24 23:48:27", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 266, "config_classify_id": 72, "config_name": "商城logo", "config_key": "site_logo", "config_type": "image", "config_rule": "", "config_props": "guidance_image=\"/statics/system/appSetLogo.png\"", "required": 0, "info": "移动端商城logo 优先读取商城装修中上传的logo,如未装修设置，默认读取此处logo;\nPC端商城logo 默认读取此处logo. \n建议尺寸：112*40px,  格式png.", "sort": 60, "user_type": 0, "status": 1, "create_time": "2020-06-25 19:41:23", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 268, "config_classify_id": 72, "config_name": "商城分享标题", "config_key": "share_title", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 100, "user_type": 0, "status": 1, "create_time": "2020-06-30 00:16:30", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 269, "config_classify_id": 72, "config_name": "商城分享简介", "config_key": "share_info", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 90, "user_type": 0, "status": 1, "create_time": "2020-06-30 00:16:57", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 270, "config_classify_id": 72, "config_name": "商城分享图片", "config_key": "share_pic", "config_type": "image", "config_rule": "", "config_props": "guidance_image=\"/statics/system/mobileSet02.png\"", "required": 0, "info": "建议尺寸：300*300px，格式jpg", "sort": 80, "user_type": 0, "status": 1, "create_time": "2020-06-30 00:17:23", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 271, "config_classify_id": 41, "config_name": "发货提醒", "config_key": "sms_fahuo_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 19:00:13", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 272, "config_classify_id": 41, "config_name": "确认收货短信提醒", "config_key": "sms_take_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 19:20:51", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 273, "config_classify_id": 41, "config_name": "用户下单通知提醒", "config_key": "sms_pay_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 19:25:32", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 274, "config_classify_id": 41, "config_name": "改价提醒", "config_key": "sms_revision_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 19:30:04", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 275, "config_classify_id": 41, "config_name": "提醒付款通知", "config_key": "sms_pay_false_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 19:42:41", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 276, "config_classify_id": 41, "config_name": "商家拒绝退款提醒", "config_key": "sms_refund_fail_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 19:55:05", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 277, "config_classify_id": 41, "config_name": "商家同意退款提醒", "config_key": "sms_refund_success_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 20:01:32", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 278, "config_classify_id": 41, "config_name": "退款确认提醒", "config_key": "sms_refund_confirm_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 20:06:14", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 279, "config_classify_id": 41, "config_name": "管理员下单提醒", "config_key": "sms_admin_pay_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 20:17:42", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 280, "config_classify_id": 41, "config_name": "管理员退货提醒", "config_key": "sms_admin_return_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 20:31:13", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 281, "config_classify_id": 41, "config_name": "管理员确认收货提醒", "config_key": "sms_admin_take_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 20:32:01", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 282, "config_classify_id": 41, "config_name": "退货信息提醒", "config_key": "sms_admin_postage_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-06-30 22:04:27", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 283, "config_classify_id": 71, "config_name": "后台登录页logo", "config_key": "sys_login_logo", "config_type": "image", "config_rule": "", "config_props": "guidance_image=\"/statics/system/adminSet03.png\"", "required": 0, "info": "建议尺寸：200*72px，格式png", "sort": 9, "user_type": 0, "status": 1, "create_time": "2020-07-05 23:20:18", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 284, "config_classify_id": 71, "config_name": "后台登录页标题", "config_key": "sys_login_title", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-07-07 00:39:25", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 285, "config_classify_id": 71, "config_name": "后台菜单顶部logo", "config_key": "sys_menu_logo", "config_type": "image", "config_rule": "", "config_props": "guidance_image=\"/statics/system/adminSet01.png\"", "required": 0, "info": "建议尺寸：236*64px，格式png", "sort": 11, "user_type": 0, "status": 1, "create_time": "2020-07-09 00:31:46", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 286, "config_classify_id": 71, "config_name": "后台菜单缩进小logo", "config_key": "sys_menu_slogo", "config_type": "image", "config_rule": "", "config_props": "guidance_image=\"/statics/system/adminSet02.png\"", "required": 0, "info": "当后台菜单样式切换为：样式一，此处需上传LOGO； 建议尺寸：100*100px，格式png", "sort": 10, "user_type": 0, "status": 1, "create_time": "2020-07-09 00:31:46", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 288, "config_classify_id": 77, "config_name": "开启商户入驻", "config_key": "mer_intention_open", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "开启后，商城移动端和PC端商户入驻功能正常启用。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-07-26 22:47:45", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 289, "config_classify_id": 41, "config_name": "预售尾款支付通知", "config_key": "sms_pay_presell_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 1, "user_type": 0, "status": 0, "create_time": "2020-11-30 01:46:45", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 294, "config_classify_id": 78, "config_name": "开启直播免审核", "config_key": "broadcast_room_type", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 2, "user_type": 0, "status": 1, "create_time": "2020-07-29 00:50:35", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 295, "config_classify_id": 85, "config_name": "采集商品接口方式", "config_key": "copy_product_status", "config_type": "radio", "config_rule": "0:关闭\n1:99API自有账号 \n2:一号通", "config_props": "defaultValue = '0'", "required": 0, "info": "1.一号通方式：是指通过注册CRMEB一号通方式，可对接更多服务接口，方便运营。\n2.99API自有账号：是指用户在99API平台已注册并购买商品采集的服务，可选择继续使用。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-07-30 07:49:01", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 296, "config_classify_id": 85, "config_name": "复制商品密钥", "config_key": "copy_product_apikey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-07-30 07:49:46", "linked_status": 1, "linked_id": 295, "linked_value": 1}, {"config_id": 297, "config_classify_id": 78, "config_name": "开启直播商品免审核", "config_key": "broadcast_goods_type", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "是否开启直播商品免审核", "sort": 1, "user_type": 0, "status": 1, "create_time": "2020-07-30 00:00:53", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 298, "config_classify_id": 88, "config_name": "腾讯地图KEY", "config_key": "tx_map_key", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-07-31 19:55:13", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 299, "config_classify_id": 52, "config_name": "开启门店自提", "config_key": "mer_take_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-07-31 19:59:14", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 300, "config_classify_id": 52, "config_name": "自提点名称", "config_key": "mer_take_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-07-31 20:01:08", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 301, "config_classify_id": 52, "config_name": "自提点手机号", "config_key": "mer_take_phone", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-07-31 20:02:04", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 302, "config_classify_id": 52, "config_name": "自提点地址", "config_key": "mer_take_address", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-07-31 20:03:57", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 303, "config_classify_id": 52, "config_name": "店铺经纬度", "config_key": "mer_take_location", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-07-31 20:16:18", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 304, "config_classify_id": 52, "config_name": "自提点营业日期", "config_key": "mer_take_day", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-07-31 20:17:06", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 305, "config_classify_id": 52, "config_name": "自提点营业时间", "config_key": "mer_take_time", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2020-07-31 20:17:39", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 306, "config_classify_id": 76, "config_name": "订单自动收货时间(天)", "config_key": "auto_take_order_timer", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "订单自动收货时间是自发货日算起，例如:设置10天，表示自发货日起10天系统会自动收货；默认为0时，表示自动收货时间是15天。", "sort": 4, "user_type": 0, "status": 1, "create_time": "2020-08-04 06:57:23", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 307, "config_classify_id": 75, "config_name": "默认赠送复制次数", "config_key": "copy_product_defaul", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "默认给商户赠送的商品采集次数", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-08-05 20:16:20", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 308, "config_classify_id": 78, "config_name": "是否展示店铺", "config_key": "hide_mer_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'\ninactiveValue = '1'\nactiveValue = '0'", "required": 0, "info": "开启移动端商城正常展示店铺信息；关闭移则隐藏店铺信息", "sort": 5, "user_type": 0, "status": 1, "create_time": "2020-08-16 23:03:44", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 309, "config_classify_id": 41, "config_name": "直播审核通过主播通知", "config_key": "sms_broadcast_room_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2020-09-07 23:53:42", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 310, "config_classify_id": 89, "config_name": "验证码时效配置(分钟)", "config_key": "sms_time", "config_type": "number", "config_rule": "", "config_props": "min = 1\nprecision = 0", "required": 0, "info": "短信验证码过期时间", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-09-07 23:53:42", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 311, "config_classify_id": 110, "config_name": "打印机自动打印", "config_key": "printing_auto_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "开启后订单支付成功后自动打印", "sort": 8, "user_type": 1, "status": 1, "create_time": "2020-10-17 11:25:09", "linked_status": 1, "linked_id": 316, "linked_value": 1}, {"config_id": 312, "config_classify_id": 108, "config_name": "支付宝支付状态", "config_key": "alipay_open", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "商城支付宝支付功能启用或者关闭", "sort": 0, "user_type": 0, "status": 1, "create_time": "2020-10-22 11:40:41", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 316, "config_classify_id": 110, "config_name": "打印机开启", "config_key": "printing_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 9, "user_type": 1, "status": 1, "create_time": "2020-11-10 17:59:49", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 317, "config_classify_id": 42, "config_name": "开启发票", "config_key": "mer_open_receipt", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 7, "user_type": 1, "status": 1, "create_time": "2020-11-13 19:40:10", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 318, "config_classify_id": 54, "config_name": "虚拟成团", "config_key": "ficti_status", "config_type": "radio", "config_rule": "0.关闭\n1.开启", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-01-06 19:41:40", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 319, "config_classify_id": 54, "config_name": "真实成团最小比例", "config_key": "group_buying_rate", "config_type": "number", "config_rule": "", "config_props": "min = 0\nmax = 100\nprecision = 0", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-01-06 19:48:07", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 320, "config_classify_id": 78, "config_name": "是否展示店铺距离", "config_key": "mer_location", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "移动端是否展示店铺距离与用户定位", "sort": 0, "user_type": 0, "status": 0, "create_time": "2021-01-08 18:11:47", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 321, "config_classify_id": 72, "config_name": "移动端登录logo", "config_key": "login_logo", "config_type": "image", "config_rule": "", "config_props": "guidance_image=\"/statics/system/mobileSet01.png\"", "required": 0, "info": "建议尺寸：120*120px，格式jpg", "sort": 70, "user_type": 0, "status": 1, "create_time": "2021-01-17 23:38:19", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 322, "config_classify_id": 41, "config_name": "入驻申请通过提醒", "config_key": "sms_apply_mer_succ_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 2, "user_type": 0, "status": 0, "create_time": "2021-01-19 19:57:57", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 323, "config_classify_id": 41, "config_name": "入驻申请未通过提醒", "config_key": "sms_apply_mer_fail_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 2, "user_type": 0, "status": 0, "create_time": "2021-01-19 19:58:29", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 324, "config_classify_id": 55, "config_name": "商户最低提现金额", "config_key": "extract_minimum_line", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 2", "required": 0, "info": "指商户的余额至少大于该金额部分，才可以提现，设置为0时默认商户余额可以全部提现", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-03-18 19:54:55", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 325, "config_classify_id": 55, "config_name": "商户每笔最小提现额度", "config_key": "extract_minimum_num", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 2", "required": 0, "info": "指商户的每次申请转账最小的金额；设置为0时默认不限制最小额度", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-03-18 19:55:46", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 327, "config_classify_id": 55, "config_name": "商户每笔最高提现金额", "config_key": "extract_maxmum_num", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 2", "required": 0, "info": "商户每次提现申请的最高额度，设置0时默认不限制", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-04-21 01:46:39", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 329, "config_classify_id": 96, "config_name": "客服电话", "config_key": "sys_phone", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "平台客服电话", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-05-08 01:33:03", "linked_status": 1, "linked_id": 435, "linked_value": 2}, {"config_id": 330, "config_classify_id": 41, "config_name": "直播未通过通知", "config_key": "sms_broadcast_room_fail", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2021-05-24 00:25:06", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 331, "config_classify_id": 41, "config_name": "到货提醒短信通知", "config_key": "procudt_increase_sms", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2021-05-24 01:10:57", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 332, "config_classify_id": 75, "config_name": "到货通知", "config_key": "procudt_increase_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 4, "user_type": 0, "status": 1, "create_time": "2021-05-24 17:23:20", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 334, "config_classify_id": 94, "config_name": "微信到账方式", "config_key": "sys_extension_type", "config_type": "radio", "config_rule": "0:线下转账\n1:企业付款到零钱\n2:商家转账到零钱", "config_props": "defaultValue = '0'", "required": 0, "info": "微信提现到零钱为自动到账（需要开通微信: 企业到零钱/商家转账到零钱，并保配置微信支付证书正确)，其他方式均需要手动转账。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-05-27 14:45:10", "linked_status": 0, "linked_id": 531, "linked_value": 2}, {"config_id": 335, "config_classify_id": 71, "config_name": "版本号展示", "config_key": "sys_open_version", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '1'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-01 17:37:55", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 336, "config_classify_id": 57, "config_name": "积分开关", "config_key": "integral_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 1, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-09 02:08:47", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 337, "config_classify_id": 57, "config_name": "积分清除时间", "config_key": "integral_clear_time", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "指累计积分的清除时间。单位：月；例如：设置为6，指每隔6个月清除前6个月的积分，比如6月30日，自动清除上一年7月1日-12月31日的积分。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-09 02:10:08", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 338, "config_classify_id": 57, "config_name": "下单赠送积分", "config_key": "integral_order_rate", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "指实际支付1元，赠送多少积分，单位：分", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-09 02:14:45", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 339, "config_classify_id": 57, "config_name": "下单赠送积分冻结期", "config_key": "integral_freeze", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "下单赠送积分冻结多少天后才能使用，单位：天", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-09 02:16:56", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 340, "config_classify_id": 57, "config_name": "邀请好友赠送积分", "config_key": "integral_user_give", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "指邀请好友成功登录商城后，赠送给邀请人的积分数；单位：分", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-09 02:17:38", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 341, "config_classify_id": 57, "config_name": "积分开启", "config_key": "mer_integral_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "开启后商品就可以参与积分抵扣", "sort": 0, "user_type": 1, "status": 1, "create_time": "2021-06-10 00:47:31", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 342, "config_classify_id": 57, "config_name": "默认抵扣比例（%）", "config_key": "mer_integral_rate", "config_type": "number", "config_rule": "", "config_props": "min = 0\nmax = 100\nprecision = 2", "required": 0, "info": "指积分抵扣金额占单个商品销售价比例，单位：%", "sort": 0, "user_type": 1, "status": 1, "create_time": "2021-06-10 00:52:02", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 343, "config_classify_id": 57, "config_name": "积分抵用金额", "config_key": "integral_money", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 2", "required": 0, "info": "指1积分抵用多少金额,单位(元)", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-10 17:37:34", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 344, "config_classify_id": 36, "config_name": "开启公众号二维码", "config_key": "open_wechat_share", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "开启后扫码海报中的二维码会先关注公众号", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-21 02:12:44", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 345, "config_classify_id": 83, "config_name": "服务商商户ID", "config_key": "wechat_service_merid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-23 19:03:10", "linked_status": 1, "linked_id": 580, "linked_value": 1}, {"config_id": 346, "config_classify_id": 83, "config_name": "服务商Key", "config_key": "wechat_service_key", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-23 19:03:44", "linked_status": 1, "linked_id": 580, "linked_value": 1}, {"config_id": 347, "config_classify_id": 83, "config_name": "服务商V3Key", "config_key": "wechat_service_v3key", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-23 19:04:29", "linked_status": 1, "linked_id": 580, "linked_value": 1}, {"config_id": 348, "config_classify_id": 83, "config_name": "服务商支付证书", "config_key": "wechat_service_client_cert", "config_type": "file", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-23 19:05:50", "linked_status": 1, "linked_id": 580, "linked_value": 1}, {"config_id": 349, "config_classify_id": 83, "config_name": "服务商支付证书密钥", "config_key": "wechat_service_client_key", "config_type": "file", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-23 19:06:31", "linked_status": 1, "linked_id": 580, "linked_value": 1}, {"config_id": 350, "config_classify_id": 83, "config_name": "服务商支付证书编号", "config_key": "wechat_service_serial_no", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-23 19:07:14", "linked_status": 1, "linked_id": 580, "linked_value": 1}, {"config_id": 351, "config_classify_id": 59, "config_name": "公众平台开放应用APPID", "config_key": "wecaht_app_appid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-24 23:56:51", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 352, "config_classify_id": 59, "config_name": "微信应用Appsecret", "config_key": "wechat_app_appsecret", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-06-24 23:57:26", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 354, "config_classify_id": 41, "config_name": "积分即将到期提醒", "config_key": "integral_sms", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2021-07-09 00:06:53", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 355, "config_classify_id": 41, "config_name": "申请分账审核通知", "config_key": "applyments_sms", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2021-07-09 00:11:17", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 356, "config_classify_id": 86, "config_name": "物流查询接口方式", "config_key": "crmeb_serve_express", "config_type": "radio", "config_rule": "2:一号通\n1:阿里云物流", "config_props": "defaultValue = '2'", "required": 0, "info": "1.一号通方式：是指通过注册CRMEB一号通方式，可对接更多服务接口，方便运营；\n2.阿里云物流查询方式：是指用户在阿里云平台自行注册并购买物流查询服务的情况，可选择继续使用；", "sort": 10, "user_type": 0, "status": 1, "create_time": "2021-07-17 00:54:30", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 357, "config_classify_id": 87, "config_name": "电子面单", "config_key": "crmeb_serve_dump", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-07-17 00:57:27", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 358, "config_classify_id": 111, "config_name": "发货地址", "config_key": "mer_from_addr", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "电子面单默认发货地址", "sort": 0, "user_type": 1, "status": 1, "create_time": "2021-07-22 14:56:53", "linked_status": 1, "linked_id": 535, "linked_value": 1}, {"config_id": 359, "config_classify_id": 55, "config_name": "商户余额冻结期(天)", "config_key": "mer_lock_time", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "设置余额冻结时间,0为不冻结", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-07-25 18:03:19", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 360, "config_classify_id": 76, "config_name": "售后时长(天)", "config_key": "sys_refund_timer", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "例如：设置10天指用户确认收货10天内可以退货，默认为0，表示用户在确认收货后15天就不能退货了。", "sort": 3, "user_type": 0, "status": 1, "create_time": "2021-08-03 01:25:17", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 361, "config_classify_id": 78, "config_name": "为你推荐方式", "config_key": "recommend_type", "config_type": "radio", "config_rule": "2:默认推荐\n1:星级推荐\n3:创建时间", "config_props": "defaultValue = '2'", "required": 0, "info": "设置移动端商城首页【为你推荐】中商品展示顺序。\n默认推荐：按用户浏览习惯算法推荐；星级推荐：星级越高，展示在最上面；创建时间：最新创建的商品展示在最上面", "sort": 4, "user_type": 0, "status": 1, "create_time": "2021-08-23 02:45:20", "linked_status": 1, "linked_id": 447, "linked_value": 1}, {"config_id": 362, "config_classify_id": 90, "config_name": "统计代码", "config_key": "static_script", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 1, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-09-22 09:58:41", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 363, "config_classify_id": 60, "config_name": "开启会员", "config_key": "member_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 10, "user_type": 0, "status": 1, "create_time": "2021-10-07 23:02:29", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 364, "config_classify_id": 60, "config_name": "下单可获得成长值", "config_key": "member_pay_num", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "用户每成功支付一次，可以获得多少成长值", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-10-08 07:05:13", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 365, "config_classify_id": 60, "config_name": "签到可获得成长值", "config_key": "member_sign_num", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "用户签到一次，可以获得多少成长值", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-10-08 07:06:36", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 366, "config_classify_id": 60, "config_name": "评价可获得成长值", "config_key": "member_reply_num", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "用户评价一次，可以获得多少成长值", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-10-08 07:07:36", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 367, "config_classify_id": 60, "config_name": "邀请可获得成长值", "config_key": "member_share_num", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "注:邀请者与被邀请者各得所设置的经验值", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-10-08 07:08:58", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 368, "config_classify_id": 75, "config_name": "自动解析复制口令", "config_key": "copy_command_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 5, "user_type": 0, "status": 1, "create_time": "2021-10-14 23:19:43", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 369, "config_classify_id": 75, "config_name": "商品评论开启", "config_key": "sys_reply_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '1'", "required": 0, "info": "开启后，移动端展示商品评论", "sort": 1, "user_type": 0, "status": 1, "create_time": "2021-10-18 22:38:49", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 370, "config_classify_id": 61, "config_name": "社区开启", "config_key": "community_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 10, "user_type": 0, "status": 1, "create_time": "2021-10-29 17:58:41", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 371, "config_classify_id": 61, "config_name": "图文免审核", "config_key": "community_audit", "config_type": "radio", "config_rule": "0:审核\n1:免审核", "config_props": "", "required": 0, "info": "", "sort": 9, "user_type": 0, "status": 1, "create_time": "2021-10-29 18:01:33", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 372, "config_classify_id": 61, "config_name": "允许发帖用户", "config_key": "community_auth", "config_type": "radio", "config_rule": "0:全部用户\n1:绑定手机号用户", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-10-29 18:04:32", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 373, "config_classify_id": 61, "config_name": "社区评论开关", "config_key": "community_reply_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-11-02 23:50:47", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 374, "config_classify_id": 61, "config_name": "评论用户", "config_key": "community_reply_auth", "config_type": "radio", "config_rule": "0:全部用户\n1:绑定手机号用户", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-11-12 17:59:16", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 375, "config_classify_id": 60, "config_name": "社区种草内容获得成长值", "config_key": "member_community_num", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "用户发布一次种草内容，可以获得多少成长值", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-11-13 02:11:27", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 376, "config_classify_id": 107, "config_name": "商户客服类型", "config_key": "services_type", "config_type": "radio", "config_rule": "0:关闭\n1:系统客服\n2:拨打电话\n3:企业微信\n4:跳转链接", "config_props": "defaultValue='0'", "required": 0, "info": "系统客服：点击联系客服使用系统的自带客服；拨打电话：点击联系客服拨打客服电话；跳转链接：跳转外部链接联系客服", "sort": 0, "user_type": 1, "status": 1, "create_time": "2021-11-15 03:26:33", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 378, "config_classify_id": 77, "config_name": "商户资质是否必传", "config_key": "sys_bases_status", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "不选时，默认必填", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-06 06:37:42", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 379, "config_classify_id": 45, "config_name": "CDN域名", "config_key": "oss_cdn", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "CDN域名，没有可不填写", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-06 17:57:33", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 380, "config_classify_id": 46, "config_name": "CDN域名", "config_key": "qiniu_cdn", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "CDN域名，没有可不填写", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-06 17:59:10", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 381, "config_classify_id": 47, "config_name": "CDN域名", "config_key": "tengxun_cdn", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "CDN域名，没有可不填写", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-06 18:00:03", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 382, "config_classify_id": 94, "config_name": "提现方式", "config_key": "withdraw_type", "config_type": "checkbox", "config_rule": "0:银行卡\n1:微信\n2:支付宝\n4:余额", "config_props": "defaultValue = '1'", "required": 0, "info": "全未选择，默认使用余额提现方式", "sort": 100, "user_type": 0, "status": 1, "create_time": "2021-12-13 09:29:28", "linked_status": 0, "linked_id": 531, "linked_value": 1}, {"config_id": 383, "config_classify_id": 60, "config_name": "会员权益开关", "config_key": "member_interests_status", "config_type": "radio", "config_rule": "0:开启\n1:关闭", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 0, "create_time": "2021-12-12 19:02:48", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 384, "config_classify_id": 45, "config_name": "是否开启图片压缩", "config_key": "thumb_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-13 19:07:59", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 385, "config_classify_id": 47, "config_name": "是否开启图片压缩", "config_key": "tengxun_thumb_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue='0'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-13 19:09:26", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 386, "config_classify_id": 45, "config_name": "图片压缩比例", "config_key": "thumb_rate", "config_type": "number", "config_rule": "", "config_props": "min = 1\nmax = 100\ndefaultValue = 100", "required": 0, "info": "（1%～100%）", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-13 19:11:02", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 396, "config_classify_id": 61, "config_name": "评论审核", "config_key": "community_reply_audit", "config_type": "radio", "config_rule": "0:免审核\n1:审核", "config_props": "defaultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-16 18:26:01", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 398, "config_classify_id": 63, "config_name": "IOS更新地址", "config_key": "iosAddress", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-26 19:24:09", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 399, "config_classify_id": 63, "config_name": "安卓更新地址", "config_key": "android<PERSON><PERSON><PERSON>", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-26 19:39:56", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 400, "config_classify_id": 63, "config_name": "当前版本", "config_key": "appVersion", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-26 19:40:16", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 401, "config_classify_id": 63, "config_name": "是否强制更新", "config_key": "openUpgrade", "config_type": "radio", "config_rule": "1:开启\n0:关闭", "config_props": "defaultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-26 19:41:14", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 402, "config_classify_id": 70, "config_name": "备案号展示", "config_key": "beian_sn", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "展示在移动端底部", "sort": 0, "user_type": 0, "status": 1, "create_time": "2021-12-27 18:03:33", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 403, "config_classify_id": 89, "config_name": "验证码请求频率(条)", "config_key": "sms_limit", "config_type": "number", "config_rule": "", "config_props": "defaultValue=0", "required": 0, "info": "每分钟短信请求不能超过的条数", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-01-13 18:35:40", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 404, "config_classify_id": 75, "config_name": "开启自动好评", "config_key": "open_auto_reply", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '1'", "required": 0, "info": "开启后，会自动对7天内未评价的订单默认五星好评", "sort": 1, "user_type": 0, "status": 1, "create_time": "2022-01-17 22:41:12", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 405, "config_classify_id": 64, "config_name": "同城配送开关", "config_key": "delivery_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-02-11 20:18:24", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 406, "config_classify_id": 64, "config_name": "<PERSON><PERSON><PERSON><PERSON>", "config_key": "uupt_appkey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "UU跑腿配置", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-02-11 20:19:11", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 407, "config_classify_id": 64, "config_name": "AppId", "config_key": "uupt_app_id", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "UU跑腿配置", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-02-11 20:20:51", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 408, "config_classify_id": 64, "config_name": "OpenId", "config_key": "uupt_open_id", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "UU跑腿配置", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-02-11 20:22:02", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 409, "config_classify_id": 64, "config_name": "配送类型", "config_key": "delivery_type", "config_type": "radio", "config_rule": "1:达达\n2:<PERSON><PERSON>", "config_props": "defaultValue = '1'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-02-11 20:24:23", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 410, "config_classify_id": 64, "config_name": "<PERSON><PERSON><PERSON><PERSON>", "config_key": "dada_app_key", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "达达配置", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-02-11 20:25:31", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 411, "config_classify_id": 64, "config_name": "AppSercret", "config_key": "dada_app_sercret", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "达达配置", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-02-11 20:26:12", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 412, "config_classify_id": 64, "config_name": "商户ID", "config_key": "dada_source_id", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "达达商户ID", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-02-11 20:27:16", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 413, "config_classify_id": 65, "config_name": "空间域名 Domain", "config_key": "obs_uploadUrl", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb.obs.cn-southwest-2.myhuaweicloud.com", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 19:15:17", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 414, "config_classify_id": 65, "config_name": "accessKey", "config_key": "obs_accessKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：AUL8K0BMYLSZTJDT88888", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 19:15:54", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 415, "config_classify_id": 65, "config_name": "secret<PERSON>ey", "config_key": "obs_secretKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：SwjS5huuunY6Bzjrhr7RGvOIA3kHkfNZu", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 19:16:24", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 416, "config_classify_id": 65, "config_name": "存储空间名称", "config_key": "obs_storage_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 19:16:56", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 417, "config_classify_id": 65, "config_name": "所属地域", "config_key": "obs_storage_region", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：obs.cn-southwest-2.myhuaweicloud.com", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 19:17:39", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 418, "config_classify_id": 65, "config_name": "CDN域名", "config_key": "obs_cdn", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "CDN域名，没有可不填写", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 19:18:03", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 419, "config_classify_id": 66, "config_name": "空间域名 Domain", "config_key": "uc_uploadUrl", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb.cn-bj.ufileos.com", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 20:00:20", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 420, "config_classify_id": 66, "config_name": "accessKey", "config_key": "uc_accessKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：TOKEN_8055d01e-f4fd-416f-b247-e1a9922", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 20:00:57", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 421, "config_classify_id": 66, "config_name": "secret<PERSON>ey", "config_key": "uc_secretKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：e177d453-8a42-4519-908d-362", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 20:03:46", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 422, "config_classify_id": 66, "config_name": "存储空间名称", "config_key": "uc_storage_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 20:04:26", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 424, "config_classify_id": 66, "config_name": "CDN域名", "config_key": "uc_cdn", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "CDN域名，没有可不填写", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-01 20:05:13", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 425, "config_classify_id": 67, "config_name": "是否开启图片压缩", "config_key": "local_thumb_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '0'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-03 17:41:57", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 426, "config_classify_id": 67, "config_name": "图片压缩比例", "config_key": "local_thumb_rate", "config_type": "number", "config_rule": "", "config_props": "min = 1\nmax = 100\ndefaultValue = 100", "required": 0, "info": "（1%～100%）", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-03 17:44:05", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 427, "config_classify_id": 47, "config_name": "图片压缩比例", "config_key": "tengxun_thumb_rate", "config_type": "number", "config_rule": "", "config_props": "min = 1\nmax = 100\ndefaultValue = 100", "required": 0, "info": "（1%～100%）", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-03 17:52:14", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 428, "config_classify_id": 46, "config_name": "是否开启图片压缩", "config_key": "qiniu_thumb_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue='0'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-03 17:53:09", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 429, "config_classify_id": 46, "config_name": "图片压缩比例", "config_key": "qiniu_thumb_rate", "config_type": "number", "config_rule": "", "config_props": "min = 1\nmax = 100\ndefaultValue = 100", "required": 0, "info": "（1%～100%）", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-03 17:53:39", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 430, "config_classify_id": 65, "config_name": "是否开启图片压缩", "config_key": "obs_thumb_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-03 17:54:46", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 431, "config_classify_id": 65, "config_name": "图片压缩比例", "config_key": "obs_thumb_rate", "config_type": "number", "config_rule": "", "config_props": "min = 1\nmax = 100\ndefaultValue = 100", "required": 0, "info": "（1%～100%）", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-03 17:55:22", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 432, "config_classify_id": 66, "config_name": "是否开启图片压缩", "config_key": "uc_thumb_status", "config_type": "switches", "config_rule": "", "config_props": "defaultValue='0'", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-03 17:57:12", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 433, "config_classify_id": 66, "config_name": "图片压缩比例", "config_key": "uc_thumb_rate", "config_type": "number", "config_rule": "", "config_props": "min = 1\nmax = 100\ndefaultValue = 100", "required": 0, "info": "（1%～100%）", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-03 17:58:59", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 434, "config_classify_id": 105, "config_name": "商城用户强制手机号登录(绑定)", "config_key": "is_phone_login", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-03-25 18:44:06", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 435, "config_classify_id": 96, "config_name": "平台客服类型", "config_key": "sys_service_switch", "config_type": "radio", "config_rule": "0:关闭\n1:系统客服\n2:拨打电话\n3:企业微信\n4:跳转链接", "config_props": "defaultValue='0'", "required": 0, "info": "系统客服：选择即开启系统自带客服；电话客服：选择并设置客服接听电话，即开启电话客服；企业微信客服：选择并配置企业微信客服，移动端商城开启企微客服，暂不支持PC商城；跳转链接：选择并设置跳转外部链接，即开启第三方客服。", "sort": 10, "user_type": 0, "status": 1, "create_time": "2022-03-29 10:26:10", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 436, "config_classify_id": 71, "config_name": "后台登录页面左侧轮播图", "config_key": "sys_login_banner", "config_type": "images", "config_rule": "", "config_props": "guidance_image=\"/statics/system/adminSet04.png\"", "required": 0, "info": "建议尺寸：510*482px，格式jpg", "sort": 5, "user_type": 0, "status": 1, "create_time": "2022-03-30 18:52:58", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 437, "config_classify_id": 92, "config_name": "分销内购", "config_key": "extension_self", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "开启:分销员自己购买，享受一级返佣，上一级享受二级返佣；关闭:分销员自己购买没有返佣", "sort": 4, "user_type": 0, "status": 1, "create_time": "2022-04-06 00:38:19", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 438, "config_classify_id": 92, "config_name": "分销限时开关", "config_key": "extension_limit", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "开启:根据设置的分销绑定时段返佣；关闭:默认永久绑定(此处不建议频繁修改，请谨慎操作)", "sort": 3, "user_type": 0, "status": 1, "create_time": "2022-04-06 08:40:19", "linked_status": 1, "linked_id": 196, "linked_value": 1}, {"config_id": 439, "config_classify_id": 92, "config_name": "分销绑定时间设置(天)", "config_key": "extension_limit_day", "config_type": "number", "config_rule": "", "config_props": "min = 0\ndefaultValue = 15", "required": 0, "info": "指定绑定关系成功至自动解绑之间的天数，自动解绑后返佣按新绑定关系结算(此处不建议频繁修改，请谨慎操作)", "sort": 2, "user_type": 0, "status": 1, "create_time": "2022-04-06 08:41:52", "linked_status": 1, "linked_id": 438, "linked_value": 1}, {"config_id": 440, "config_classify_id": 42, "config_name": "DIY效果是否展示", "config_key": "mer_diy_status", "config_type": "radio", "config_rule": "0:关闭\n1:开启", "config_props": "defaultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 0, "create_time": "2022-04-19 18:44:22", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 441, "config_classify_id": 105, "config_name": "用户修改头像和昵称", "config_key": "open_update_info", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-04-25 01:41:26", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 442, "config_classify_id": 89, "config_name": "短信接口方式", "config_key": "sms_use_type", "config_type": "radio", "config_rule": "1:一号通\n2:阿里云短信", "config_props": "defaultValue='1'", "required": 0, "info": "1. 一号通：是指通过注册CRMEB一号通方式，可对接更多服务接口，方便运营；\n\n2. 阿里云：是指用户在阿里云平台自行注册并购买短信服务的方式；\n  实现步骤：购买短信服务(https://account.aliyun.com/login/login.htm)—阿里云平台申请短信模板—本系统平台后台-设置-消息管理-设置—更新模板ID.", "sort": 100, "user_type": 0, "status": 1, "create_time": "2022-05-31 10:22:42", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 443, "config_classify_id": 89, "config_name": "阿里云AccessKeyId", "config_key": "aliyun_AccessKeyId", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 99, "user_type": 0, "status": 1, "create_time": "2022-05-31 10:31:50", "linked_status": 1, "linked_id": 442, "linked_value": 2}, {"config_id": 444, "config_classify_id": 89, "config_name": "阿里云AccessKeySecret", "config_key": "aliyun_AccessKeySecret", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 98, "user_type": 0, "status": 1, "create_time": "2022-06-01 01:22:32", "linked_status": 1, "linked_id": 442, "linked_value": 2}, {"config_id": 446, "config_classify_id": 89, "config_name": "阿里云短信签名", "config_key": "aliyun_SignName", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 80, "user_type": 0, "status": 1, "create_time": "2022-06-01 01:23:50", "linked_status": 1, "linked_id": 442, "linked_value": 2}, {"config_id": 447, "config_classify_id": 78, "config_name": "展示为你推荐", "config_key": "recommend_switch", "config_type": "switches", "config_rule": "", "config_props": "defaultValue = '1'", "required": 0, "info": "开启：商城首页展示【为你推荐】商品列表；关闭：商城首页不展示【为你推荐】商品列表", "sort": 5, "user_type": 0, "status": 1, "create_time": "2022-08-18 18:00:13", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 448, "config_classify_id": 78, "config_name": "热卖排行开关", "config_key": "hot_ranking_switch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-09-12 20:32:53", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 449, "config_classify_id": 78, "config_name": "热卖排行分类等级", "config_key": "hot_ranking_lv", "config_type": "radio", "config_rule": "0:一级分类\n1:二级分类\n2:三级分类", "config_props": "defaultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-09-13 04:35:39", "linked_status": 1, "linked_id": 448, "linked_value": 1}, {"config_id": 450, "config_classify_id": 78, "config_name": "热卖排行更新时长（小时）", "config_key": "hot_ranking_time", "config_type": "number", "config_rule": "", "config_props": "defaultValue=1", "required": 0, "info": "指每隔多长时间系统自动根据当前累计销量计算一次排行榜单；比如：设置1小时，即每隔1小时计算一次排行榜单。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-09-22 08:11:16", "linked_status": 1, "linked_id": 448, "linked_value": 1}, {"config_id": 451, "config_classify_id": 78, "config_name": "商城搜索方式", "config_key": "vic_word_status", "config_type": "radio", "config_rule": "0:模糊搜索\n1:分词搜索", "config_props": "defaultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-09-28 19:10:43", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 452, "config_classify_id": 97, "config_name": "是否开启付费会员", "config_key": "svip_switch_status", "config_type": "switches", "config_rule": "0:关闭\n1:开启", "config_props": "defultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-11-09 17:28:38", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 453, "config_classify_id": 97, "config_name": "付费会员价展示", "config_key": "svip_show_price", "config_type": "radio", "config_rule": "0:全部用户可见\n1:仅付费会员可见", "config_props": "defultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-11-09 17:32:26", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 454, "config_classify_id": 97, "config_name": "付费会员", "config_key": "mer_svip_status", "config_type": "switches", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2022-11-11 22:40:24", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 455, "config_classify_id": 97, "config_name": "付费会员折扣（%）", "config_key": "svip_store_rate", "config_type": "number", "config_rule": "", "config_props": "defaultValue=100\nmax=100\nmin=0", "required": 0, "info": "默认显示100，表示无折扣；设置为80，表示该店铺所有商品会员价是8折； 设置为0时，表示会员免费购买。", "sort": 0, "user_type": 1, "status": 1, "create_time": "2022-11-11 22:44:11", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 456, "config_classify_id": 97, "config_name": "优惠券叠加", "config_key": "svip_coupon_merge", "config_type": "radio", "config_rule": "0:会员价不和商户优惠券叠加\n1:会员价和商户优惠券叠加", "config_props": "defaultValue=\"0\"", "required": 0, "info": "", "sort": 0, "user_type": 1, "status": 1, "create_time": "2022-11-11 22:48:37", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 457, "config_classify_id": 70, "config_name": "ICO图标", "config_key": "site_ico", "config_type": "file", "config_rule": "accept=ico", "config_props": "guidance_image=\"/statics/system/adminSetIcon.png\"", "required": 0, "info": "展示在浏览器窗口的小图标, 建议尺寸：32*32px, 文件格式必须为：.ico", "sort": 0, "user_type": 0, "status": 1, "create_time": "2022-11-14 19:41:24", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 458, "config_classify_id": 61, "config_name": "移动端内容展示", "config_key": "community_app_switch", "config_type": "checkbox", "config_rule": "1:图文展示\n2:短视频展示", "config_props": "defaultValue=\"1\"", "required": 0, "info": "", "sort": 7, "user_type": 0, "status": 1, "create_time": "2022-11-28 19:00:36", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 459, "config_classify_id": 61, "config_name": "短视频审核", "config_key": "community_video_audit", "config_type": "radio", "config_rule": "0:审核\n1:免审核", "config_props": "defaulValue=\"0\"", "required": 0, "info": "", "sort": 8, "user_type": 0, "status": 1, "create_time": "2022-11-28 19:03:15", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 460, "config_classify_id": 77, "config_name": "店铺保证金标识展示", "config_key": "margin_ico_switch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-02-14 23:21:29", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 461, "config_classify_id": 77, "config_name": "保证金标识", "config_key": "margin_ico", "config_type": "image", "config_rule": "", "config_props": "guidance_image=\"/statics/system/adminSetMargin.png\"", "required": 0, "info": "缴纳保证金的商户才能展示", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-02-14 23:25:21", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 462, "config_classify_id": 80, "config_name": "V3支付KEY", "config_key": "pay_weixin_v3_key", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "微信支付v3版本的支付密钥，『如果未开启V3支付，则无需填写』", "sort": 60, "user_type": 0, "status": 1, "create_time": "2023-02-17 14:53:57", "linked_status": 1, "linked_id": 532, "linked_value": 1}, {"config_id": 463, "config_classify_id": 80, "config_name": "V3证书序列号", "config_key": "pay_wechat_serial_no_v3", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "「商户API证书」的「证书序列号」，可以在证书管理里面查看，『如果未开启V3支付，则无需填写』", "sort": 60, "user_type": 0, "status": 1, "create_time": "2023-02-17 15:00:05", "linked_status": 1, "linked_id": 532, "linked_value": 1}, {"config_id": 464, "config_classify_id": 81, "config_name": "小程序V3支付KEY", "config_key": "pay_routine_v3_key", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "小程序支付v3版本的支付密钥 『 如果未开启V3支付，则无需填写 』", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-02-17 15:42:21", "linked_status": 1, "linked_id": 533, "linked_value": 1}, {"config_id": 465, "config_classify_id": 81, "config_name": "小程序V3支付证书编号", "config_key": "pay_routine_serial_no_v3", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "「商户API证书」的「证书序列号」，可以在证书管理里面查看，『如果未开启V3支付，则无需填写』", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-02-17 15:51:53", "linked_status": 1, "linked_id": 533, "linked_value": 1}, {"config_id": 466, "config_classify_id": 81, "config_name": "小程序Mchid", "config_key": "pay_routine_new_mchid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "新版小程序支付配置，小程序开发后台有支付管理选项的，请填写此处小程序支付商户号mchid，『 不是新版小程序支付，则不填写此处 』", "sort": 0, "user_type": 0, "status": 0, "create_time": "2023-02-23 23:48:36", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 467, "config_classify_id": 98, "config_name": "开屏广告", "config_key": "open_screen_switch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-10 18:15:32", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 468, "config_classify_id": 98, "config_name": "开屏广告时间", "config_key": "open_screen_time", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-10 18:21:45", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 469, "config_classify_id": 99, "config_name": "保证金补缴提醒", "config_key": "margin_remind_switch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-24 01:23:31", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 470, "config_classify_id": 99, "config_name": "补缴提醒时间（天）", "config_key": "margin_remind_day", "config_type": "number", "config_rule": "", "config_props": "defaultValue=1", "required": 0, "info": "店铺保证金补缴提醒开启，并设置天数，比如:填写30天，即自保证金不足日开始计算连续提醒商户补缴保证金30天，如果期间商户还未补足保证金，30天满后自动关闭店铺", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-24 01:24:22", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 471, "config_classify_id": 100, "config_name": "空间域名 Domain", "config_key": "jdoss_uploadUrl", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：s3.cn-north-1.jdcloud-oss.com", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 00:47:01", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 472, "config_classify_id": 100, "config_name": "accessKey", "config_key": "jdoss_accessKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：JDC_77BCCDA4D65BF80BB28888888", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 00:48:23", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 473, "config_classify_id": 100, "config_name": "secret<PERSON>ey", "config_key": "jdoss_secretKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：C554860FD4CC2A3C9AD704ECC888888", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 00:49:20", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 474, "config_classify_id": 100, "config_name": "存储空间名称", "config_key": "jdoss_storage_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 00:50:30", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 475, "config_classify_id": 100, "config_name": "所属地域", "config_key": "jdoss_storage_region", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：cn-north-1", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 00:52:59", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 476, "config_classify_id": 100, "config_name": "CDN域名", "config_key": "jdoss_cdn", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 00:53:56", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 477, "config_classify_id": 100, "config_name": "是否开启图片压缩", "config_key": "jdoss_thumb_status", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 00:54:39", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 478, "config_classify_id": 100, "config_name": "图片压缩比例", "config_key": "jdoss_thumb_rate", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "（1%～100%）", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 00:55:38", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 479, "config_classify_id": 101, "config_name": "空间域名 Domain", "config_key": "ctoss_uploadUrl", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb.obs.cn-snxy1.ctyun.cn", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 01:22:12", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 480, "config_classify_id": 101, "config_name": "accessKey", "config_key": "ctoss_accessKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：G751BUNJSPKBBCPDUFAB", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 01:22:40", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 481, "config_classify_id": 101, "config_name": "secret<PERSON>ey", "config_key": "ctoss_secretKey", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：hbku23PT1CRWcodY7VO0xgbEy5X2PWFevLpalSab", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 01:23:47", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 482, "config_classify_id": 101, "config_name": "存储空间名称", "config_key": "ctoss_storage_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：crmeb", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 01:24:53", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 483, "config_classify_id": 101, "config_name": "所属地域", "config_key": "ctoss_storage_region", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "例：obs.cn-snxy1.ctyun.cn", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 01:25:20", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 484, "config_classify_id": 101, "config_name": "CDN域名", "config_key": "ctoss_cdn", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 01:25:50", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 485, "config_classify_id": 101, "config_name": "是否开启图片压缩", "config_key": "ctoss_thumb_status", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 01:26:12", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 486, "config_classify_id": 101, "config_name": "图片压缩比例", "config_key": "ctoss_thumb_rate", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "（1%～100%）", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-04-25 01:27:16", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 487, "config_classify_id": 98, "config_name": "广告频次", "config_key": "open_screen_space", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-05-25 17:40:30", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 488, "config_classify_id": 97, "config_name": "会员专享列表", "config_key": "svip_product_switch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "开启/关闭付费会员开通页底部会员专属商品列表", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-03 23:55:17", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 489, "config_classify_id": 105, "config_name": "小程序首次登录获取头像昵称", "config_key": "first_avatar_switch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "小程序首次登录弹出获取头像昵称弹窗", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-16 19:09:25", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 490, "config_classify_id": 67, "config_name": "图片压缩限值（M）", "config_key": "local_upload_max_size", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "例：设置5M，即5M 及以上图片按设置比例压缩，需对应修改php 默认上传文件大小值；小于5M的图片不压缩。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-17 18:24:08", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 491, "config_classify_id": 45, "config_name": "图片压缩限值（M）", "config_key": "upload_max_size", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "例：设置5M，即5M 及以上图片按设置比例压缩，需对应修改php 默认上传文件大小值；小于5M的图片不压缩。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-17 18:31:06", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 492, "config_classify_id": 46, "config_name": "图片压缩限值（M）", "config_key": "qiniu_upload_max_size", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "例：设置5M，即5M 及以上图片按设置比例压缩，需对应修改php 默认上传文件大小值；小于5M的图片不压缩。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-17 18:31:33", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 493, "config_classify_id": 47, "config_name": "图片压缩限值（M）", "config_key": "tengxun_upload_max_size", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "例：设置5M，即5M 及以上图片按设置比例压缩，需对应修改php 默认上传文件大小值；小于5M的图片不压缩。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-17 18:31:57", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 494, "config_classify_id": 65, "config_name": "图片压缩限值（M）", "config_key": "obs_upload_max_size", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "例：设置5M，即5M 及以上图片按设置比例压缩，需对应修改php 默认上传文件大小值；小于5M的图片不压缩。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-17 18:33:05", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 495, "config_classify_id": 66, "config_name": "图片压缩限值（M）", "config_key": "uc_upload_max_size", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "例：设置5M，即5M 及以上图片按设置比例压缩，需对应修改php 默认上传文件大小值；小于5M的图片不压缩。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-17 18:33:34", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 496, "config_classify_id": 100, "config_name": "图片压缩限值（M）", "config_key": "jdoss_upload_max_size", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "例：设置5M，即5M 及以上图片按设置比例压缩，需对应修改php 默认上传文件大小值；小于5M的图片不压缩。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-17 18:34:50", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 497, "config_classify_id": 101, "config_name": "图片压缩限值（M）", "config_key": "ctoss_upload_max_size", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "例：设置5M，即5M 及以上图片按设置比例压缩，需对应修改php 默认上传文件大小值；小于5M的图片不压缩。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-07-17 18:35:19", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 498, "config_classify_id": 105, "config_name": "手机号快速验证组件", "config_key": "wechat_phone_switch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "开启使用微信手机号快速验证组件，关闭则不使用。", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-08-11 01:52:20", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 499, "config_classify_id": 48, "config_name": "小程序发货管理", "config_key": "order_shipping_open", "config_type": "switches", "config_rule": "0:关闭\n1:开启", "config_props": "0", "required": 0, "info": "如果小程序后台出现发货管理菜单栏，请打开此开关", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-10-20 01:25:00", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 500, "config_classify_id": 102, "config_name": "商品排行数据时间", "config_key": "sys_pay_product_rank", "config_type": "radio", "config_rule": "today:今日\nweek:本周\nmonth:本月", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-11-28 03:47:34", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 501, "config_classify_id": 102, "config_name": "商品排行数据类型", "config_key": "sys_pay_product_rank_type", "config_type": "radio", "config_rule": "0:销售数量\n1:销售金额", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-11-28 03:57:12", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 502, "config_classify_id": 102, "config_name": "商户排行数据时间", "config_key": "sys_pay_merchant_rank", "config_type": "radio", "config_rule": "today:当日\nweek:本周\nmonth:本月", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-12-12 03:25:18", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 503, "config_classify_id": 102, "config_name": "商户排行显示数据", "config_key": "sys_pay_merchant_rank_type", "config_type": "radio", "config_rule": "0:销售金额\n1:商品数量", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2023-12-12 03:26:36", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 504, "config_classify_id": 103, "config_name": "APPID", "config_key": "serve_account", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "一号通后台应用管理获得APPID", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-01-09 08:11:50", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 505, "config_classify_id": 103, "config_name": "AppSecret", "config_key": "serve_token", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "一号通后台应用管理获得AppSecret", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-01-09 08:12:18", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 506, "config_classify_id": 102, "config_name": "显示名称", "config_key": "data_screen_title", "config_type": "input", "config_rule": "", "config_props": "defaultValue=\"商城可视化数据大屏\"", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-01-10 02:26:40", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 510, "config_classify_id": 92, "config_name": "分销模式", "config_key": "promoter_type", "config_type": "radio", "config_rule": "0:礼包分销\n1:手动分销\n2:人人分销\n3:满额分销", "config_props": "defaultValue = '0'", "required": 0, "info": "“礼包分销”购买分销礼包后成为分销员，“手动分销\"仅可后台手动设置分销员，“人人分销”默认每个用户注册成功后都可以成为分销员，“满额分销\"指用户购买商品满足设置的消费金额后自动成为分销员。", "sort": 2, "user_type": 0, "status": 1, "create_time": "2024-04-08 07:55:38", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 511, "config_classify_id": 92, "config_name": "满额分销最低金额（元）", "config_key": "promoter_low_money", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "用户消费金额达到设置金额（含设置金额）即可自动开通分销权限", "sort": 1, "user_type": 0, "status": 1, "create_time": "2024-04-08 07:56:18", "linked_status": 1, "linked_id": 510, "linked_value": 3}, {"config_id": 512, "config_classify_id": 60, "config_name": "支付金额获得成长值", "config_key": "member_order_pay_num", "config_type": "number", "config_rule": "", "config_props": "min = 0\nprecision = 0", "required": 0, "info": "用户实际支付1元，可获得多少成长值", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-05-09 02:15:01", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 513, "config_classify_id": 105, "config_name": "注册有礼启用", "config_key": "newcomer_status", "config_type": "switches", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-06-13 08:37:40", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 514, "config_classify_id": 105, "config_name": "注册赠送积分", "config_key": "register_integral_status", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "0:关闭\n1:开启", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-06-13 08:39:22", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 515, "config_classify_id": 105, "config_name": "注册赠送积分", "config_key": "register_give_integral", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-06-13 08:40:02", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 516, "config_classify_id": 105, "config_name": "赠送余额", "config_key": "register_money_status", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "0:关闭\n1:开启", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-06-13 08:53:19", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 517, "config_classify_id": 105, "config_name": "赠送余额", "config_key": "register_give_money", "config_type": "number", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-06-13 08:53:39", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 518, "config_classify_id": 105, "config_name": "赠送优惠券", "config_key": "register_coupon_status", "config_type": "switches", "config_rule": "0:关闭\n1:开启", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-06-13 08:54:25", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 519, "config_classify_id": 105, "config_name": "赠送优惠券", "config_key": "register_give_coupon", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-06-13 08:55:11", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 520, "config_classify_id": 105, "config_name": "注册有礼弹窗", "config_key": "register_popup_pic", "config_type": "image", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-06-13 08:56:18", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 521, "config_classify_id": 105, "config_name": "弹窗跳转地址", "config_key": "register_popup_url", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-06-13 08:57:10", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 522, "config_classify_id": 108, "config_name": "线下支付功能", "config_key": "offline_switch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "商城线下支付功能启用或者关闭", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-07-02 04:01:03", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 523, "config_classify_id": 96, "config_name": "客服链接", "config_key": "customer_url", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "客服类型选择企业微信时，跳转的链接地址", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-07-13 07:57:21", "linked_status": 1, "linked_id": 435, "linked_value": 3}, {"config_id": 524, "config_classify_id": 96, "config_name": "企业ID", "config_key": "customer_corpId", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "小程序需要跳转企业微信客服的话需要配置此项，并且在小程序客服中绑定企业ID", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-07-13 09:09:57", "linked_status": 1, "linked_id": 435, "linked_value": 3}, {"config_id": 525, "config_classify_id": 107, "config_name": "客服电话", "config_key": "service_phone", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "商户客服电话", "sort": 0, "user_type": 1, "status": 1, "create_time": "2024-07-13 09:31:36", "linked_status": 1, "linked_id": 376, "linked_value": 2}, {"config_id": 526, "config_classify_id": 107, "config_name": "客服链接", "config_key": "mer_customer_url", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "客服类型选择企业微信时，跳转的链接地址", "sort": 0, "user_type": 1, "status": 1, "create_time": "2024-07-13 09:32:40", "linked_status": 1, "linked_id": 376, "linked_value": 3}, {"config_id": 527, "config_classify_id": 107, "config_name": "企业ID", "config_key": "mer_customer_corpId", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "如果客服链接填写企业微信客服，小程序需要跳转企业微信客服的话需要配置此项，并且在小程序客服中绑定企业ID", "sort": 0, "user_type": 1, "status": 1, "create_time": "2024-07-13 09:33:21", "linked_status": 1, "linked_id": 376, "linked_value": 3}, {"config_id": 528, "config_classify_id": 96, "config_name": "跳转链接", "config_key": "customer_link", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "客服类型选择跳转链接时，跳转的链接地址", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-08-27 09:44:15", "linked_status": 1, "linked_id": 435, "linked_value": 4}, {"config_id": 529, "config_classify_id": 107, "config_name": "跳转链接", "config_key": "mer_customer_link", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "客服类型选择跳转链接时，跳转的链接地址", "sort": 0, "user_type": 1, "status": 1, "create_time": "2024-08-27 09:47:16", "linked_status": 1, "linked_id": 376, "linked_value": 4}, {"config_id": 530, "config_classify_id": 36, "config_name": "关注是否生成用户", "config_key": "create_wechat_user", "config_type": "radio", "config_rule": "0:否\n1:是", "config_props": "", "required": 0, "info": "用户关注公众号之后是否生成商城用户", "sort": 0, "user_type": 0, "status": 0, "create_time": "2024-09-18 10:34:06", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 531, "config_classify_id": 94, "config_name": "微信提现方式", "config_key": "extract_switch", "config_type": "radio", "config_rule": "1:线下转账\n2:自动转账", "config_props": "defaultValue = '1'", "required": 0, "info": "", "sort": 100, "user_type": 0, "status": 0, "create_time": "2024-09-18 11:00:04", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 532, "config_classify_id": 80, "config_name": "支付接口类型", "config_key": "pay_wechat_type", "config_type": "radio", "config_rule": "0:v2\n1:v3", "config_props": "defaultValue = '0'", "required": 0, "info": "支付接口类型v2对应微信支付旧版v2支付。v3对应微信支付v3支付接口。支付证书可以通用一个。支付秘钥和v2旧版支付有区别。付款码支付请配置v2", "sort": 70, "user_type": 0, "status": 1, "create_time": "2024-09-19 11:30:00", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 533, "config_classify_id": 81, "config_name": "支付接口类型", "config_key": "pay_routine_type", "config_type": "radio", "config_rule": "0:v2\n1:v3", "config_props": "defaultValue= '0'", "required": 0, "info": "支付接口类型v2对应微信支付旧版v2支付。v3对应微信支付v3支付接口。支付证书可以通用一个。支付秘钥和v2旧版支付有区别。付款码支付请配置v2", "sort": 70, "user_type": 0, "status": 1, "create_time": "2024-09-19 11:38:43", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 534, "config_classify_id": 92, "config_name": "佣金悬浮窗", "config_key": "extension_pop", "config_type": "radio", "config_rule": "0:全部可见\n1:推广员可见\n2:非推广员可见\n3:关闭", "config_props": "defaultValue=\"0\"", "required": 0, "info": "商品详情页最高佣金的悬浮框控制开关", "sort": 3, "user_type": 0, "status": 1, "create_time": "2024-09-23 18:16:34", "linked_status": 0, "linked_id": 196, "linked_value": 1}, {"config_id": 535, "config_classify_id": 111, "config_name": "电子面单开启", "config_key": "mer_dump_switch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "电子面单打印功能是否开启", "sort": 110, "user_type": 1, "status": 1, "create_time": "2024-09-25 11:02:54", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 536, "config_classify_id": 111, "config_name": "打印机选择", "config_key": "mer_dump_type", "config_type": "radio", "config_rule": "0:通用打印机(仅支持单张打印)\n1:快递100打印机(支持批量打印)", "config_props": "defaultValue = \"0\"", "required": 0, "info": "通用打印机不限型号，在电脑浏览器界面右上角选择打印机并设置即可; 快送100电子面单打印机型号:快送100云打印机二代3寸 电脑Wi-Fi两用", "sort": 100, "user_type": 1, "status": 1, "create_time": "2024-09-25 11:05:17", "linked_status": 1, "linked_id": 535, "linked_value": 1}, {"config_id": 537, "config_classify_id": 111, "config_name": "寄件人姓名", "config_key": "mer_from_name", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "电子面单默认寄件人姓名", "sort": 0, "user_type": 1, "status": 1, "create_time": "2024-09-25 11:10:55", "linked_status": 1, "linked_id": 535, "linked_value": 1}, {"config_id": 538, "config_classify_id": 111, "config_name": "寄件人电话", "config_key": "mer_from_tel", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "电子面单默认寄件人电话", "sort": 0, "user_type": 1, "status": 1, "create_time": "2024-09-25 11:11:58", "linked_status": 1, "linked_id": 535, "linked_value": 1}, {"config_id": 539, "config_classify_id": 111, "config_name": "云打印机编号", "config_key": "mer_config_siid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "快送100电子面单打印机编号,在打印机背面查看", "sort": 80, "user_type": 1, "status": 1, "create_time": "2024-09-25 11:12:56", "linked_status": 1, "linked_id": 536, "linked_value": 1}, {"config_id": 540, "config_classify_id": 111, "config_name": "商品信息打印", "config_key": "mer_is_cargo", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "开启时快递面单上会打印商品的信息,关闭则不打印商品信息", "sort": 101, "user_type": 1, "status": 0, "create_time": "2024-10-10 17:15:14", "linked_status": 1, "linked_id": 535, "linked_value": 1}, {"config_id": 541, "config_classify_id": 80, "config_name": "微信支付公钥", "config_key": "pay_weixin_public_key", "config_type": "file", "config_rule": "", "config_props": "", "required": 0, "info": "微信新版本支付公钥", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-12-25 12:09:23", "linked_status": 1, "linked_id": 532, "linked_value": 1}, {"config_id": 542, "config_classify_id": 81, "config_name": "微信支付公钥", "config_key": "pay_routine_public_key", "config_type": "file", "config_rule": "", "config_props": "", "required": 0, "info": "微信支付公钥", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-12-25 12:21:40", "linked_status": 1, "linked_id": 533, "linked_value": 1}, {"config_id": 543, "config_classify_id": 80, "config_name": "微信支付公钥ID", "config_key": "pay_weixin_public_id", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "微信支付公钥ID", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-12-26 09:41:34", "linked_status": 1, "linked_id": 532, "linked_value": 1}, {"config_id": 544, "config_classify_id": 81, "config_name": "微信支付公钥ID", "config_key": "pay_routine_public_id", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "微信支付公钥ID", "sort": 0, "user_type": 0, "status": 1, "create_time": "2024-12-26 09:43:11", "linked_status": 1, "linked_id": 533, "linked_value": 1}, {"config_id": 545, "config_classify_id": 94, "config_name": "转账场景ID", "config_key": "transfer_scene_id", "config_type": "number", "config_rule": "", "config_props": "", "required": 1, "info": "该笔转账使用的转账场景，可前往“商户平台-产品中心-商家转账”中申请。如：1001-现金营销", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-02-26 09:57:51", "linked_status": 1, "linked_id": 334, "linked_value": 2}, {"config_id": 559, "config_classify_id": 113, "config_name": "支付宝小程序AppID", "config_key": "alipay_routine_appId", "config_type": "input", "config_rule": null, "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-30 16:35:53", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 560, "config_classify_id": 113, "config_name": "支付宝小程序私钥", "config_key": "alipay_routine_privateKey", "config_type": "textarea", "config_rule": null, "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-30 16:35:53", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 561, "config_classify_id": 113, "config_name": "支付宝公钥证书", "config_key": "alipay_routine_cert", "config_type": "textarea", "config_rule": null, "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-30 16:35:53", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 562, "config_classify_id": 113, "config_name": "支付宝小程序名称", "config_key": "alipay_routine_name", "config_type": "input", "config_rule": null, "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-30 16:35:53", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 567, "config_classify_id": 113, "config_name": "支付宝小程序logo", "config_key": "alipay_routine_logo", "config_type": "image", "config_rule": null, "config_props": "", "required": 0, "info": "", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-30 16:42:29", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 572, "config_classify_id": 115, "config_name": "直付通服务商模式", "config_key": "alipay_service_mode", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "是否启用直付通服务商模式", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-31 12:14:59", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 573, "config_classify_id": 114, "config_name": "服务商PID", "config_key": "alipay_service_pid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "服务商PID", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 10:54:12", "linked_status": 1, "linked_id": 572, "linked_value": 1}, {"config_id": 574, "config_classify_id": 114, "config_name": "APPID", "config_key": "alipay_appid", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "支付宝APPID", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-31 12:16:32", "linked_status": 0, "linked_id": 577, "linked_value": 1}, {"config_id": 575, "config_classify_id": 114, "config_name": "应用私钥", "config_key": "alipay_private_key", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "应用私钥", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-31 12:17:22", "linked_status": 1, "linked_id": 577, "linked_value": 0}, {"config_id": 576, "config_classify_id": 114, "config_name": "应用公钥", "config_key": "alipay_public_key", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "应用公钥", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-31 12:18:37", "linked_status": 1, "linked_id": 577, "linked_value": 0}, {"config_id": 577, "config_classify_id": 114, "config_name": "证书模式", "config_key": "alipay_cert_mode", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "是否启用证书模式", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-31 12:19:58", "linked_status": 0, "linked_id": 572, "linked_value": 1}, {"config_id": 578, "config_classify_id": 114, "config_name": "应用公钥证书", "config_key": "alipay_app_public_cert_path", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "应用公钥证书内容", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-31 12:20:44", "linked_status": 1, "linked_id": 577, "linked_value": 1}, {"config_id": 579, "config_classify_id": 114, "config_name": "支付宝公钥证书", "config_key": "alipay_public_cert_path", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "支付宝公钥证书内容", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-31 12:21:48", "linked_status": 1, "linked_id": 577, "linked_value": 1}, {"config_id": 580, "config_classify_id": 114, "config_name": "支付宝根证书", "config_key": "alipay_root_cert_path", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "支付宝根证书内容", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-31 12:22:38", "linked_status": 1, "linked_id": 577, "linked_value": 1}, {"config_id": 581, "config_classify_id": 83, "config_name": "电商收付通", "config_key": "open_wx_sub_mch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "是否开启电商收付通", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-05-31 20:40:33", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 583, "config_classify_id": 115, "config_name": "直付通进件", "config_key": "open_alipay_sub_mch", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "是否开启进件", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 11:08:32", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 584, "config_classify_id": 115, "config_name": "直付通APPID", "config_key": "alipay_service_app_id", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "直付通服务商APPID", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 22:31:20", "linked_status": 0, "linked_id": 572, "linked_value": 1}, {"config_id": 585, "config_classify_id": 115, "config_name": "直付通私钥", "config_key": "alipay_service_private_key", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "直付通服务商应用私钥", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 22:31:20", "linked_status": 0, "linked_id": 572, "linked_value": 1}, {"config_id": 586, "config_classify_id": 115, "config_name": "直付通公钥", "config_key": "alipay_service_public_key", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "直付通服务商应用公钥", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 22:31:20", "linked_status": 1, "linked_id": 587, "linked_value": 0}, {"config_id": 587, "config_classify_id": 115, "config_name": "直付通证书模式", "config_key": "alipay_service_cert_mode", "config_type": "switches", "config_rule": "", "config_props": "", "required": 0, "info": "是否启用证书模式", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 22:31:20", "linked_status": 0, "linked_id": 572, "linked_value": 1}, {"config_id": 588, "config_classify_id": 115, "config_name": "直付通应用公钥证书", "config_key": "alipay_service_app_cert", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "直付通应用公钥证书内容", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 22:31:20", "linked_status": 1, "linked_id": 587, "linked_value": 1}, {"config_id": 589, "config_classify_id": 115, "config_name": "直付通支付宝公钥证书", "config_key": "alipay_service_public_cert", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "直付通支付宝公钥证书内容", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 22:31:20", "linked_status": 1, "linked_id": 587, "linked_value": 1}, {"config_id": 590, "config_classify_id": 115, "config_name": "直付通支付宝根证书", "config_key": "alipay_service_root_cert", "config_type": "textarea", "config_rule": "", "config_props": "", "required": 0, "info": "直付通支付宝根证书内容", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 22:31:20", "linked_status": 1, "linked_id": 587, "linked_value": 1}, {"config_id": 591, "config_classify_id": 114, "config_name": "多商户支付模式", "config_key": "alipay_multi_merchant_mode", "config_type": "select", "config_rule": "platform:平台收款\ndirect:直付通\nsingle:单商户", "config_props": "", "required": 0, "info": "多商户支付模式(platform:平台收款,direct:直付通,single:单商户)", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-02 22:31:20", "linked_status": 0, "linked_id": 0, "linked_value": 0}, {"config_id": 592, "config_classify_id": 114, "config_name": "授权回调地址", "config_key": "alipay_auth_redirect_uri", "config_type": "input", "config_rule": "", "config_props": "", "required": 0, "info": "支付宝应用授权回调地址", "sort": 0, "user_type": 0, "status": 1, "create_time": "2025-06-03 13:45:08", "linked_status": 0, "linked_id": 0, "linked_value": 0}]