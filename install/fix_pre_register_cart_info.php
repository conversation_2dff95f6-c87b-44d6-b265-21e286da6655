<?php

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * 修复预注册订单的cart_info结构
 * 使其与前端显示逻辑兼容
 */

require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;
use think\facade\Log;

try {
    echo "开始修复预注册订单的cart_info结构...\n";
    
    // 查找所有预注册订单的商品
    $preRegisterProducts = Db::name('store_order_product')
        ->alias('op')
        ->join('store_order o', 'op.order_id = o.order_id')
        ->where('o.admin_mark', 'like', '%预注册订单%')
        ->whereOr('o.order_extend', 'like', '%pre_register%')
        ->field('op.order_product_id,op.cart_info,op.product_id')
        ->select()
        ->toArray();
    
    if (empty($preRegisterProducts)) {
        echo "未找到需要修复的预注册订单商品\n";
        exit;
    }
    
    echo "找到 " . count($preRegisterProducts) . " 个预注册订单商品需要修复\n";
    
    $fixedCount = 0;
    $errorCount = 0;
    
    foreach ($preRegisterProducts as $product) {
        try {
            $cartInfo = json_decode($product['cart_info'], true);
            
            // 检查是否已经是正确的结构
            if (isset($cartInfo['product']) && isset($cartInfo['productAttr'])) {
                echo "订单商品 {$product['order_product_id']} 已经是正确结构，跳过\n";
                continue;
            }
            
            // 获取商品信息
            $productInfo = Db::name('store_product')
                ->where('product_id', $product['product_id'])
                ->field('product_id,store_name,image,price')
                ->find();
            
            if (!$productInfo) {
                echo "订单商品 {$product['order_product_id']} 对应的商品不存在，跳过\n";
                continue;
            }
            
            // 构建新的cart_info结构
            $newCartInfo = [
                'product_id' => $product['product_id'],
                'product' => [
                    'product_id' => $product['product_id'],
                    'store_name' => $cartInfo['store_name'] ?? $productInfo['store_name'],
                    'image' => $cartInfo['image'] ?? $productInfo['image'],
                    'price' => $cartInfo['price'] ?? $productInfo['price']
                ],
                'productAttr' => [
                    'sku' => '预注册商品',
                    'price' => $cartInfo['price'] ?? $productInfo['price'],
                    'image' => $cartInfo['image'] ?? $productInfo['image']
                ],
                'cart_num' => $cartInfo['quantity'] ?? $cartInfo['cart_num'] ?? 1,
                'quantity' => $cartInfo['quantity'] ?? $cartInfo['cart_num'] ?? 1,
                'order_type' => 'pre_register'
            ];
            
            // 更新数据库
            $result = Db::name('store_order_product')
                ->where('order_product_id', $product['order_product_id'])
                ->update(['cart_info' => json_encode($newCartInfo, JSON_UNESCAPED_UNICODE)]);
            
            if ($result) {
                $fixedCount++;
                echo "修复订单商品 {$product['order_product_id']} 成功\n";
            } else {
                $errorCount++;
                echo "修复订单商品 {$product['order_product_id']} 失败\n";
            }
            
        } catch (\Exception $e) {
            $errorCount++;
            echo "修复订单商品 {$product['order_product_id']} 时发生错误: " . $e->getMessage() . "\n";
            Log::error('修复预注册订单cart_info失败', [
                'order_product_id' => $product['order_product_id'],
                'error' => $e->getMessage()
            ]);
        }
    }
    
    echo "\n修复完成！\n";
    echo "成功修复: {$fixedCount} 个\n";
    echo "修复失败: {$errorCount} 个\n";
    
    if ($fixedCount > 0) {
        echo "\n建议清除相关缓存以确保修改生效\n";
    }
    
} catch (\Exception $e) {
    echo "修复过程中发生错误: " . $e->getMessage() . "\n";
    Log::error('修复预注册订单cart_info脚本执行失败', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
