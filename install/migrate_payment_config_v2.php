<?php

/**
 * 支付配置分层架构迁移脚本
 * 将现有配置迁移到新的分层架构
 */

require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;
use think\facade\Log;

class PaymentConfigMigration
{
    /**
     * 系统配置键名映射（旧 => 新）
     */
    private static $systemConfigMapping = [
        // 微信配置统一
        'wechat_appid' => 'wechat_appid',
        'wechat_mchid' => 'wechat_mch_id',
        'wechat_key' => 'wechat_key',
        'wechat_apiv3_key' => 'wechat_api_v3_key',
        
        // 微信服务商配置
        'wechat_service_appid' => 'wechat_service_appid',
        'wechat_service_merid' => 'wechat_service_mch_id',
        'wechat_service_key' => 'wechat_service_key',
        'wechat_service_v3key' => 'wechat_service_v3_key',
        
        // 支付宝配置统一
        'alipay_app_id' => 'alipay_app_id',
        'alipay_private_key' => 'alipay_private_key',
        'alipay_public_key' => 'alipay_public_key',
        
        // 支付宝服务商配置
        'alipay_service_app_id' => 'alipay_service_app_id',
        'alipay_service_private_key' => 'alipay_service_private_key',
        'alipay_service_public_key' => 'alipay_service_public_key',
        
        // 小程序配置统一
        'routine_appId' => 'routine_app_id',
        'pay_routine_mchid' => 'routine_mch_id',
        'pay_routine_key' => 'routine_key'
    ];
    
    /**
     * 商户配置字段映射（旧 => 新）
     */
    private static $merchantFieldMapping = [
        // 微信字段映射
        'wechat_app_id' => 'appid',
        'wechat_mch_id' => 'mch_id',
        'wechat_key' => 'key',
        'wechat_private_key' => 'private_key',
        'wechat_public_key' => 'public_key',
        'wechat_api_v3_key' => 'api_v3_key',
        'wechat_serial_no' => 'serial_no',
        
        // 支付宝字段映射
        'alipay_app_id' => 'app_id',
        'alipay_private_key' => 'private_key',
        'alipay_public_key' => 'public_key',
        'alipay_cert_mode' => 'cert_mode',
        'alipay_service_mode' => 'service_mode',
        
        // 通用字段
        'is_service_mode' => 'service_mode'
    ];
    
    /**
     * 执行迁移
     */
    public static function migrate()
    {
        echo "开始支付配置分层架构迁移...\n";
        
        try {
            // 1. 备份现有数据
            self::backupData();
            
            // 2. 迁移系统配置
            self::migrateSystemConfig();
            
            // 3. 迁移商户配置
            self::migrateMerchantConfig();
            
            // 4. 更新数据库结构
            self::updateDatabaseSchema();
            
            // 5. 验证迁移结果
            self::validateMigration();
            
            echo "迁移完成！\n";
            
        } catch (\Exception $e) {
            echo "迁移失败: " . $e->getMessage() . "\n";
            echo "请检查备份数据并手动回滚\n";
            throw $e;
        }
    }
    
    /**
     * 备份现有数据
     */
    private static function backupData()
    {
        echo "备份现有数据...\n";
        
        $backupTime = date('Y-m-d_H-i-s');
        
        // 备份系统配置
        $systemConfigs = Db::name('system_config')->select();
        file_put_contents(
            __DIR__ . "/backup_system_config_{$backupTime}.json",
            json_encode($systemConfigs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
        
        // 备份商户支付配置
        $merchantConfigs = Db::name('merchant_payment_config')->select();
        file_put_contents(
            __DIR__ . "/backup_merchant_payment_config_{$backupTime}.json",
            json_encode($merchantConfigs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
        
        // 备份商户支付参数
        $merchantParams = Db::name('merchant_payment_params')->select();
        file_put_contents(
            __DIR__ . "/backup_merchant_payment_params_{$backupTime}.json",
            json_encode($merchantParams, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
        
        echo "数据备份完成\n";
    }
    
    /**
     * 迁移系统配置
     */
    private static function migrateSystemConfig()
    {
        echo "迁移系统配置...\n";
        
        foreach (self::$systemConfigMapping as $oldKey => $newKey) {
            if ($oldKey !== $newKey) {
                $count = Db::name('system_config')
                    ->where('menu_name', $oldKey)
                    ->update(['menu_name' => $newKey]);
                
                if ($count > 0) {
                    echo "  更新系统配置: {$oldKey} => {$newKey}\n";
                }
            }
        }
        
        echo "系统配置迁移完成\n";
    }
    
    /**
     * 迁移商户配置
     */
    private static function migrateMerchantConfig()
    {
        echo "迁移商户配置...\n";
        
        // 获取所有商户支付参数
        $params = Db::name('merchant_payment_params')->select();
        
        $updateCount = 0;
        foreach ($params as $param) {
            $oldParamName = $param['param_name'];
            $newParamName = self::$merchantFieldMapping[$oldParamName] ?? $oldParamName;
            
            if ($oldParamName !== $newParamName) {
                Db::name('merchant_payment_params')
                    ->where('id', $param['id'])
                    ->update(['param_name' => $newParamName]);
                
                $updateCount++;
            }
        }
        
        echo "  更新商户配置参数: {$updateCount} 条\n";
        echo "商户配置迁移完成\n";
    }
    
    /**
     * 更新数据库结构
     */
    private static function updateDatabaseSchema()
    {
        echo "更新数据库结构...\n";
        
        // 为商户支付参数表添加新字段
        $sql = "
        ALTER TABLE `eb_merchant_payment_params` 
        ADD COLUMN `config_layer` varchar(32) DEFAULT 'base' COMMENT '配置层级：base/service/merchant' AFTER `param_value`,
        ADD COLUMN `field_type` varchar(20) DEFAULT 'string' COMMENT '字段类型' AFTER `config_layer`,
        ADD COLUMN `param_source` varchar(32) DEFAULT 'manual' COMMENT '参数来源：manual/system/auth' AFTER `field_type`;
        ";
        
        try {
            Db::execute($sql);
            echo "  数据库结构更新完成\n";
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "  数据库结构已存在，跳过更新\n";
            } else {
                throw $e;
            }
        }
        
        // 为商户支付配置表添加支付模式字段
        $sql2 = "
        ALTER TABLE `eb_merchant_payment_config` 
        ADD COLUMN `payment_mode` varchar(32) DEFAULT 'direct' COMMENT '支付模式：direct/service/ecommerce/zhifu' AFTER `payment_type`;
        ";
        
        try {
            Db::execute($sql2);
            echo "  支付模式字段添加完成\n";
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "  支付模式字段已存在，跳过添加\n";
            } else {
                throw $e;
            }
        }
    }
    
    /**
     * 验证迁移结果
     */
    private static function validateMigration()
    {
        echo "验证迁移结果...\n";
        
        // 验证系统配置
        $systemConfigCount = 0;
        foreach (self::$systemConfigMapping as $oldKey => $newKey) {
            if ($oldKey !== $newKey) {
                $count = Db::name('system_config')->where('menu_name', $newKey)->count();
                if ($count > 0) {
                    $systemConfigCount++;
                }
            }
        }
        echo "  系统配置验证: {$systemConfigCount} 项配置已更新\n";
        
        // 验证商户配置
        $merchantConfigCount = 0;
        foreach (self::$merchantFieldMapping as $oldField => $newField) {
            if ($oldField !== $newField) {
                $count = Db::name('merchant_payment_params')->where('param_name', $newField)->count();
                if ($count > 0) {
                    $merchantConfigCount++;
                }
            }
        }
        echo "  商户配置验证: {$merchantConfigCount} 类字段已更新\n";
        
        // 验证数据库结构
        $columns = Db::query("SHOW COLUMNS FROM eb_merchant_payment_params LIKE 'config_layer'");
        if (!empty($columns)) {
            echo "  数据库结构验证: 新字段已添加\n";
        } else {
            throw new \Exception("数据库结构更新失败");
        }
        
        echo "迁移验证完成\n";
    }
    
    /**
     * 回滚迁移
     */
    public static function rollback($backupTime)
    {
        echo "开始回滚迁移...\n";
        
        try {
            // 恢复系统配置
            $systemConfigFile = __DIR__ . "/backup_system_config_{$backupTime}.json";
            if (file_exists($systemConfigFile)) {
                $systemConfigs = json_decode(file_get_contents($systemConfigFile), true);
                
                Db::name('system_config')->delete(true); // 清空表
                Db::name('system_config')->insertAll($systemConfigs);
                
                echo "系统配置已恢复\n";
            }
            
            // 恢复商户配置
            $merchantConfigFile = __DIR__ . "/backup_merchant_payment_config_{$backupTime}.json";
            if (file_exists($merchantConfigFile)) {
                $merchantConfigs = json_decode(file_get_contents($merchantConfigFile), true);
                
                Db::name('merchant_payment_config')->delete(true);
                Db::name('merchant_payment_config')->insertAll($merchantConfigs);
                
                echo "商户配置已恢复\n";
            }
            
            // 恢复商户参数
            $merchantParamsFile = __DIR__ . "/backup_merchant_payment_params_{$backupTime}.json";
            if (file_exists($merchantParamsFile)) {
                $merchantParams = json_decode(file_get_contents($merchantParamsFile), true);
                
                Db::name('merchant_payment_params')->delete(true);
                Db::name('merchant_payment_params')->insertAll($merchantParams);
                
                echo "商户参数已恢复\n";
            }
            
            echo "回滚完成\n";
            
        } catch (\Exception $e) {
            echo "回滚失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
}

// 执行迁移
if (php_sapi_name() === 'cli') {
    $action = $argv[1] ?? 'migrate';
    
    if ($action === 'migrate') {
        PaymentConfigMigration::migrate();
    } elseif ($action === 'rollback') {
        $backupTime = $argv[2] ?? '';
        if (empty($backupTime)) {
            echo "请指定备份时间，格式：Y-m-d_H-i-s\n";
            exit(1);
        }
        PaymentConfigMigration::rollback($backupTime);
    } else {
        echo "用法: php migrate_payment_config_v2.php [migrate|rollback] [backup_time]\n";
        exit(1);
    }
}
