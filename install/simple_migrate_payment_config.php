<?php

/**
 * 简化的支付配置迁移脚本
 * 不依赖ThinkPHP框架，直接使用PDO
 */

// 数据库配置
$dbConfig = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'crmeb',
    'username' => 'shop',
    'password' => '3esH.PlbxsY-j@3*',
    'charset' => 'utf8mb4'
];

class SimplePaymentConfigMigration
{
    private static $pdo;
    
    /**
     * 获取数据库连接
     */
    private static function getPdo($config)
    {
        if (!self::$pdo) {
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
            self::$pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
        }
        return self::$pdo;
    }
    
    /**
     * 执行迁移
     */
    public static function migrate($dbConfig)
    {
        echo "开始支付配置分层架构迁移...\n";
        
        try {
            $pdo = self::getPdo($dbConfig);
            
            // 1. 备份数据
            self::backupData($pdo);
            
            // 2. 更新数据库结构
            self::updateDatabaseSchema($pdo);
            
            // 3. 迁移系统配置
            self::migrateSystemConfig($pdo);
            
            // 4. 迁移商户配置
            self::migrateMerchantConfig($pdo);
            
            echo "迁移完成！\n";
            
        } catch (Exception $e) {
            echo "迁移失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * 备份数据
     */
    private static function backupData($pdo)
    {
        echo "备份现有数据...\n";
        
        $backupTime = date('Y-m-d_H-i-s');
        
        // 备份系统配置
        $stmt = $pdo->query("SELECT * FROM eb_system_config");
        $systemConfigs = $stmt->fetchAll();
        file_put_contents(
            __DIR__ . "/backup_system_config_{$backupTime}.json",
            json_encode($systemConfigs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
        
        // 备份商户支付配置
        $stmt = $pdo->query("SELECT * FROM eb_merchant_payment_config");
        $merchantConfigs = $stmt->fetchAll();
        file_put_contents(
            __DIR__ . "/backup_merchant_payment_config_{$backupTime}.json",
            json_encode($merchantConfigs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
        
        // 备份商户支付参数
        $stmt = $pdo->query("SELECT * FROM eb_merchant_payment_params");
        $merchantParams = $stmt->fetchAll();
        file_put_contents(
            __DIR__ . "/backup_merchant_payment_params_{$backupTime}.json",
            json_encode($merchantParams, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
        
        echo "数据备份完成，备份时间: {$backupTime}\n";
    }
    
    /**
     * 更新数据库结构
     */
    private static function updateDatabaseSchema($pdo)
    {
        echo "更新数据库结构...\n";

        // 检查并添加字段到商户支付参数表
        $fieldsToAdd = [
            'config_layer' => "varchar(32) DEFAULT 'base' COMMENT '配置层级：base/service/merchant'",
            'field_type' => "varchar(20) DEFAULT 'string' COMMENT '字段类型'",
            'param_source' => "varchar(32) DEFAULT 'manual' COMMENT '参数来源：manual/system/auth'"
        ];

        foreach ($fieldsToAdd as $fieldName => $fieldDef) {
            try {
                // 检查字段是否存在
                $stmt = $pdo->query("SHOW COLUMNS FROM eb_merchant_payment_params LIKE '{$fieldName}'");
                $exists = $stmt->fetch();

                if (!$exists) {
                    $sql = "ALTER TABLE `eb_merchant_payment_params` ADD COLUMN `{$fieldName}` {$fieldDef}";
                    $pdo->exec($sql);
                    echo "  添加字段: {$fieldName}\n";
                } else {
                    echo "  字段已存在: {$fieldName}\n";
                }
            } catch (PDOException $e) {
                echo "  添加字段 {$fieldName} 失败: " . $e->getMessage() . "\n";
            }
        }

        // 检查并添加支付模式字段到商户支付配置表
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM eb_merchant_payment_config LIKE 'payment_mode'");
            $exists = $stmt->fetch();

            if (!$exists) {
                $sql = "ALTER TABLE `eb_merchant_payment_config` ADD COLUMN `payment_mode` varchar(32) DEFAULT 'direct' COMMENT '支付模式：direct/service/ecommerce/zhifu'";
                $pdo->exec($sql);
                echo "  添加支付模式字段\n";
            } else {
                echo "  支付模式字段已存在\n";
            }
        } catch (PDOException $e) {
            echo "  添加支付模式字段失败: " . $e->getMessage() . "\n";
        }

        echo "数据库结构更新完成\n";
    }
    
    /**
     * 迁移系统配置
     */
    private static function migrateSystemConfig($pdo)
    {
        echo "迁移系统配置...\n";
        
        // 系统配置键名映射（旧 => 新）
        $systemConfigMapping = [
            'wechat_mchid' => 'wechat_mch_id',
            'wechat_apiv3_key' => 'wechat_api_v3_key',
            'wechat_service_merid' => 'wechat_service_mch_id',
            'wechat_service_v3key' => 'wechat_service_v3_key',
            'routine_appId' => 'routine_app_id',
            'pay_routine_mchid' => 'routine_mch_id',
            'pay_routine_key' => 'routine_key'
        ];
        
        $updateCount = 0;
        foreach ($systemConfigMapping as $oldKey => $newKey) {
            // 更新系统配置表
            $stmt = $pdo->prepare("UPDATE eb_system_config SET config_key = ? WHERE config_key = ?");
            $result = $stmt->execute([$newKey, $oldKey]);

            if ($stmt->rowCount() > 0) {
                echo "  更新系统配置表: {$oldKey} => {$newKey}\n";
                $updateCount++;
            }

            // 更新系统配置值表
            $stmt = $pdo->prepare("UPDATE eb_system_config_value SET config_key = ? WHERE config_key = ?");
            $result = $stmt->execute([$newKey, $oldKey]);

            if ($stmt->rowCount() > 0) {
                echo "  更新系统配置值表: {$oldKey} => {$newKey}\n";
                $updateCount++;
            }
        }
        
        echo "系统配置迁移完成，更新了 {$updateCount} 项配置\n";
    }
    
    /**
     * 迁移商户配置
     */
    private static function migrateMerchantConfig($pdo)
    {
        echo "迁移商户配置...\n";
        
        // 商户配置字段映射（旧 => 新）
        $merchantFieldMapping = [
            'wechat_app_id' => 'appid',
            'wechat_mch_id' => 'mch_id',
            'wechat_key' => 'key',
            'wechat_private_key' => 'private_key',
            'wechat_public_key' => 'public_key',
            'wechat_api_v3_key' => 'api_v3_key',
            'wechat_serial_no' => 'serial_no',
            'alipay_app_id' => 'app_id',
            'alipay_private_key' => 'private_key',
            'alipay_public_key' => 'public_key',
            'alipay_cert_mode' => 'cert_mode',
            'alipay_service_mode' => 'service_mode',
            'is_service_mode' => 'service_mode'
        ];
        
        $updateCount = 0;
        foreach ($merchantFieldMapping as $oldField => $newField) {
            $stmt = $pdo->prepare("UPDATE eb_merchant_payment_params SET param_name = ? WHERE param_name = ?");
            $result = $stmt->execute([$newField, $oldField]);
            
            if ($stmt->rowCount() > 0) {
                $updateCount += $stmt->rowCount();
            }
        }
        
        echo "商户配置迁移完成，更新了 {$updateCount} 条参数记录\n";
    }
    
    /**
     * 验证迁移结果
     */
    public static function validateMigration($dbConfig)
    {
        echo "验证迁移结果...\n";
        
        try {
            $pdo = self::getPdo($dbConfig);
            
            // 验证数据库结构
            $stmt = $pdo->query("SHOW COLUMNS FROM eb_merchant_payment_params LIKE 'config_layer'");
            $columns = $stmt->fetchAll();
            if (!empty($columns)) {
                echo "  数据库结构验证: 新字段已添加\n";
            } else {
                throw new Exception("数据库结构更新失败");
            }
            
            // 验证系统配置
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM eb_system_config WHERE config_key = 'wechat_mch_id'");
            $result = $stmt->fetch();
            if ($result['count'] > 0) {
                echo "  系统配置验证: 配置键名已更新\n";
            }
            
            // 验证商户配置
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM eb_merchant_payment_params WHERE param_name = 'appid'");
            $result = $stmt->fetch();
            if ($result['count'] > 0) {
                echo "  商户配置验证: 字段名已更新\n";
            }
            
            echo "迁移验证完成\n";
            
        } catch (Exception $e) {
            echo "验证失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
}

// 执行迁移
if (php_sapi_name() === 'cli') {
    $action = $argv[1] ?? 'migrate';
    
    if ($action === 'migrate') {
        SimplePaymentConfigMigration::migrate($dbConfig);
        SimplePaymentConfigMigration::validateMigration($dbConfig);
    } else {
        echo "用法: php simple_migrate_payment_config.php migrate\n";
        exit(1);
    }
}
