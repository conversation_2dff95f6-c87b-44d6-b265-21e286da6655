<?php
use think\facade\Route;
use app\common\middleware\AdminAuthMiddleware;
use app\common\middleware\AdminTokenMiddleware;
use app\common\middleware\AllowOriginMiddleware;
use app\common\middleware\LogMiddleware;

Route::group(function () {
    // API支付管理
    Route::group('payment/api', function() {
        Route::get('list', '/getList')->name('PaymentApiList')->option([
            '_alias' => 'API支付列表'
        ]);
        Route::get('statistics', '/getStatistics')->name('PaymentApiStatistics')->option([
            '_alias' => 'API支付统计'
        ]);
        Route::get('detail/:id', '/getDetail')->name('PaymentApiDetail')->option([
            '_alias' => 'API支付详情'
        ]);
        Route::get('export', '/exportData')->name('PaymentApiExport')->option([
            '_alias' => '导出支付记录'
        ]);
        Route::get('sync', '/syncOrderStatus')->name('PaymentApiSync')->option([
            '_alias' => '同步订单状态'
        ]);
        Route::get('merchants', '/getMerchantList')->name('PaymentApiMerchants')->option([
            '_alias' => '获取商户列表'
        ]);
        Route::post('notify', '/sendNotification')->name('PaymentApiNotify')->option([
            '_alias' => '发送通知'
        ]);
        Route::delete('order/:id', '/deleteOrder')->name('PaymentApiDeleteOrder')->option([
            '_alias' => '删除订单'
        ]);
        Route::post('refund', '/refundOrder')->name('PaymentApiRefund')->option([
            '_alias' => '订单退款'
        ]);
    })->prefix('admin.payment.ApiPaymentController')->option([
        '_path' => '/payment/api',
        '_auth' => true,
    ]);

    // 支付订单管理
    Route::group('payment/order', function() {
        Route::get('list', '/getList')->name('PaymentOrderList')->option([
            '_alias' => '支付订单列表'
        ]);
        Route::post('close/:no', '/close')->name('PaymentOrderClose')->option([
            '_alias' => '关闭订单'
        ]);
    })->prefix('admin.payment.OrderController')->option([
        '_path' => '/payment/order',
        '_auth' => true,
    ]);

    // 支付架构管理
    Route::group('payment/architecture', function() {
        Route::get('overview', '/getOverview')->name('PaymentArchOverview')->option([
            '_alias' => '获取架构概览'
        ]);
        Route::get('methods', '/getMethods')->name('PaymentArchMethods')->option([
            '_alias' => '获取支付方式'
        ]);
        Route::post('methods', '/createMethod')->name('PaymentArchCreateMethod')->option([
            '_alias' => '创建支付方式'
        ]);
        Route::post('methods/:id', '/updateMethod')->name('PaymentArchUpdateMethod')->option([
            '_alias' => '更新支付方式'
        ]);
        Route::delete('methods/:id', '/deleteMethod')->name('PaymentArchDeleteMethod')->option([
            '_alias' => '删除支付方式'
        ]);
        Route::get('available-types', '/getAvailablePaymentTypes')->name('PaymentArchAvailableTypes')->option([
            '_alias' => '获取可用支付方式类型'
        ]);
        Route::post('test', '/testConnectivity')->name('PaymentArchTest')->option([
            '_alias' => '测试连通性'
        ]);
    })->prefix('admin.payment.PaymentArchitectureController')->option([
        '_path' => '/payment/architecture',
        '_auth' => true,
    ]);

    // 配置管理
    Route::group('payment/config', function() {
        Route::get('partner', '/getPartnerConfigs')->name('PaymentConfigPartner')->option([
            '_alias' => '获取合作伙伴配置'
        ]);
        Route::get('merchant', '/getMerchantConfigs')->name('PaymentConfigMerchant')->option([
            '_alias' => '获取商户配置'
        ]);
        Route::get('methods', '/getPaymentMethods')->name('PaymentConfigMethods')->option([
            '_alias' => '获取支付方式列表'
        ]);
        Route::get('methods-config', '/getPaymentMethodsConfig')->name('PaymentConfigMethodsConfig')->option([
            '_alias' => '获取支付方式配置'
        ]);
        Route::get('merchants', '/getMerchantList')->name('PaymentConfigMerchants')->option([
            '_alias' => '获取商户列表'
        ]);
        Route::delete('methods-config/cache', '/clearPaymentMethodsCache')->name('PaymentConfigClearCache')->option([
            '_alias' => '清除支付方式配置缓存'
        ]);
        Route::get('test-methods', '/testMethods')->name('PaymentConfigTestMethods')->option([
            '_alias' => '测试支付方式路由'
        ]);
        Route::post('create', '/createPaymentConfig')->name('PaymentConfigCreate')->option([
            '_alias' => '创建配置'
        ]);
        Route::get(':type/:id', '/getPaymentConfig')->name('PaymentConfigGet')->option([
            '_alias' => '获取配置详情'
        ]);
        Route::post(':type/:id', '/updatePaymentConfig')->name('PaymentConfigUpdate')->option([
            '_alias' => '更新配置'
        ]);
        Route::delete(':type/:id', '/deletePaymentConfig')->name('PaymentConfigDelete')->option([
            '_alias' => '删除配置'
        ]);
        Route::post('hot-update', '/hotUpdate')->name('PaymentConfigHotUpdate')->option([
            '_alias' => '配置热更新'
        ]);

        // 新版分层配置API
        Route::get('get', '/getPaymentConfig')->name('PaymentConfigGetLayered')->option([
            '_alias' => '获取分层配置'
        ]);
        Route::post('update', '/updatePaymentConfig')->name('PaymentConfigUpdateLayered')->option([
            '_alias' => '更新分层配置'
        ]);
        Route::get('modes', '/getPaymentModes')->name('PaymentConfigModes')->option([
            '_alias' => '获取支付模式'
        ]);
        Route::get('template', '/getConfigTemplate')->name('PaymentConfigTemplate')->option([
            '_alias' => '获取配置模板'
        ]);
        Route::get('validate', '/validateConfig')->name('PaymentConfigValidate')->option([
            '_alias' => '验证配置'
        ]);
        Route::post('clear-cache', '/clearCache')->name('PaymentConfigClearCacheLayered')->option([
            '_alias' => '清除配置缓存'
        ]);
        Route::post('gray-release', '/grayRelease')->name('PaymentConfigGrayRelease')->option([
            '_alias' => '灰度发布'
        ]);
        Route::get('gray-releases', '/getGrayReleases')->name('PaymentConfigGrayReleases')->option([
            '_alias' => '获取灰度发布列表'
        ]);
        Route::post('gray-releases/:id/:action', '/grayReleaseAction')->name('PaymentConfigGrayAction')->option([
            '_alias' => '灰度发布操作'
        ]);
    })->prefix('admin.payment.ConfigController')->option([
        '_path' => '/payment/config',
        '_auth' => true,
    ]);

    // 商户支付配置管理
    Route::group('merchant/payment/config', function() {
        Route::get('types', '/getPaymentTypes')->name('MerchantPaymentTypes')->option([
            '_alias' => '获取支付类型列表'
        ]);
        Route::get('type_info', '/getPaymentTypeInfo')->name('MerchantPaymentTypeInfo')->option([
            '_alias' => '获取支付类型信息'
        ]);
        Route::get('lst/:merId', '/getPaymentConfigList')->name('MerchantPaymentConfigList')->option([
            '_alias' => '获取商户支付配置列表'
        ]);
        Route::get('lst', '/getPaymentConfigList')->name('MerchantPaymentConfigListAll')->option([
            '_alias' => '获取所有商户支付配置列表'
        ]);
        Route::get('detail/:merId/:type', '/getPaymentConfigDetail')->name('MerchantPaymentConfigDetail')->option([
            '_alias' => '获取商户支付配置详情'
        ]);
        Route::get('merchant/:merId', '/getPaymentConfig')->name('MerchantPaymentConfig')->option([
            '_alias' => '获取商户支付配置'
        ]);
        Route::post('merchant/:merId', '/updatePaymentConfig')->name('MerchantPaymentConfigUpdate')->option([
            '_alias' => '更新商户支付配置'
        ]);
        Route::get(':id', '/getPaymentConfigById')->name('MerchantPaymentConfigById')->option([
            '_alias' => '根据ID获取支付配置详情'
        ]);
        Route::post('status/:id', '/setPaymentConfigStatus')->name('MerchantPaymentConfigStatus')->option([
            '_alias' => '设置支付配置状态'
        ]);
        Route::post('set_default/:merId/:id', '/setDefaultPaymentConfig')->name('MerchantPaymentConfigDefault')->option([
            '_alias' => '设置默认支付配置'
        ]);
        Route::delete('delete/:id', '/deletePaymentConfig')->name('MerchantPaymentConfigDelete')->option([
            '_alias' => '删除支付配置'
        ]);
    })->prefix('admin.payment.ConfigController')->option([
        '_path' => '/merchant/payment/config',
        '_auth' => true,
    ]);

    // 性能监控
    Route::group('payment/metrics', function() {
        Route::get('realtime', '/getRealtime')->name('PaymentMetricsRealtime')->option([
            '_alias' => '获取实时指标'
        ]);
        Route::get('history', '/getHistory')->name('PaymentMetricsHistory')->option([
            '_alias' => '获取历史指标'
        ]);
        Route::get('report', '/getReport')->name('PaymentMetricsReport')->option([
            '_alias' => '获取性能报告'
        ]);
        Route::get('export', '/exportReport')->name('PaymentMetricsExport')->option([
            '_alias' => '导出性能报告'
        ]);
    })->prefix('admin.payment.MetricsController')->option([
        '_path' => '/payment/metrics',
        '_auth' => true,
    ]);

    // 智能路由
    Route::group('payment/router', function() {
        Route::get('config', '/getConfig')->name('PaymentRouterGetConfig')->option([
            '_alias' => '获取路由配置'
        ]);
        Route::post('config', '/updateConfig')->name('PaymentRouterUpdateConfig')->option([
            '_alias' => '更新路由配置'
        ]);
        Route::get('logs', '/getLogs')->name('PaymentRouterLogs')->option([
            '_alias' => '获取路由日志'
        ]);
        Route::get('stats', '/getStats')->name('PaymentRouterStats')->option([
            '_alias' => '获取路由统计'
        ]);
        Route::get('detail/:id', '/getDetail')->name('PaymentRouterDetail')->option([
            '_alias' => '获取路由详情'
        ]);
        Route::post('test', '/test')->name('PaymentRouterTest')->option([
            '_alias' => '路由测试'
        ]);
        Route::post('batch-optimize', '/batchOptimize')->name('PaymentRouterBatchOptimize')->option([
            '_alias' => '批量路由优化'
        ]);
    })->prefix('admin.payment.PaymentRouterController')->option([
        '_path' => '/payment/router',
        '_auth' => true,
    ]);

    // 告警管理
    Route::group('payment/alerts', function() {
        Route::get('rules', '/getRules')->name('PaymentAlertsRules')->option([
            '_alias' => '获取告警规则'
        ]);
        Route::post('rules', '/createRule')->name('PaymentAlertsCreateRule')->option([
            '_alias' => '创建告警规则'
        ]);
        Route::put('rules/:id', '/updateRule')->name('PaymentAlertsUpdateRule')->option([
            '_alias' => '更新告警规则'
        ]);
        Route::delete('rules/:id', '/deleteRule')->name('PaymentAlertsDeleteRule')->option([
            '_alias' => '删除告警规则'
        ]);
        Route::get('records', '/getRecords')->name('PaymentAlertsRecords')->option([
            '_alias' => '获取告警记录'
        ]);
        Route::post('records/:id/handled', '/markAlertHandled')->name('PaymentAlertsHandle')->option([
            '_alias' => '处理告警'
        ]);
        Route::post('records/batch-handle', '/batchHandleAlerts')->name('PaymentAlertsBatchHandle')->option([
            '_alias' => '批量处理告警'
        ]);
        Route::get('stats', '/getAlertStats')->name('PaymentAlertsStats')->option([
            '_alias' => '获取告警统计'
        ]);
    })->prefix('admin.payment.PaymentAlertController')->option([
        '_path' => '/payment/alerts',
        '_auth' => true,
    ]);

    // 审计日志
    Route::group('payment/audit', function() {
        Route::get('logs', '/getLogs')->name('PaymentAuditLogs')->option([
            '_alias' => '获取审计日志'
        ]);
        Route::get('config', '/getConfigAuditLogs')->name('PaymentAuditConfigLogs')->option([
            '_alias' => '获取配置审计日志'
        ]);
        Route::get('operation', '/getOperationAuditLogs')->name('PaymentAuditOperationLogs')->option([
            '_alias' => '获取操作审计日志'
        ]);
        Route::get('payment', '/getPaymentAuditLogs')->name('PaymentAuditPaymentLogs')->option([
            '_alias' => '获取支付审计日志'
        ]);
        Route::get('export', '/exportAuditLogs')->name('PaymentAuditExport')->option([
            '_alias' => '导出审计日志'
        ]);
        Route::post('cleanup', '/cleanupAuditLogs')->name('PaymentAuditCleanup')->option([
            '_alias' => '清理审计日志'
        ]);
        Route::get('stats', '/getAuditStats')->name('PaymentAuditStats')->option([
            '_alias' => '获取审计统计'
        ]);
    })->prefix('admin.payment.PaymentAuditController')->option([
        '_path' => '/payment/audit',
        '_auth' => true,
    ]);

    // 系统管理
    Route::group('payment/system', function() {
        Route::get('status', '/getStatus')->name('PaymentSystemStatus')->option([
            '_alias' => '获取系统状态'
        ]);
        Route::post('clear-cache', '/clearCache')->name('PaymentSystemClearCache')->option([
            '_alias' => '清理系统缓存'
        ]);
        Route::post('diagnosis', '/runDiagnosis')->name('PaymentSystemDiagnosis')->option([
            '_alias' => '运行系统诊断'
        ]);
        Route::get('config', '/getConfig')->name('PaymentSystemGetConfig')->option([
            '_alias' => '获取系统配置'
        ]);
        Route::post('config', '/updateConfig')->name('PaymentSystemUpdateConfig')->option([
            '_alias' => '更新系统配置'
        ]);
    })->prefix('admin.payment.PaymentSystemController')->option([
        '_path' => '/payment/system',
        '_auth' => true,
    ]);

    // 数据报表管理 - 新增功能
    Route::group('payment/reports', function() {
        Route::get('overview', '/getOverview')->name('PaymentReportsOverview')->option([
            '_alias' => '获取支付概览数据'
        ]);
        Route::get('payment-methods', '/getPaymentMethodAnalysis')->name('PaymentReportsPaymentMethods')->option([
            '_alias' => '获取支付方式分析'
        ]);
        Route::get('merchant-ranking', '/getMerchantRanking')->name('PaymentReportsMerchantRanking')->option([
            '_alias' => '获取商户排行榜'
        ]);
        Route::get('trends', '/getTrendAnalysis')->name('PaymentReportsTrends')->option([
            '_alias' => '获取趋势分析'
        ]);
        Route::post('custom', '/generateCustomReport')->name('PaymentReportsCustom')->option([
            '_alias' => '生成自定义报表'
        ]);
        Route::get('export', '/exportReport')->name('PaymentReportsExport')->option([
            '_alias' => '导出报表数据'
        ]);
    })->prefix('admin.payment.PaymentReportController')->option([
        '_path' => '/payment/reports',
        '_auth' => true,
    ]);

})->middleware(AllowOriginMiddleware::class)
    ->middleware(AdminTokenMiddleware::class, true)
    ->middleware(AdminAuthMiddleware::class)
    ->middleware(LogMiddleware::class); 
