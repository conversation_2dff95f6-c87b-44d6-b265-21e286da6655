<?php

/**
 * 测试分层配置架构
 */

require_once __DIR__ . '/vendor/autoload.php';

use crmeb\services\payment\config\PaymentConfigCenter;
use crmeb\services\payment\config\LayeredConfigResolver;
use crmeb\services\payment\config\PaymentModeEnum;

// 初始化应用
$app = new \think\App();
$app->initialize();

echo "=== 测试分层配置架构 ===\n\n";

// 测试商户ID
$testMerId = 1;

// 测试支付类型
$paymentTypes = ['wechat', 'alipay', 'routine'];

foreach ($paymentTypes as $paymentType) {
    echo "--- 测试支付类型: {$paymentType} ---\n";
    
    try {
        // 1. 测试配置解析
        echo "1. 获取有效配置:\n";
        $config = PaymentConfigCenter::getEffectiveConfig($testMerId, $paymentType);
        if ($config) {
            echo "   配置获取成功，包含字段: " . implode(', ', array_keys($config)) . "\n";
        } else {
            echo "   配置为空\n";
        }
        
        // 2. 测试支付模式识别
        echo "2. 支付模式识别:\n";
        $resolver = new LayeredConfigResolver();
        $fullConfig = $resolver->resolveConfig($testMerId, $paymentType);
        $paymentMode = $fullConfig['_meta']['payment_mode'] ?? 'unknown';
        $modeName = PaymentModeEnum::getModeDisplayName($paymentMode);
        echo "   支付模式: {$paymentMode} ({$modeName})\n";
        
        // 3. 测试配置层级
        echo "3. 配置层级:\n";
        $configLayers = PaymentModeEnum::getConfigLayers($paymentMode);
        foreach ($configLayers as $layer => $configType) {
            echo "   {$layer}: {$configType}\n";
        }
        
        // 4. 测试配置模板
        echo "4. 配置模板:\n";
        $template = PaymentConfigCenter::getConfigTemplate($paymentType, $testMerId);
        if ($template) {
            echo "   模板字段: " . implode(', ', array_keys($template)) . "\n";
        } else {
            echo "   模板为空\n";
        }
        
        // 5. 测试配置验证
        echo "5. 配置验证:\n";
        $isComplete = PaymentConfigCenter::isConfigComplete($paymentType, $config ?: [], $testMerId);
        echo "   配置完整性: " . ($isComplete ? '完整' : '不完整') . "\n";
        
    } catch (Exception $e) {
        echo "   错误: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 测试统一支付服务集成
echo "--- 测试统一支付服务集成 ---\n";
try {
    $unifiedService = new \crmeb\services\payment\UnifiedPaymentService();
    
    // 测试获取商户配置
    $reflection = new ReflectionClass($unifiedService);
    $method = $reflection->getMethod('getMerchantConfig');
    $method->setAccessible(true);
    
    $merchantConfig = $method->invoke($unifiedService, $testMerId, 'wechat');
    
    if ($merchantConfig) {
        echo "统一支付服务配置获取成功，包含字段: " . implode(', ', array_keys($merchantConfig)) . "\n";
    } else {
        echo "统一支付服务配置为空\n";
    }
    
} catch (Exception $e) {
    echo "统一支付服务测试失败: " . $e->getMessage() . "\n";
}

echo "\n--- 测试系统配置 ---\n";
try {
    // 测试系统配置获取
    $systemConfig = PaymentConfigCenter::getSystemConfig('wechat');
    if ($systemConfig) {
        echo "系统配置获取成功，包含字段: " . implode(', ', array_keys($systemConfig)) . "\n";
    } else {
        echo "系统配置为空\n";
    }
    
} catch (Exception $e) {
    echo "系统配置测试失败: " . $e->getMessage() . "\n";
}

echo "\n--- 测试支付模式枚举 ---\n";
try {
    // 测试所有支付模式
    $allModes = PaymentModeEnum::getAllModes();
    echo "支持的支付模式:\n";
    foreach ($allModes as $mode) {
        $displayName = PaymentModeEnum::getModeDisplayName($mode);
        echo "  {$mode}: {$displayName}\n";
    }
    
    // 测试支付类型映射
    echo "\n支付类型映射:\n";
    foreach (PaymentModeEnum::PAYMENT_TYPE_TO_MODE as $type => $mode) {
        echo "  {$type} => {$mode}\n";
    }
    
} catch (Exception $e) {
    echo "支付模式枚举测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
