import request from './request'

/**
 * 获取API支付列表
 * @param {Object} params
 */
export function getApiPaymentList(params) {
  return request.get('payment/api/list', params)
}

/**
 * 获取支付统计数据
 * @param {Object} params
 */
export function getApiPaymentStatistics(params) {
  return request.get('payment/api/statistics', params)
}

/**
 * 获取支付详情
 * @param {number|string} id
 */
export function getApiPaymentDetail(id) {
  return request.get(`payment/api/detail/${id}`)
}

/**
 * 导出支付记录
 * @param {Object} params
 */
export function exportApiPaymentData(params) {
  return request.get('payment/api/export', params)
}

/**
 * 同步订单状态
 * @param {string} orderId
 */
export function syncOrderStatus(orderId) {
  return request.get('payment/api/sync', { order_id: orderId })
}

/**
 * 获取已配置支付方式的商户列表
 * @param {Object} params
 */
export function getApiMerchantList(params) {
  return request.get('payment/api/merchants', params)
}

/**
 * 发送通知给合作伙伴
 * @param {Object} data
 */
export function sendApiNotification(data) {
  return request.post('payment/api/notify', data)
}

/**
 * 删除API订单
 * @param {number|string} orderId
 */
export function deleteApiOrder(orderId) {
  return request.delete(`payment/api/order/${orderId}`)
}

/**
 * 订单退款
 * @param {number|string} orderId
 */
export function refundApiOrder(orderId) {
  return request.post('payment/api/refund', { order_id: orderId })
}

// ==================== 支付订单管理 ====================

/**
 * @description 支付订单 -- 获取支付订单列表
 */
export function getPaymentOrderListApi(params) {
  return request.get('payment/order/list', params)
}

/**
 * @description 支付订单 -- 关闭订单
 */
export function closePaymentOrderApi(orderNo) {
  return request.post(`payment/order/close/${orderNo}`)
}

// ==================== 支付架构管理 ====================

/**
 * @description 支付架构 -- 获取架构概览
 */
export function getPaymentArchitectureApi() {
  return request.get('payment/architecture/overview')
}

/**
 * @description 支付架构 -- 获取支付方式状态
 */
export function getPaymentMethodsApi(data) {
  return request.get('payment/architecture/methods', data)
}

/**
 * @description 支付架构 -- 更新支付方式状态
 */
export function updatePaymentMethodApi(id, data) {
  return request.post(`payment/architecture/methods/${id}`, data)
}

/**
 * @description 支付架构 -- 测试支付连通性
 */
export function testPaymentConnectivityApi(data) {
  return request.post('payment/architecture/test', data)
}

/**
 * @description 支付架构 -- 创建支付方式
 */
export function createPaymentMethodApi(data) {
  return request.post('payment/architecture/methods', data)
}

/**
 * @description 支付架构 -- 删除支付方式
 */
export function deletePaymentMethodApi(id, data = {}) {
  return request.delete(`payment/architecture/methods/${id}`, data)
}

/**
 * @description 支付架构 -- 获取可用支付方式类型
 */
export function getAvailablePaymentTypesApi() {
  return request.get('payment/architecture/available-types')
}

// ==================== 配置管理 ====================

/**
 * @description 配置管理 -- 获取商户配置列表
 */
export function getMerchantConfigsApi(data) {
  return request.get('payment/config/merchant', data)
}

/**
 * @description 配置管理 -- 创建配置
 */
export function createPaymentConfigApi(data) {
  return request.post('payment/config/create', data)
}

/**
 * @description 配置管理 -- 获取配置详情（新版分层架构）
 */
export function getPaymentConfigApi(params = {}) {
  return request.get('payment/config/get', params)
}

/**
 * @description 配置管理 -- 更新配置（新版分层架构）
 */
export function updatePaymentConfigApi(data) {
  return request.post('payment/config/update', data)
}

/**
 * @description 配置管理 -- 获取支付模式信息
 */
export function getPaymentModesApi(params = {}) {
  return request.get('payment/config/modes', params)
}

/**
 * @description 配置管理 -- 获取配置模板
 */
export function getConfigTemplateApi(params = {}) {
  return request.get('payment/config/template', params)
}

/**
 * @description 配置管理 -- 验证配置
 */
export function validateConfigApi(params = {}) {
  return request.get('payment/config/validate', params)
}

/**
 * @description 配置管理 -- 清除配置缓存
 */
export function clearConfigCacheApi(data = {}) {
  return request.post('payment/config/clear-cache', data)
}

/**
 * @description 配置管理 -- 删除配置
 */
export function deletePaymentConfigApi(type, id, data) {
  return request.delete(`payment/config/${type}/${id}`, data)
}

/**
 * @description 配置管理 -- 配置热更新
 */
export function hotUpdateConfigApi(data) {
  return request.post('payment/config/hot-update', data)
}

/**
 * @description 配置管理 -- 灰度发布
 */
export function grayReleaseConfigApi(data) {
  return request.post('payment/config/gray-release', data)
}

/**
 * @description 配置管理 -- 获取灰度发布列表
 */
export function getGrayReleasesApi(data) {
  return request.get('payment/config/gray-releases', data)
}

/**
 * @description 配置管理 -- 灰度发布操作
 */
export function grayReleaseActionApi(id, action) {
  return request.post(`payment/config/gray-releases/${id}/${action}`)
}

/**
 * @description 配置管理 -- 获取支付方式列表
 */
export function getConfigPaymentMethodsApi(data) {
  return request.get('payment/config/methods', data)
}

/**
 * @description 配置管理 -- 获取商户列表
 */
export function getMerchantListApi(data) {
  return request.get('payment/config/merchants', data)
}

// ==================== 性能监控 ====================

/**
 * @description 性能监控 -- 获取实时指标
 */
export function getRealtimeMetricsApi() {
  return request.get('payment/metrics/realtime')
}

/**
 * @description 性能监控 -- 获取历史指标
 */
export function getHistoryMetricsApi(data) {
  return request.get('payment/metrics/history', data)
}

/**
 * @description 性能监控 -- 获取性能报告
 */
export function getPerformanceReportApi(data) {
  return request.get('payment/metrics/report', data)
}

/**
 * @description 性能监控 -- 获取支付成功率统计
 */
export function getSuccessRateStatsApi(data) {
  return request.get('payment/metrics/success-rate', data)
}

/**
 * @description 性能监控 -- 获取响应时间统计
 */
export function getResponseTimeStatsApi(data) {
  return request.get('payment/metrics/response-time', data)
}

/**
 * @description 性能监控 -- 导出性能报告
 */
export function exportMetricsReportApi(data) {
  return request.get('payment/metrics/export', data)
}

// ==================== 智能路由 ====================

/**
 * @description 智能路由 -- 获取路由配置
 */
export function getRouterConfigApi() {
  return request.get('payment/router/config')
}

/**
 * @description 智能路由 -- 更新路由配置
 */
export function updateRouterConfigApi(data) {
  return request.post('payment/router/config', data)
}

/**
 * @description 智能路由 -- 获取路由日志
 */
export function getRouterLogsApi(data) {
  return request.get('payment/router/logs', data)
}

/**
 * @description 智能路由 -- 获取路由统计
 */
export function getRouterStatsApi(data) {
  return request.get('payment/router/stats', data)
}

/**
 * @description 智能路由 -- 路由测试
 */
export function testRouterApi(data) {
  return request.post('payment/router/test', data)
}

/**
 * @description 智能路由 -- 获取路由详情
 */
export function getRouterDetailApi(id) {
  return request.get(`payment/router/detail/${id}`)
}

/**
 * @description 智能路由 -- 批量路由优化
 */
export function batchRouterOptimizeApi(data) {
  return request.post('payment/router/batch-optimize', data)
}

// ==================== 告警管理 ====================

/**
 * @description 告警管理 -- 获取告警规则列表
 */
export function getAlertRulesApi(data) {
  return request.get('payment/alerts/rules', data)
}

/**
 * @description 告警管理 -- 创建告警规则
 */
export function createAlertRuleApi(data) {
  return request.post('payment/alerts/rules', data)
}

/**
 * @description 告警管理 -- 更新告警规则
 */
export function updateAlertRuleApi(id, data) {
  return request.put(`payment/alerts/rules/${id}`, data)
}

/**
 * @description 告警管理 -- 删除告警规则
 */
export function deleteAlertRuleApi(id) {
  return request.delete(`payment/alerts/rules/${id}`)
}

/**
 * @description 告警管理 -- 获取告警记录列表
 */
export function getAlertRecordsApi(data) {
  return request.get('payment/alerts/records', data)
}

/**
 * @description 告警管理 -- 标记告警已处理
 */
export function markAlertHandledApi(id) {
  return request.post(`payment/alerts/records/${id}/handled`)
}

/**
 * @description 告警管理 -- 批量处理告警
 */
export function batchHandleAlertsApi(data) {
  return request.post('payment/alerts/records/batch-handle', data)
}

/**
 * @description 告警管理 -- 获取告警统计
 */
export function getAlertStatsApi(data) {
  return request.get('payment/alerts/stats', data)
}

// ==================== 审计日志 ====================

/**
 * @description 审计日志 -- 获取审计日志列表
 */
export function getAuditLogsApi(data) {
  return request.get('payment/audit/logs', data)
}

/**
 * @description 审计日志 -- 获取配置审计日志
 */
export function getConfigAuditLogsApi(data) {
  return request.get('payment/audit/config', data)
}

/**
 * @description 审计日志 -- 获取操作审计日志
 */
export function getOperationAuditLogsApi(data) {
  return request.get('payment/audit/operation', data)
}

/**
 * @description 审计日志 -- 获取支付审计日志
 */
export function getPaymentAuditLogsApi(data) {
  return request.get('payment/audit/payment', data)
}

/**
 * @description 审计日志 -- 导出审计日志
 */
export function exportAuditLogsApi(data) {
  return request.get('payment/audit/export', data)
}

/**
 * @description 审计日志 -- 清理历史日志
 */
export function cleanupAuditLogsApi(data) {
  return request.post('payment/audit/cleanup', data)
}

/**
 * @description 审计日志 -- 获取审计统计
 */
export function getAuditStatsApi(data) {
  return request.get('payment/audit/stats', data)
}

// ==================== 系统管理 ====================

/**
 * @description 系统管理 -- 获取系统状态
 */
export function getSystemStatusApi() {
  console.log('getSystemStatusApi')
  return request.get('payment/system/status')
}

/**
 * @description 系统管理 -- 清理系统缓存
 */
export function clearSystemCacheApi() {
  return request.post('payment/system/clear-cache')
}

/**
 * @description 系统管理 -- 运行系统诊断
 */
export function runSystemDiagnosisApi() {
  return request.post('payment/system/diagnosis')
}

/**
 * @description 系统管理 -- 获取系统配置
 */
export function getSystemConfigApi() {
  return request.get('payment/system/config')
}

/**
 * @description 系统管理 -- 更新系统配置
 */
export function updateSystemConfigApi(data) {
  return request.post('payment/system/config', data)
}