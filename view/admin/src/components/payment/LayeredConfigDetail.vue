<template>
  <div class="layered-config-component">
    <!-- 配置详情标题 -->
    <div class="config-header" v-if="configData">
      <h4>分层配置详情</h4>
      <p class="text-gray">商户ID: {{ configData.mer_id }} | 支付类型: {{ configData.payment_type }}</p>
    </div>

    <!-- 配置信息展示 -->
    <div v-if="currentConfig">
      <el-row :gutter="20">
        <!-- 配置基本信息 -->
        <el-col :span="8">
          <el-card shadow="hover">
            <div slot="header">
              <span>配置信息</span>
              <el-button style="float: right; padding: 3px 0" type="text" @click="refreshConfig">刷新</el-button>
            </div>
            <div>
              <p><strong>商户ID:</strong> {{ configData.mer_id }}</p>
              <p><strong>支付类型:</strong> {{ configData.payment_type }}</p>
              <p><strong>支付模式:</strong>
                <el-tag :type="getModeTagType(currentConfig.mode_info.payment_mode)">
                  {{ currentConfig.mode_info.mode_name }}
                </el-tag>
              </p>
              <p><strong>配置完整性:</strong>
                <el-tag :type="currentConfig.is_complete ? 'success' : 'warning'">
                  {{ currentConfig.is_complete ? '完整' : '不完整' }}
                </el-tag>
              </p>
              <p><strong>服务商模式:</strong>
                <el-tag :type="currentConfig.mode_info.is_service_mode ? 'warning' : 'info'">
                  {{ currentConfig.mode_info.is_service_mode ? '是' : '否' }}
                </el-tag>
              </p>
            </div>
          </el-card>
        </el-col>

        <!-- 配置层级 -->
        <el-col :span="8">
          <el-card shadow="hover">
            <div slot="header">
              <span>配置层级</span>
            </div>
            <div>
              <div v-for="(configType, layer) in currentConfig.mode_info.config_layers" :key="layer" class="layer-item">
                <el-tag :type="getLayerTagType(layer)" size="small">{{ getLayerName(layer) }}</el-tag>
                <span class="ml10">{{ configType }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 配置字段 -->
        <el-col :span="8">
          <el-card shadow="hover">
            <div slot="header">
              <span>配置字段</span>
              <el-button style="float: right; padding: 3px 0" type="text" @click="showEditDialog">编辑</el-button>
            </div>
            <div>
              <div v-for="(value, key) in currentConfig.config" :key="key" class="config-item">
                <strong>{{ key }}:</strong>
                <span v-if="isSecretField(key)" class="secret-field">***</span>
                <span v-else class="config-value">{{ value || '未配置' }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="请选择左侧配置查看详情">
        <el-button type="primary" @click="$emit('add-config')">新增配置</el-button>
      </el-empty>
    </div>

    <!-- 配置编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="closeDialog"
    >
      <el-form :model="configForm" :rules="configRules" ref="configForm" label-width="120px">
        <el-form-item label="商户ID" prop="mer_id">
          <el-input v-model="configForm.mer_id" :disabled="editMode" placeholder="请输入商户ID" />
        </el-form-item>
        
        <el-form-item label="支付类型" prop="payment_type">
          <el-select v-model="configForm.payment_type" :disabled="editMode" class="w100" @change="onPaymentTypeChange">
            <el-option
              v-for="method in paymentMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
        </el-form-item>

        <!-- 动态配置字段 -->
        <div v-if="configTemplate">
          <div v-for="(fieldDef, fieldName) in configTemplate" :key="fieldName">
            <el-form-item 
              :label="fieldDef.description" 
              :prop="fieldName"
              :required="fieldDef.required"
            >
              <el-tag v-if="fieldDef.layer" size="mini" :type="getLayerTagType(fieldDef.layer)" class="mb5">
                {{ getLayerName(fieldDef.layer) }}
              </el-tag>
              
              <el-input
                v-if="fieldDef.type === 'string'"
                v-model="configForm.config[fieldName]"
                :placeholder="`请输入${fieldDef.description}`"
                :type="isSecretField(fieldName) ? 'password' : 'text'"
                :show-password="isSecretField(fieldName)"
              />
              
              <el-input
                v-else-if="fieldDef.type === 'text'"
                v-model="configForm.config[fieldName]"
                type="textarea"
                :rows="3"
                :placeholder="`请输入${fieldDef.description}`"
              />
              
              <el-switch
                v-else-if="fieldDef.type === 'boolean'"
                v-model="configForm.config[fieldName]"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
      
      <div slot="footer" class="text-center">
        <el-button icon="el-icon-close" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="saveConfig">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getPaymentConfigApi,
  updatePaymentConfigApi,
  getConfigPaymentMethodsApi
} from '@/api/payment'

export default {
  name: 'LayeredConfigComponent',
  props: {
    configData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      currentConfig: null,
      configTemplate: null,
      paymentMethods: [],
      dialogVisible: false,
      editMode: false,
      dialogTitle: '编辑配置',
      configForm: {
        mer_id: '',
        payment_type: '',
        config: {}
      },
      configRules: {
        mer_id: [
          { required: true, message: '请输入商户ID', trigger: 'blur' }
        ],
        payment_type: [
          { required: true, message: '请选择支付类型', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    configData: {
      handler(newVal) {
        if (newVal) {
          this.loadConfig()
        } else {
          this.currentConfig = null
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.getPaymentMethods()
  },
  methods: {
    // 获取支付方式列表
    async getPaymentMethods() {
      try {
        const res = await getConfigPaymentMethodsApi()
        this.paymentMethods = res.data || []
      } catch (error) {
        console.error('获取支付方式失败:', error)
      }
    },

    // 加载配置
    async loadConfig() {
      if (!this.configData || !this.configData.mer_id || !this.configData.payment_type) {
        return
      }

      try {
        const res = await getPaymentConfigApi({
          mer_id: this.configData.mer_id,
          payment_type: this.configData.payment_type
        })

        this.currentConfig = res.data
        this.configTemplate = res.data.template

      } catch (error) {
        console.error('获取配置失败:', error)
        this.currentConfig = null
      }
    },

    // 刷新配置
    refreshConfig() {
      this.loadConfig()
    },

    // 显示编辑对话框
    showEditDialog() {
      if (!this.currentConfig) {
        this.$message.warning('请先选择配置')
        return
      }

      this.editMode = true
      this.dialogTitle = '编辑配置'
      this.configForm = {
        mer_id: this.configData.mer_id,
        payment_type: this.configData.payment_type,
        config: { ...this.currentConfig.config }
      }
      this.dialogVisible = true
    },

    // 支付类型变化
    async onPaymentTypeChange() {
      if (!this.configForm.payment_type) return

      try {
        // 获取配置模板
        const res = await getPaymentConfigApi({
          mer_id: this.configForm.mer_id || 0,
          payment_type: this.configForm.payment_type
        })
        
        this.configTemplate = res.data.template
        
        // 初始化配置字段
        this.configForm.config = {}
        Object.keys(this.configTemplate).forEach(fieldName => {
          this.configForm.config[fieldName] = ''
        })
      } catch (error) {
        console.error('获取配置模板失败:', error)
      }
    },

    // 保存配置
    async saveConfig() {
      try {
        await this.$refs.configForm.validate()

        await updatePaymentConfigApi({
          mer_id: this.configForm.mer_id,
          payment_type: this.configForm.payment_type,
          config: this.configForm.config
        })

        this.$message.success('配置保存成功')
        this.dialogVisible = false

        // 刷新配置显示
        this.refreshConfig()

        // 通知父组件配置已更新
        this.$emit('config-updated', {
          mer_id: this.configForm.mer_id,
          payment_type: this.configForm.payment_type
        })
      } catch (error) {
        this.$message.error('保存配置失败: ' + error.message)
      }
    },

    // 关闭对话框
    closeDialog() {
      this.$refs.configForm.resetFields()
      this.configForm.config = {}
      this.configTemplate = null
    },

    // 获取模式标签类型
    getModeTagType(mode) {
      const typeMap = {
        'wechat_direct': 'primary',
        'wechat_service': 'warning',
        'wechat_ecommerce': 'danger',
        'alipay_direct': 'success',
        'alipay_service': 'warning',
        'alipay_zhifu': 'danger'
      }
      return typeMap[mode] || 'info'
    },

    // 获取层级标签类型
    getLayerTagType(layer) {
      const typeMap = {
        'base': 'primary',
        'service': 'warning',
        'merchant': 'success'
      }
      return typeMap[layer] || 'info'
    },

    // 获取层级名称
    getLayerName(layer) {
      const nameMap = {
        'base': '基础配置',
        'service': '服务商配置',
        'merchant': '商户配置'
      }
      return nameMap[layer] || layer
    },

    // 判断是否为敏感字段
    isSecretField(fieldName) {
      const secretFields = ['key', 'private_key', 'api_v3_key', 'service_key', 'service_v3_key']
      return secretFields.includes(fieldName)
    }
  }
}
</script>

<style scoped>
.layer-item {
  margin-bottom: 8px;
}

.config-item {
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.config-value {
  margin-left: 8px;
  color: #606266;
}

.secret-field {
  margin-left: 8px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.mb5 {
  margin-bottom: 5px;
}

.ml10 {
  margin-left: 10px;
}

.w100 {
  width: 100%;
}
</style>
