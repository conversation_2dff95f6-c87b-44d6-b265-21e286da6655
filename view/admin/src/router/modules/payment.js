
import Layout from '@/layout'
import { roterPre } from '@/settings'
export default {
  path: `${roterPre}/payment`,
  name: 'payment',
  meta: {
    title: '支付管理',
    icon: 'credit-card'
  },
  alwaysShow: true,
  component: Layout,
  redirect: `${roterPre}/payment`,
  children: [
    {
      path: 'api',
      name: 'PaymentApi',
      meta: {
        title: 'API支付'
      },
      component: () => import('@/views/payment/api/index')
    },
    {
      path: 'architecture',
      name: 'PaymentArchitecture',
      meta: {
        title: '支付架构'
      },
      component: () => import('@/views/payment/architecture/index')
    },
    {
      path: 'config',
      name: 'PaymentConfig',
      meta: {
        title: '配置管理'
      },
      component: () => import('@/views/payment/config/index')
    },
    {
      path: 'metrics',
      name: 'PaymentMetrics',
      meta: {
        title: '性能监控'
      },
      component: () => import('@/views/payment/metrics/index')
    },
    {
      path: 'router',
      name: 'PaymentRouter',
      meta: {
        title: '智能路由'
      },
      component: () => import('@/views/payment/router/index')
    },
    {
      path: 'alerts',
      name: 'PaymentAlerts',
      meta: {
        title: '告警管理'
      },
      component: () => import('@/views/payment/alerts/index')
    },
    {
      path: 'audit',
      name: 'PaymentAudit',
      meta: {
        title: '审计日志'
      },
      component: () => import('@/views/payment/audit/index')
    },
    {
      path: 'system',
      name: 'PaymentSystem',
      meta: {
        title: '系统管理'
      },
      component: () => import('@/views/payment/system/index')
    }
  ]
} 