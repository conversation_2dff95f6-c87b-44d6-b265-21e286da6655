<template>
  <div class="divBox">
    <el-card class="box-card FromData">
      <div slot="header" class="clearfix">
        <span>支付宝商户授权管理</span>
      </div>
      
      <!-- 筛选条件 -->
      <el-form :inline="true" :model="searchForm" ref="searchForm" size="small" label-width="85px" class="search-form">
        <el-form-item label="商户名称" prop="mer_name">
          <el-input v-model="searchForm.mer_name" placeholder="请输入商户名称" clearable class="selWidth"></el-input>
        </el-form-item>
        <el-form-item label="授权状态" prop="status">
          <el-select v-model="searchForm.status" clearable placeholder="请选择" class="selWidth">
            <el-option label="全部" value=""></el-option>
            <el-option label="已授权" value="1"></el-option>
            <el-option label="未授权" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="getList">查询</el-button>
          <el-button size="small" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        size="small">
        <el-table-column
          prop="mer_id"
          label="商户ID"
          width="80"
          align="center">
        </el-table-column>
        <el-table-column
          prop="mer_name"
          label="商户名称"
          min-width="120"
          align="center">
        </el-table-column>
        <el-table-column
          prop="status"
          label="授权状态"
          width="100"
          align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1" type="success">已授权</el-tag>
            <el-tag v-else type="danger">未授权</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="auth_app_id"
          label="授权应用ID"
          min-width="150"
          align="center">
          <template slot-scope="scope">
            {{ scope.row.auth_app_id || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="user_id"
          label="授权用户ID"
          min-width="150"
          align="center">
          <template slot-scope="scope">
            {{ scope.row.user_id || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="auth_time"
          label="授权时间"
          min-width="160"
          align="center">
          <template slot-scope="scope">
            {{ scope.row.auth_time ? formatDate(scope.row.auth_time) : '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="expire_time"
          label="过期时间"
          min-width="160"
          align="center">
          <template slot-scope="scope">
            {{ scope.row.expire_time ? formatDate(scope.row.expire_time) : '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="200"
          fixed="right"
          align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status !== 1" type="primary" size="small" @click="generateAuthUrl(scope.row)">生成授权链接</el-button>
            <el-button v-if="scope.row.status === 1" type="warning" size="small" @click="refreshAuthToken(scope.row)">刷新令牌</el-button>
            <el-button v-if="scope.row.status === 1" type="danger" size="small" @click="cancelAuth(scope.row)">取消授权</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
      
      <!-- 授权链接弹窗 -->
      <el-dialog
        title="支付宝授权"
        :visible.sync="dialogVisible"
        width="500px"
        center
        :close-on-click-modal="false"
        :close-on-press-escape="false">
        <div class="auth-dialog-content">
          <p>请将以下链接发送给商户，引导商户前往支付宝进行授权操作：</p>
          <div class="auth-url-actions">
            <el-button type="primary" size="small" @click="copyAuthUrl">复制授权链接</el-button>
          </div>
          <div class="auth-url-info">
            <el-form :model="{ url: authUrl }" label-width="80px" size="small">
              <el-form-item label="授权链接">
                <el-input v-model="authUrl" readonly></el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { alipayAuthListApi, alipayAuthUrlApi, alipayAuthCancelApi } from '@/api/merchant'

export default {
  name: 'AlipayAuthList',
  data() {
    return {
      searchForm: {
        mer_name: '',
        status: ''
      },
      tableData: [],
      loading: false,
      page: 1,
      limit: 10,
      total: 0,
      authUrl: '',
      dialogVisible: false,
      currentMerchant: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取授权列表
    getList() {
      this.loading = true
      const params = {
        page: this.page,
        limit: this.limit,
        mer_name: this.searchForm.mer_name,
        status: this.searchForm.status
      }
      
      alipayAuthListApi(params).then(res => {
        if (res.status === 200 && res.data.status === 200) {
          this.tableData = res.data.data.list || []
          this.total = res.data.data.count || 0
        } else {
          this.$message.error(res.data.message || '获取授权列表失败')
        }
      }).catch(err => {
        this.$message.error('获取授权列表失败：' + err.message)
      }).finally(() => {
        this.loading = false
      })
    },
    
    // 重置搜索条件
    resetSearch() {
      this.searchForm = {
        mer_name: '',
        status: ''
      }
      this.page = 1
      this.getList()
    },
    
    // 生成授权链接
    generateAuthUrl(row) {
      this.currentMerchant = row
      this.loading = true
      alipayAuthUrlApi(row.mer_id).then(res => {
        if (res.status === 200 && res.data.status === 200) {
          this.authUrl = res.data.data.url || ''
          this.dialogVisible = true
        } else {
          this.$message.error(res.data.message || '生成授权链接失败')
        }
      }).catch(err => {
        this.$message.error('生成授权链接失败：' + err.message)
      }).finally(() => {
        this.loading = false
      })
    },
    
    // 刷新授权令牌
    refreshAuthToken(row) {
      this.$confirm(`确定要刷新商户"${row.mer_name}"的授权令牌吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        this.$http.post(`merchant/alipay/auth/refresh/${row.mer_id}`).then(res => {
          if (res.status === 200 && res.data.status === 200) {
            this.$message.success('刷新授权令牌成功')
            this.getList()
          } else {
            this.$message.error(res.data.message || '刷新授权令牌失败')
          }
        }).catch(err => {
          this.$message.error('刷新授权令牌失败：' + err.message)
        }).finally(() => {
          this.loading = false
        })
      }).catch(() => {
        // 取消操作
      })
    },
    
    // 取消授权
    cancelAuth(row) {
      this.$confirm('确定要取消该商户的支付宝授权吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        alipayAuthCancelApi(row.mer_id).then(res => {
          if (res.status === 200 && res.data.status === 200) {
            this.$message.success('取消授权成功')
            this.getList()
          } else {
            this.$message.error(res.data.message || '取消授权失败')
          }
        }).catch(err => {
          this.$message.error('取消授权失败：' + err.message)
        }).finally(() => {
          this.loading = false
        })
      }).catch(() => {
        // 取消操作
      })
    },
    
    // 复制授权链接
    copyAuthUrl() {
      if (this.authUrl) {
        const input = document.createElement('input')
        input.value = this.authUrl
        document.body.appendChild(input)
        input.select()
        document.execCommand('copy')
        document.body.removeChild(input)
        this.$message.success('授权链接已复制到剪贴板')
      } else {
        this.$message.error('授权链接为空')
      }
    },
    
    // 处理每页显示条数变化
    handleSizeChange(val) {
      this.limit = val
      this.page = 1
      this.getList()
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.page = val
      this.getList()
    },
    
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '-'
      const date = new Date(timestamp * 1000)
      return date.getFullYear() + '-' + 
        (date.getMonth() + 1).toString().padStart(2, '0') + '-' + 
        date.getDate().toString().padStart(2, '0') + ' ' + 
        date.getHours().toString().padStart(2, '0') + ':' + 
        date.getMinutes().toString().padStart(2, '0') + ':' + 
        date.getSeconds().toString().padStart(2, '0')
    }
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.auth-dialog-content {
  text-align: center;
}
.auth-url-info {
  text-align: left;
  margin: 20px 0;
}
.auth-url-info p {
  margin-bottom: 10px;
}
.auth-url-actions {
  margin-top: 20px;
}
</style>
