<template>
  <div class="divBox">
    <!-- 筛选卡片 -->
    <div class="selCard">
      <el-form ref="searchForm" :model="searchForm" :inline="true" class="demo-form-inline">
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入商户名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="支付方式" prop="payment_type">
          <el-select v-model="searchForm.payment_type" placeholder="请选择支付方式" clearable style="width: 150px">
            <el-option
              v-for="method in paymentMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList(1)">查询</el-button>
          <el-button @click="searchReset">重置</el-button>
          <el-button type="success" @click="refreshData">刷新</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 主要内容 -->
    <el-card class="mt14">
      <div class="mb20">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="商户配置" name="merchant"></el-tab-pane>
          <el-tab-pane label="灰度发布" name="gray"></el-tab-pane>
        </el-tabs>
        
        <div class="flex">
          <div class="flex-auto">
            <el-button v-if="activeTab !== 'gray'" size="small" type="primary" icon="el-icon-plus" @click="showAddDialog">
              添加配置
            </el-button>
            <el-button v-if="activeTab === 'gray'" size="small" type="primary" icon="el-icon-upload2" @click="showGrayReleaseDialog">
              发起灰度发布
            </el-button>
          </div>
          <div>
            <el-button size="small" type="info" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
          </div>
        </div>
      </div>

    
      <!-- 商户配置表格 -->
      <div v-show="activeTab === 'merchant'">
        <el-table v-loading="listLoading" :data="merchantConfigs" size="small">
          <el-table-column prop="merchant_id" label="商户ID" min-width="100" />
          <el-table-column prop="merchant_name" label="商户名称" min-width="200">
            <template slot-scope="scope">
              {{ scope.row.merchant_name || '未知商户' }}
            </template>
          </el-table-column>
          <el-table-column prop="payment_type" label="支付方式" min-width="120">
            <template slot-scope="scope">
              <el-tag>{{ getPaymentTypeName(scope.row.payment_type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="config_type" label="配置类型" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="getConfigTypeTag(scope.row.config_type)">
                {{ getConfigTypeName(scope.row.config_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_service_mode" label="服务商模式" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="scope.row.is_service_mode ? 'warning' : 'info'">
                {{ scope.row.is_service_mode ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                {{ scope.row.status === 'active' ? '激活' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新时间" min-width="180">
            <template slot-scope="scope">
              {{ formatTime(scope.row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="220" fixed="right">
            <template slot-scope="scope">
              <div class="button-group">
                <div class="button-row">
                  <el-button type="info" size="small" icon="el-icon-view" @click="viewConfig(scope.row)">查看</el-button>
                  <el-button type="success" size="small" icon="el-icon-edit" @click="editConfig(scope.row)">编辑</el-button>
                </div>
                <div class="button-row">
                  <el-button type="primary" size="small" icon="el-icon-refresh" @click="hotUpdate(scope.row)">更新</el-button>
                  <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteConfig(scope.row)">删除</el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 灰度发布表格 -->
      <div v-show="activeTab === 'gray'">
        <el-table v-loading="listLoading" :data="grayReleases" size="small">
          <el-table-column prop="id" label="发布ID" min-width="100" />
          <el-table-column prop="config_type" label="配置类型" min-width="120">
            <template slot-scope="scope">
              <el-tag>商户</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="target_name" label="目标名称" min-width="200" />
          <el-table-column prop="status" label="状态" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="getGrayStatusTag(scope.row.status)">
                {{ getGrayStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="gray_percent" label="灰度比例" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.gray_percent }}%
            </template>
          </el-table-column>
          <el-table-column prop="operator_name" label="操作员" min-width="120" />
          <el-table-column prop="start_time" label="开始时间" min-width="180">
            <template slot-scope="scope">
              {{ formatTime(scope.row.start_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="200" fixed="right">
            <template slot-scope="scope">
              <div class="button-group">
                <div class="button-row" v-if="scope.row.status === 'pending'" style="justify-content: center;">
                  <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-video-play"
                    @click="executeGrayRelease(scope.row)"
                  >
                    执行
                  </el-button>
                </div>
                <div class="button-row" v-if="scope.row.status === 'running'" style="justify-content: center;">
                  <el-button
                    type="success"
                    size="small"
                    icon="el-icon-check"
                    @click="completeGrayRelease(scope.row)"
                  >
                    完成
                  </el-button>
                </div>
                <div class="button-row" v-if="['running', 'success'].includes(scope.row.status)" style="justify-content: center;">
                  <el-button
                    type="warning"
                    size="small"
                    icon="el-icon-back"
                    @click="rollbackGrayRelease(scope.row)"
                  >
                    回滚
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="block">
        <el-pagination
          background
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>

    <!-- 配置编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="closeDialog"
    >
      <el-form :model="configForm" :rules="dynamicConfigRules" ref="configForm" label-width="120px">
        <el-form-item label="配置类型" prop="config_type">
          <el-select v-model="configForm.config_type" :disabled="editMode" class="w100">
            <el-option label="商户配置" value="merchant" />
          </el-select>
        </el-form-item>
        
        
        
        <el-form-item v-if="configForm.config_type === 'merchant'" label="商户" prop="merchant_id">
          <!-- 编辑模式：显示商户名称+配置ID -->
          <template v-if="editMode">
            <el-input 
              :value="getMerchantDisplayText()" 
              :disabled="true" 
              class="w100"
              placeholder="商户信息"
            />
            <!-- 隐藏的配置ID字段 -->
            <el-input v-model="configForm.id" type="hidden" />
          </template>
          <!-- 新增模式：正常的下拉选择 -->
          <template v-else>
            <el-select v-model="configForm.merchant_id" filterable class="w100">
              <el-option
                v-for="merchant in merchantList"
                :key="merchant.mer_id"
                :label="merchant.mer_name"
                :value="merchant.mer_id"
              />
            </el-select>
          </template>
        </el-form-item>
        
        <el-form-item label="支付方式" prop="payment_type">
          <el-select v-model="configForm.payment_type" class="w100" @change="onPaymentTypeChange">
            <el-option
              v-for="method in paymentMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
        </el-form-item>
        
        <!-- 微信支付版本选择 -->
        <el-form-item v-if="isWechatPayment" label="微信版本" prop="wechat_version">
          <el-select v-model="configForm.wechat_version" class="w100" @change="onWechatVersionChange">
            <el-option
              v-for="version in getCurrentPaymentVersions"
              :key="version"
              :label="version.toUpperCase()"
              :value="version"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="应用ID" prop="app_id">
          <el-input v-model="configForm.app_id" placeholder="请输入应用ID" />
        </el-form-item>
        
        <el-form-item label="商户号" prop="mch_id">
          <el-input v-model="configForm.mch_id" placeholder="请输入商户号" />
        </el-form-item>
        
        <!-- 微信V3配置 -->
        <template v-if="isWechatPayment && configForm.wechat_version === 'v3'">
          <el-form-item label="APIv3密钥" prop="api_v3_key">
            <el-input
              v-model="configForm.api_v3_key"
              type="password"
              placeholder="请输入APIv3密钥"
              show-password
            />
          </el-form-item>
          
          <el-form-item label="证书序列号" prop="serial_no">
            <el-input v-model="configForm.serial_no" placeholder="请输入证书序列号" />
          </el-form-item>
          
          <el-form-item label="商户私钥" prop="wechat_private_key">
            <el-input
              v-model="configForm.wechat_private_key"
              type="textarea"
              :rows="3"
              placeholder="请输入商户私钥内容"
            />
          </el-form-item>
          
          <el-form-item label="微信支付证书" prop="wechat_public_key">
            <el-input
              v-model="configForm.wechat_public_key"
              type="textarea"
              :rows="3"
              placeholder="请输入微信支付证书内容"
            />
          </el-form-item>
        </template>
        
        <!-- 支付宝相关字段 -->
        <template v-if="isAlipayPayment">
          <el-form-item label="应用私钥" prop="alipay_private_key">
            <el-input
              v-model="configForm.alipay_private_key"
              type="textarea"
              :rows="3"
              placeholder="请输入应用私钥"
            />
          </el-form-item>
          
          <el-form-item label="支付宝公钥" prop="alipay_public_key">
            <el-input
              v-model="configForm.alipay_public_key"
              type="textarea"
              :rows="3"
              placeholder="请输入支付宝公钥"
            />
          </el-form-item>
          
          <el-form-item label="证书模式">
            <el-switch v-model="configForm.cert_mode" />
            <span class="ml10 text-gray">开启后使用证书验签，更安全</span>
          </el-form-item>
          
          <template v-if="configForm.cert_mode">
            <el-form-item label="应用证书" prop="app_cert">
              <el-input
                v-model="configForm.app_cert"
                type="textarea"
                :rows="3"
                placeholder="请输入应用证书内容"
              />
            </el-form-item>
            
            <el-form-item label="支付宝公钥证书" prop="public_cert">
              <el-input
                v-model="configForm.public_cert"
                type="textarea"
                :rows="3"
                placeholder="请输入支付宝公钥证书内容"
              />
            </el-form-item>
            
            <el-form-item label="根证书" prop="root_cert">
              <el-input
                v-model="configForm.root_cert"
                type="textarea"
                :rows="3"
                placeholder="请输入根证书内容"
              />
            </el-form-item>
          </template>
          
          <el-form-item label="SMID" prop="alipay_smid">
            <el-input v-model="configForm.alipay_smid" placeholder="请输入支付宝SMID（可选）" />
          </el-form-item>
          
          <el-form-item label="授权Token" prop="alipay_auth_token">
            <el-input v-model="configForm.alipay_auth_token" placeholder="请输入授权Token（可选）" />
          </el-form-item>
        </template>
        
        <el-form-item label="服务商模式" v-if="isWechatPayment">
          <el-switch v-model="configForm.is_service_mode" />
          <span class="ml10 text-gray">开启后使用服务商模式</span>
        </el-form-item>
        
        <el-form-item v-if="configForm.is_service_mode" label="子商户号" prop="sub_mch_id">
          <el-input v-model="configForm.sub_mch_id" placeholder="请输入子商户号" />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="configForm.status"
            active-value="active"
            inactive-value="disabled"
            active-text="启用"
            inactive-text="禁用">
          </el-switch>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="text-center">
        <el-button icon="el-icon-close" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="saveConfig">保存</el-button>
      </div>
    </el-dialog>

    <!-- 热更新对话框 -->
    <el-dialog
      title="配置热更新"
      :visible.sync="hotUpdateDialogVisible"
      width="600px"
    >
      <el-form :model="hotUpdateForm" label-width="120px">
        <el-form-item label="更新说明">
          <el-input
            v-model="hotUpdateForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入更新说明"
          />
        </el-form-item>
        <el-form-item label="更新方式">
          <el-radio-group v-model="hotUpdateForm.update_type">
            <el-radio label="immediate">立即更新</el-radio>
            <el-radio label="gray">灰度更新</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="hotUpdateForm.update_type === 'gray'" label="灰度比例">
          <el-slider
            v-model="hotUpdateForm.gray_percent"
            :min="1"
            :max="100"
            show-stops
            :marks="{10: '10%', 50: '50%', 100: '100%'}"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="text-center">
        <el-button @click="hotUpdateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmHotUpdate">确认更新</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { 
  getMerchantConfigsApi, 
  getGrayReleasesApi,
  createPaymentConfigApi,
  updatePaymentConfigApi,
  deletePaymentConfigApi,
  hotUpdateConfigApi,
  grayReleaseActionApi,
  getPaymentConfigApi,
  getMerchantListApi,
  getConfigPaymentMethodsApi
} from '@/api/payment'

export default {
  name: 'PaymentConfig',
  data() {
    return {
      activeTab: 'merchant',
      listLoading: false,
      searchForm: {
        config_type: '',
        payment_type: '',
        keyword: ''
      },
      merchantConfigs: [],
      grayReleases: [],
      merchantList: [],
      paymentMethods: [], // 动态支付方式列表
      tableData: {
        data: [],
        total: 0
      },
      tableFrom: {
        page: 1,
        limit: 20
      },
      dialogVisible: false,
      hotUpdateDialogVisible: false,
      editMode: false,
      dialogTitle: '新增配置',
      configForm: {
        id: null,
        config_type: 'merchant',
        merchant_id: '',
        payment_type: 'wechat',
        wechat_version: 'v3', // 微信版本：v2, v3
        app_id: '',
        mch_id: '',
        key: '',
        // 微信专用字段
        wechat_private_key: '',
        wechat_public_key: '',
        // 支付宝专用字段
        alipay_private_key: '',
        alipay_public_key: '',
        // 通用字段
        private_key: '',
        public_key: '',
        is_service_mode: false,
        sub_mch_id: '',
        cert_mode: false,
        app_cert: '',
        public_cert: '',
        root_cert: '',
        alipay_smid: '',
        alipay_auth_token: '',
        // 微信V3相关
        serial_no: '',
        api_v3_key: '',
        // 证书文件
        cert_path: '',
        key_path: '',
        status: 'active'
      },
      hotUpdateForm: {
        description: '',
        update_type: 'immediate',
        gray_percent: 10
      }
    }
  },
  computed: {
    // 是否为微信支付（使用统一的支付类型映射）
    isWechatPayment() {
      const wechatTypes = ['wechat', 'routine', 'weixinApp', 'weixin', 'weixinQr', 'weixinBarCode', 'h5']
      return wechatTypes.includes(this.configForm.payment_type)
    },
    // 是否为支付宝支付（使用统一的支付类型映射）
    isAlipayPayment() {
      const alipayTypes = ['alipay', 'alipayApp', 'alipayQr', 'alipayBarCode', 'alipay_mini']
      return alipayTypes.includes(this.configForm.payment_type)
    },
    // 获取当前支付类型的标准字段
    standardFields() {
      if (this.isWechatPayment) {
        return ['app_id', 'mch_id', 'key', 'api_v3_key', 'serial_no', 'private_key', 'public_key', 'service_mode', 'sub_mch_id']
      } else if (this.isAlipayPayment) {
        return ['app_id', 'private_key', 'public_key', 'cert_mode', 'app_cert', 'public_cert', 'root_cert', 'alipay_smid', 'alipay_auth_token', 'service_mode']
      }
      return []
    },
    // 获取当前支付类型的必填字段
    requiredFields() {
      if (this.isWechatPayment) {
        return ['app_id', 'mch_id', 'key']
      } else if (this.isAlipayPayment) {
        return ['app_id', 'private_key', 'public_key']
      }
      return []
    },
    // 获取当前支付方式的版本列表
    getCurrentPaymentVersions() {
      const method = this.paymentMethods.find(m => m.value === this.configForm.payment_type)
      return method ? method.versions : []
    },
    // 动态表单验证规则
    dynamicConfigRules() {
      const baseRules = {
        config_type: [
          { required: true, message: '请选择配置类型', trigger: 'change' }
        ],
        payment_type: [
          { required: true, message: '请选择支付方式', trigger: 'change' }
        ],
        app_id: [
          { required: true, message: '请输入应用ID', trigger: 'blur' }
        ],
        mch_id: [
          { required: true, message: '请输入商户号', trigger: 'blur' }
        ],
        key: [
          { required: true, message: '请输入API密钥', trigger: 'blur' }
        ]
      }
      
      // 根据配置类型添加相应的验证规则
      if (this.configForm.config_type === 'merchant') {
        baseRules.merchant_id = [
          { required: true, message: '请选择商户', trigger: 'change' }
        ]
      }
      
      return baseRules
    }
  },
  mounted() {
    this.getList()
    this.loadPaymentMethods()
  },
  methods: {
     
    // 加载支付方式列表
    async loadPaymentMethods() {
      try {
        const res = await getConfigPaymentMethodsApi()
        this.paymentMethods = res.data.list || []
      } catch (error) {
        console.error('获取支付方式失败:', error)
      }
    },
    // 重置搜索
    searchReset() {
      // 安全地重置搜索表单
      if (this.$refs.searchForm) {
        try {
          this.$refs.searchForm.resetFields()
        } catch (error) {
          console.warn('搜索表单重置失败，手动重置:', error)
          // 手动重置搜索表单
          this.searchForm = {
            config_type: '',
            payment_type: '',
            keyword: ''
          }
        }
      } else {
        // 如果表单引用不存在，手动重置
        this.searchForm = {
          config_type: '',
          payment_type: '',
          keyword: ''
        }
      }
      this.getList(1)
    },
    // 标签页切换
    handleTabClick() {
      this.tableFrom.page = 1
      // 清空搜索条件，因为不同tab的搜索字段可能不同
      this.searchForm = {
        config_type: '',
        payment_type: '',
        keyword: ''
      }
      this.getList()
    },
    // 获取列表数据
    async getList(num) {
      this.listLoading = true
      this.tableFrom.page = num ? num : this.tableFrom.page
      
      try {
        if (this.activeTab === 'merchant') {
          const res = await getMerchantConfigsApi({
            page: this.tableFrom.page,
            limit: this.tableFrom.limit,
            ...this.searchForm
          })
          this.merchantConfigs = res.data.list
          this.tableData.total = res.data.total
        } else if (this.activeTab === 'gray') {
          const res = await getGrayReleasesApi({
            page: this.tableFrom.page,
            limit: this.tableFrom.limit
          })
          this.grayReleases = res.data.list
          this.tableData.total = res.data.total
        }
      } catch (error) {
        this.$message.error('获取数据失败')
      } finally {
        this.listLoading = false
      }
    },
    // 刷新数据
    refreshData() {
      this.getList()
      this.$message.success('数据已刷新')
    },
    // 分页处理
    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    },
    // 显示新增对话框
    showAddDialog() {
      this.editMode = false
      this.dialogTitle = '新增配置'
      this.resetForm()
      this.configForm.config_type = this.activeTab
      this.dialogVisible = true
    },
    // 编辑配置
    async editConfig(row) {
      this.editMode = true
      this.dialogTitle = '编辑配置'
      
      try {
        // 根据配置类型获取完整的配置详情
        let configData = { ...row }
        
        if (this.activeTab === 'merchant') {
          // 对于商户配置，row.id是eb_merchant_payment_config.id
          // row.merchant_id是mer_id
          const res = await getPaymentConfigApi({
            mer_id: row.merchant_id,
            payment_type: row.payment_type
          })
          const config = res.data.config || {}
          
          // 判断支付方式类型（使用统一的支付类型映射）
          const wechatTypes = ['wechat', 'routine', 'weixinApp', 'weixin', 'weixinQr', 'weixinBarCode', 'h5']
          const alipayTypes = ['alipay', 'alipayApp', 'alipayQr', 'alipayBarCode', 'alipay_mini']
          const isWechatPayment = wechatTypes.includes(row.payment_type)
          const isAlipayPayment = alipayTypes.includes(row.payment_type)
          
          configData = {
            // 重要：这里的id是eb_merchant_payment_config.id，用于更新时识别记录
            id: row.id,
            config_type: 'merchant',
            merchant_id: row.merchant_id, // 这是mer_id
            merchant_name: row.merchant_name, // 保存商户名称
            payment_type: row.payment_type,
            wechat_version: config.wechat_version || 'v3',
            app_id: config.app_id || '',
            mch_id: config.mch_id || '',
            key: config.key || '',
            api_v3_key: (config.params && config.params.wechat && config.params.wechat.api_v3_key) || config.api_v3_key || '',
            serial_no: (config.params && config.params.wechat && config.params.wechat.serial_no) || config.serial_no || '',
            // 通用字段
            private_key: config.private_key || '',
            public_key: config.public_key || '',
            is_service_mode: config.is_service_mode || false,
            sub_mch_id: config.sub_mch_id || '',
            cert_mode: config.cert_mode || false,
            app_cert: config.app_cert || '',
            public_cert: config.public_cert || '',
            root_cert: config.root_cert || '',
            alipay_smid: config.alipay_smid || '',
            alipay_auth_token: config.alipay_auth_token || '',
            status: row.status || 'active'
          }
          
          // 根据支付方式映射专用字段
          if (isWechatPayment) {
            // 微信支付：将通用字段映射到微信专用字段
            configData.wechat_private_key = config.private_key || ''
            configData.wechat_public_key = config.public_key || ''
            // 清空支付宝专用字段
            configData.alipay_private_key = ''
            configData.alipay_public_key = ''
          } else if (isAlipayPayment) {
            // 支付宝：将通用字段映射到支付宝专用字段
            configData.alipay_private_key = config.private_key || ''
            configData.alipay_public_key = config.public_key || ''
            // 清空微信专用字段
            configData.wechat_private_key = ''
            configData.wechat_public_key = ''
          } else {
            // 其他支付方式：清空所有专用字段
            configData.wechat_private_key = ''
            configData.wechat_public_key = ''
            configData.alipay_private_key = ''
            configData.alipay_public_key = ''
          }
          
        }
        
        this.configForm = configData
        this.dialogVisible = true
        
        // 调试信息
        console.log('编辑配置 - row:', row)
        console.log('编辑配置 - configData:', configData)
        console.log('编辑配置 - editMode:', this.editMode)
        console.log('编辑配置 - 支付方式:', configData.payment_type)
        console.log('编辑配置 - 微信字段:', {
          wechat_private_key: configData.wechat_private_key,
          wechat_public_key: configData.wechat_public_key
        })
        console.log('编辑配置 - 支付宝字段:', {
          alipay_private_key: configData.alipay_private_key,
          alipay_public_key: configData.alipay_public_key
        })
        
      } catch (error) {
        console.error('获取配置详情失败:', error)
        // 如果获取详情失败，使用基本数据
        this.configForm = {
          // 重要：确保id正确设置
          id: row.id,
          config_type: this.activeTab,
          merchant_id: row.merchant_id || '',
          merchant_name: row.merchant_name || '未知商户', // 保存商户名称
          payment_type: row.payment_type || 'wechat',
          wechat_version: 'v3',
          app_id: row.app_id || '',
          mch_id: row.mch_id || '',
          key: '',
          api_v3_key: '',
          serial_no: '',
          // 通用字段
          private_key: '',
          public_key: '',
          // 专用字段
          wechat_private_key: '',
          wechat_public_key: '',
          alipay_private_key: '',
          alipay_public_key: '',
          is_service_mode: row.is_service_mode || false,
          sub_mch_id: '',
          cert_mode: false,
          app_cert: '',
          public_cert: '',
          root_cert: '',
          alipay_smid: '',
          alipay_auth_token: '',
          status: row.status || 'active'
        }
        this.dialogVisible = true
        this.$message.warning('获取配置详情失败，使用基本信息编辑')
      }
    },
    // 查看配置
    viewConfig(row) {
      this.editMode = true
      this.dialogTitle = '查看配置'
      this.configForm = { ...row }
      this.dialogVisible = true
    },
    // 保存配置
    async saveConfig() {
      try {
        await this.$refs.configForm.validate()
        
        // 准备提交数据，进行字段转换
        const submitData = { ...this.configForm }
        
        // 根据支付方式转换字段
        if (this.isWechatPayment) {
          // 微信支付：将专用字段转换为通用字段
          if (submitData.wechat_private_key) {
            submitData.private_key = submitData.wechat_private_key
          }
          if (submitData.wechat_public_key) {
            submitData.public_key = submitData.wechat_public_key
          }
        } else if (this.isAlipayPayment) {
          // 支付宝：将专用字段转换为通用字段
          if (submitData.alipay_private_key) {
            submitData.private_key = submitData.alipay_private_key
          }
          if (submitData.alipay_public_key) {
            submitData.public_key = submitData.alipay_public_key
          }
        }
        
        // 调试信息
        console.log('保存配置 - editMode:', this.editMode)
        console.log('保存配置 - configForm.id:', this.configForm.id)
        console.log('保存配置 - submitData:', submitData)
        
        if (this.editMode && this.configForm.id) {
          console.log('执行更新操作')
          // 更新操作：使用配置记录的ID
          if (submitData.config_type === 'merchant') {
            // 对于商户配置的编辑模式，传递配置记录ID，而不是商户ID
            await updatePaymentConfigApi({
              mer_id: submitData.merchant_id,
              payment_type: submitData.payment_type,
              config: submitData
            })
          } else {
            // 对于合作伙伴配置
            await updatePaymentConfigApi({
              mer_id: submitData.merchant_id,
              payment_type: submitData.payment_type,
              config: submitData
            })
          }
          this.$message.success('配置更新成功')
        } else {
          console.log('执行新增操作')
          // 新增配置API调用
          await createPaymentConfigApi(submitData)
          this.$message.success('配置创建成功')
        }
        this.dialogVisible = false
        this.getList()
      } catch (error) {
        console.error('配置保存失败:', error)
        this.$message.error('配置保存失败: ' + (error.message || error))
      }
    },
    // 热更新
    hotUpdate(row) {
      this.selectedConfig = row
      this.hotUpdateDialogVisible = true
    },
    // 确认热更新
    async confirmHotUpdate() {
      try {
        const data = {
          config_id: this.selectedConfig.id,
          config_type: this.selectedConfig.config_type || this.activeTab,
          ...this.hotUpdateForm
        }
        await hotUpdateConfigApi(data)
        this.$message.success('配置热更新成功')
        this.hotUpdateDialogVisible = false
        this.getList()
      } catch (error) {
        this.$message.error('配置热更新失败')
      }
    },
    // 删除配置
    async deleteConfig(row) {
      try {
        await this.$confirm('确认删除此配置？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 删除配置API调用
        const configType = row.config_type || this.activeTab
        // 重要：使用配置记录ID，不是merchant_id
        const configId = row.id // 这是eb_merchant_payment_config.id
        
        console.log('删除配置 - configType:', configType)
        console.log('删除配置 - configId:', configId)
        console.log('删除配置 - row:', row)
        
        await deletePaymentConfigApi(configType, configId)
        this.$message.success('配置删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除配置失败:', error)
          this.$message.error('配置删除失败: ' + (error.message || error))
        }
      }
    },
    // 显示灰度发布对话框
    showGrayReleaseDialog() {
      this.$message.info('灰度发布功能开发中')
    },
    // 执行灰度发布
    async executeGrayRelease(row) {
      try {
        await grayReleaseActionApi(row.id, 'execute')
        this.$message.success('灰度发布执行成功')
        this.getList()
      } catch (error) {
        this.$message.error('灰度发布执行失败')
      }
    },
    // 完成灰度发布
    async completeGrayRelease(row) {
      try {
        await grayReleaseActionApi(row.id, 'complete')
        this.$message.success('灰度发布完成')
        this.getList()
      } catch (error) {
        this.$message.error('灰度发布完成失败')
      }
    },
    // 回滚灰度发布
    async rollbackGrayRelease(row) {
      try {
        await this.$confirm('确认回滚此灰度发布？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await grayReleaseActionApi(row.id, 'rollback')
        this.$message.success('灰度发布回滚成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('灰度发布回滚失败')
        }
      }
    },
    // 重置表单
    resetForm() {
      // 先重置表单数据，再调用resetFields，避免undefined值导致的错误
      this.configForm = {
        id: null,
        config_type: 'merchant',
        merchant_id: '',
        payment_type: 'wechat',
        wechat_version: 'v3',
        app_id: '',
        mch_id: '',
        key: '',
        // 微信专用字段
        wechat_private_key: '',
        wechat_public_key: '',
        // 支付宝专用字段
        alipay_private_key: '',
        alipay_public_key: '',
        // 通用字段
        private_key: '',
        public_key: '',
        is_service_mode: false,
        sub_mch_id: '',
        cert_mode: false,
        app_cert: '',
        public_cert: '',
        root_cert: '',
        alipay_smid: '',
        alipay_auth_token: '',
        // 微信V3相关
        serial_no: '',
        api_v3_key: '',
        // 证书文件
        cert_path: '',
        key_path: '',
        status: 'active'
      }
      
      // 在下一个tick中安全地重置表单验证状态
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          try {
            // 先清除验证状态
            this.$refs.configForm.clearValidate()
            // 等待DOM更新后再重置字段
            this.$nextTick(() => {
              if (this.$refs.configForm && this.$refs.configForm.resetFields) {
                this.$refs.configForm.resetFields()
              }
            })
          } catch (error) {
            // 如果resetFields失败，至少清除验证状态
            console.warn('表单重置失败，仅清除验证状态:', error)
            if (this.$refs.configForm && this.$refs.configForm.clearValidate) {
              this.$refs.configForm.clearValidate()
            }
          }
        }
      })
    },
    // 关闭对话框
    closeDialog() {
      this.dialogVisible = false
      this.resetForm()
    },
    // 时间格式化
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp * 1000).toLocaleString()
    },
    // 获取支付方式名称
    getPaymentTypeName(type) {
      const names = {
        wechat: '微信支付',
        alipay: '支付宝',
        routine: '小程序',
        weixinApp: '微信APP',
        alipayApp: '支付宝APP',
        weixinQr: '微信扫码',
        alipayQr: '支付宝扫码',
        alipayBarCode: '支付宝付款码'
      }
      return names[type] || type
    },
    // 获取配置类型标签
    getConfigTypeTag(type) {
      const tags = {      
        merchant_alipay_auth: 'success',
        merchant_config: 'warning',
        system_config: 'danger'
      }
      return tags[type] || 'default'
    },
    // 获取配置类型名称
    getConfigTypeName(type) {
      const names = {
        merchant_alipay_auth: '商户授权',
        merchant_config: '商户配置',
        system_config: '系统配置'
      }
      return names[type] || type
    },
    // 获取灰度状态标签
    getGrayStatusTag(status) {
      const tags = {
        pending: 'info',
        running: 'warning',
        success: 'success',
        failed: 'danger',
        rollback: 'primary'
      }
      return tags[status] || 'default'
    },
    // 获取灰度状态名称
    getGrayStatusName(status) {
      const names = {
        pending: '待执行',
        running: '进行中',
        success: '成功',
        failed: '失败',
        rollback: '已回滚'
      }
      return names[status] || status
    },
    // 支付方式变化处理
    onPaymentTypeChange(paymentType) {
      // 保存关键标识字段，避免在编辑模式下丢失
      const preservedFields = {
        id: this.configForm.id,
        config_type: this.configForm.config_type,
        merchant_id: this.configForm.merchant_id,
        merchant_name: this.configForm.merchant_name, // 保留商户名称
        status: this.configForm.status
      }
      
      // 重置相关字段
      this.configForm.wechat_version = 'v3'
      this.configForm.key = ''
      this.configForm.api_v3_key = ''
      this.configForm.serial_no = ''
      this.configForm.private_key = ''
      this.configForm.public_key = ''
      // 重置微信专用字段
      this.configForm.wechat_private_key = ''
      this.configForm.wechat_public_key = ''
      // 重置支付宝专用字段
      this.configForm.alipay_private_key = ''
      this.configForm.alipay_public_key = ''
      this.configForm.cert_mode = false
      this.configForm.app_cert = ''
      this.configForm.public_cert = ''
      this.configForm.root_cert = ''
      this.configForm.alipay_smid = ''
      this.configForm.alipay_auth_token = ''
      
      // 恢复关键标识字段
      Object.assign(this.configForm, preservedFields)
      
      // 根据支付方式设置默认版本
      if (this.isWechatPayment) {
        const versions = this.getCurrentPaymentVersions || []
        this.configForm.wechat_version = versions.includes('v3') ? 'v3' : (versions[0] || 'v3')
      }
    },
    // 微信版本变化处理
    onWechatVersionChange(version) {
      // 保存关键标识字段，避免在编辑模式下丢失
      const preservedFields = {
        id: this.configForm.id,
        config_type: this.configForm.config_type,
        merchant_id: this.configForm.merchant_id,
        merchant_name: this.configForm.merchant_name, // 保留商户名称
        status: this.configForm.status,
        app_id: this.configForm.app_id,
        mch_id: this.configForm.mch_id
      }
      
      // 清空版本相关的配置
      this.configForm.key = ''
      this.configForm.api_v3_key = ''
      this.configForm.serial_no = ''
      this.configForm.private_key = ''
      this.configForm.public_key = ''
      // 清空微信专用字段
      this.configForm.wechat_private_key = ''
      this.configForm.wechat_public_key = ''
      
      // 恢复关键标识字段
      Object.assign(this.configForm, preservedFields)
    },
    // 获取商户名称
    getMerchantName(merchantId) {
      const merchant = this.merchantList.find(m => m.mer_id === merchantId)
      return merchant ? merchant.mer_name : '未知商户'
    },
    // 获取商户显示文本
    getMerchantDisplayText() {
      // 优先使用保存在configForm中的商户名称（来自后端JOIN查询）
      const merchantName = this.configForm.merchant_name || '未知商户'
      const configId = this.configForm.id || '新配置'
      return `${merchantName} (配置ID: ${configId})`
    }
  }
}
</script>

<style scoped lang="scss">
.divBox {
  background-color: #f5f7f9;
  padding: 14px;
}

.selCard {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
}

.selWidth {
  width: 200px;
}

.w100 {
  width: 100%;
}

.text-gray {
  color: #999;
}

.ml10 {
  margin-left: 10px;
}

.fee-rate {
  color: #e6a23c;
  font-weight: bold;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  padding: 5px 0;
}

.button-row {
  display: flex;
  justify-content: center;
  gap: 10px;
  width: 100%;
}

.mt14 {
  margin-top: 14px;
}

.mb20 {
  margin-bottom: 20px;
}

.text-center {
  text-align: center;
}
</style> 