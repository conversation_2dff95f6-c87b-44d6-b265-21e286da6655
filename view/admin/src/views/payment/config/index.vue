<template>
  <div class="divBox">
    <!-- 筛选卡片 -->
    <div class="selCard">
      <el-form ref="searchForm" :model="searchForm" :inline="true" class="demo-form-inline">
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入商户名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="支付方式" prop="payment_type">
          <el-select v-model="searchForm.payment_type" placeholder="请选择支付方式" clearable style="width: 150px">
            <el-option
              v-for="method in paymentMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList(1)">查询</el-button>
          <el-button @click="searchReset">重置</el-button>
          <el-button type="success" @click="refreshData">刷新</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 主要内容 -->
    <el-card class="mt14">
      <div class="mb20">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="商户配置" name="merchant"></el-tab-pane>
          <el-tab-pane label="灰度发布" name="gray"></el-tab-pane>
        </el-tabs>
        
        <div class="flex">
          <div class="flex-auto">
            <el-button v-if="activeTab !== 'gray'" size="small" type="primary" icon="el-icon-plus" @click="showAddDialog">
              添加配置
            </el-button>
            <el-button v-if="activeTab === 'gray'" size="small" type="primary" icon="el-icon-upload2" @click="showGrayReleaseDialog">
              发起灰度发布
            </el-button>
          </div>
          <div>
            <el-button size="small" type="info" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
          </div>
        </div>
      </div>

    
      <!-- 商户配置表格 -->
      <div v-show="activeTab === 'merchant'">
        <el-table v-loading="listLoading" :data="merchantConfigs" size="small">
          <el-table-column prop="mer_id" label="商户ID" min-width="100" />
          <el-table-column prop="merchant_name" label="商户名称" min-width="200">
            <template slot-scope="scope">
              {{ scope.row.merchant_name || '未知商户' }}
            </template>
          </el-table-column>
          <el-table-column prop="payment_type" label="支付方式" min-width="120">
            <template slot-scope="scope">
              <el-tag>{{ getPaymentTypeName(scope.row.payment_type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="config_type" label="配置类型" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="getConfigTypeTag(scope.row.config_type)">
                {{ getConfigTypeName(scope.row.config_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_service_mode" label="服务商模式" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="scope.row.is_service_mode ? 'warning' : 'info'">
                {{ scope.row.is_service_mode ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                {{ scope.row.status === 'active' ? '激活' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新时间" min-width="180">
            <template slot-scope="scope">
              {{ formatTime(scope.row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="220" fixed="right">
            <template slot-scope="scope">
              <div class="button-group">
                <div class="button-row">
                  <el-button type="info" size="small" icon="el-icon-view" @click="viewConfig(scope.row)">查看</el-button>
                  <el-button type="success" size="small" icon="el-icon-edit" @click="editConfig(scope.row)">编辑</el-button>
                </div>
                <div class="button-row">
                  <el-button type="primary" size="small" icon="el-icon-refresh" @click="hotUpdate(scope.row)">更新</el-button>
                  <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteConfig(scope.row)">删除</el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 灰度发布表格 -->
      <div v-show="activeTab === 'gray'">
        <el-table v-loading="listLoading" :data="grayReleases" size="small">
          <el-table-column prop="id" label="发布ID" min-width="100" />
          <el-table-column prop="config_type" label="配置类型" min-width="120">
            <template slot-scope="scope">
              <el-tag>商户</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="target_name" label="目标名称" min-width="200" />
          <el-table-column prop="status" label="状态" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="getGrayStatusTag(scope.row.status)">
                {{ getGrayStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="gray_percent" label="灰度比例" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.gray_percent }}%
            </template>
          </el-table-column>
          <el-table-column prop="operator_name" label="操作员" min-width="120" />
          <el-table-column prop="start_time" label="开始时间" min-width="180">
            <template slot-scope="scope">
              {{ formatTime(scope.row.start_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="200" fixed="right">
            <template slot-scope="scope">
              <div class="button-group">
                <div class="button-row" v-if="scope.row.status === 'pending'" style="justify-content: center;">
                  <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-video-play"
                    @click="executeGrayRelease(scope.row)"
                  >
                    执行
                  </el-button>
                </div>
                <div class="button-row" v-if="scope.row.status === 'running'" style="justify-content: center;">
                  <el-button
                    type="success"
                    size="small"
                    icon="el-icon-check"
                    @click="completeGrayRelease(scope.row)"
                  >
                    完成
                  </el-button>
                </div>
                <div class="button-row" v-if="['running', 'success'].includes(scope.row.status)" style="justify-content: center;">
                  <el-button
                    type="warning"
                    size="small"
                    icon="el-icon-back"
                    @click="rollbackGrayRelease(scope.row)"
                  >
                    回滚
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="block">
        <el-pagination
          background
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>

    <!-- 配置编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="closeDialog"
    >
      <el-form :model="configForm" :rules="dynamicConfigRules" ref="configForm" label-width="120px">
        <el-form-item label="配置类型" prop="config_type">
          <el-select v-model="configForm.config_type" :disabled="editMode" class="w100">
            <el-option label="商户配置" value="merchant" />
          </el-select>
        </el-form-item>
        
        
        
        <el-form-item v-if="configForm.config_type === 'merchant'" label="商户" prop="mer_id">
          <!-- 编辑模式：显示商户名称+配置ID -->
          <template v-if="editMode">
            <el-input 
              :value="getMerchantDisplayText()" 
              :disabled="true" 
              class="w100"
              placeholder="商户信息"
            />
            <!-- 隐藏的配置ID字段 -->
            <el-input v-model="configForm.id" type="hidden" />
          </template>
          <!-- 新增模式：正常的下拉选择 -->
          <template v-else>
            <el-select v-model="configForm.mer_id" filterable class="w100">
              <el-option
                v-for="merchant in merchantList"
                :key="merchant.mer_id"
                :label="merchant.mer_name"
                :value="merchant.mer_id"
              />
            </el-select>
          </template>
        </el-form-item>
        
        <el-form-item label="支付方式" prop="payment_type">
          <el-select v-model="configForm.payment_type" class="w100" @change="onPaymentTypeChange">
            <el-option
              v-for="method in paymentMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
        </el-form-item>

        <!-- 动态配置字段 -->
        <div v-if="configTemplate && Object.keys(configTemplate).length > 0">
          <el-divider content-position="left">配置字段</el-divider>
          <div v-for="(fieldDef, fieldName) in configTemplate" :key="fieldName">
            <el-form-item
              :label="fieldDef.description"
              :prop="`config.${fieldName}`"
              :required="fieldDef.required"
            >
              <div class="field-header">
                <el-tag v-if="fieldDef.layer" size="mini" :type="getLayerTagType(fieldDef.layer)" class="mb5">
                  {{ getLayerName(fieldDef.layer) }}
                </el-tag>
                <span v-if="fieldDef.required" class="required-mark">*</span>
              </div>

              <el-input
                v-if="fieldDef.type === 'string'"
                v-model="configForm.config[fieldName]"
                :placeholder="`请输入${fieldDef.description}`"
                :type="isSecretField(fieldName) ? 'password' : 'text'"
                :show-password="isSecretField(fieldName)"
              />

              <el-input
                v-else-if="fieldDef.type === 'text'"
                v-model="configForm.config[fieldName]"
                type="textarea"
                :rows="3"
                :placeholder="`请输入${fieldDef.description}`"
              />

              <el-switch
                v-else-if="fieldDef.type === 'boolean'"
                v-model="configForm.config[fieldName]"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 模板加载状态 -->
        <div v-else-if="configForm.payment_type" class="template-loading">
          <el-alert
            title="正在加载配置模板..."
            type="info"
            :closable="false"
            show-icon>
          </el-alert>
        </div>
        
        <el-form-item label="状态">
          <el-switch
            v-model="configForm.status"
            active-value="active"
            inactive-value="disabled"
            active-text="启用"
            inactive-text="禁用">
          </el-switch>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="text-center">
        <el-button icon="el-icon-close" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="saveConfig">保存</el-button>
      </div>
    </el-dialog>

    <!-- 热更新对话框 -->
    <el-dialog
      title="配置热更新"
      :visible.sync="hotUpdateDialogVisible"
      width="600px"
    >
      <el-form :model="hotUpdateForm" label-width="120px">
        <el-form-item label="更新说明">
          <el-input
            v-model="hotUpdateForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入更新说明"
          />
        </el-form-item>
        <el-form-item label="更新方式">
          <el-radio-group v-model="hotUpdateForm.update_type">
            <el-radio label="immediate">立即更新</el-radio>
            <el-radio label="gray">灰度更新</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="hotUpdateForm.update_type === 'gray'" label="灰度比例">
          <el-slider
            v-model="hotUpdateForm.gray_percent"
            :min="1"
            :max="100"
            show-stops
            :marks="{10: '10%', 50: '50%', 100: '100%'}"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="text-center">
        <el-button @click="hotUpdateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmHotUpdate">确认更新</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {
  getMerchantConfigsApi,
  getGrayReleasesApi,
  createPaymentConfigApi,
  updatePaymentConfigApi,
  deletePaymentConfigApi,
  hotUpdateConfigApi,
  grayReleaseActionApi,
  getPaymentConfigApi,
  getMerchantListApi,
  getConfigPaymentMethodsApi,
  getConfigTemplateApi
} from '@/api/payment'

export default {
  name: 'PaymentConfig',
  data() {
    return {
      activeTab: 'merchant',
      listLoading: false,
      searchForm: {
        config_type: '',
        payment_type: '',
        keyword: ''
      },
      merchantConfigs: [],
      grayReleases: [],
      merchantList: [],
      paymentMethods: [], // 动态支付方式列表
      configTemplate: null, // 配置模板
      tableData: {
        data: [],
        total: 0
      },
      tableFrom: {
        page: 1,
        limit: 20
      },
      dialogVisible: false,
      hotUpdateDialogVisible: false,
      editMode: false,
      dialogTitle: '新增配置',
      configForm: {
        id: null,
        config_type: 'merchant',
        mer_id: '',
        payment_type: 'wechat',
        config: {}, // 动态配置字段
        status: 'active'
      },
      hotUpdateForm: {
        description: '',
        update_type: 'immediate',
        gray_percent: 10
      }
    }
  },
  computed: {
    // 动态表单验证规则
    dynamicConfigRules() {
      const baseRules = {
        config_type: [
          { required: true, message: '请选择配置类型', trigger: 'change' }
        ],
        payment_type: [
          { required: true, message: '请选择支付方式', trigger: 'change' }
        ]
      }

      // 根据配置类型添加相应的验证规则
      if (this.configForm.config_type === 'merchant') {
        baseRules.mer_id = [
          { required: true, message: '请选择商户', trigger: 'change' }
        ]
      }

      return baseRules
    }
  },
  mounted() {
    this.getList()
    this.loadPaymentMethods()
  },
  methods: {
     
    // 加载支付方式列表
    async loadPaymentMethods() {
      try {
        const res = await getConfigPaymentMethodsApi()
        // 后端直接返回数组，不需要.list
        this.paymentMethods = res.data || []
        console.log('支付方式列表:', this.paymentMethods)
      } catch (error) {
        console.error('获取支付方式失败:', error)
        // 如果获取失败，使用默认支付方式
        this.paymentMethods = [
          { value: 'wechat', label: '微信支付' },
          { value: 'alipay', label: '支付宝' },
          { value: 'routine', label: '小程序支付' },
          { value: 'balance', label: '余额支付' },
          { value: 'offline', label: '线下支付' }
        ]
      }
    },
    // 重置搜索
    searchReset() {
      // 安全地重置搜索表单
      if (this.$refs.searchForm) {
        try {
          this.$refs.searchForm.resetFields()
        } catch (error) {
          console.warn('搜索表单重置失败，手动重置:', error)
          // 手动重置搜索表单
          this.searchForm = {
            config_type: '',
            payment_type: '',
            keyword: ''
          }
        }
      } else {
        // 如果表单引用不存在，手动重置
        this.searchForm = {
          config_type: '',
          payment_type: '',
          keyword: ''
        }
      }
      this.getList(1)
    },
    // 标签页切换
    handleTabClick() {
      this.tableFrom.page = 1
      // 清空搜索条件，因为不同tab的搜索字段可能不同
      this.searchForm = {
        config_type: '',
        payment_type: '',
        keyword: ''
      }
      this.getList()
    },
    // 获取列表数据
    async getList(num) {
      this.listLoading = true
      this.tableFrom.page = num ? num : this.tableFrom.page
      
      try {
        if (this.activeTab === 'merchant') {
          const res = await getMerchantConfigsApi({
            page: this.tableFrom.page,
            limit: this.tableFrom.limit,
            ...this.searchForm
          })
          this.merchantConfigs = res.data.list
          this.tableData.total = res.data.total
        } else if (this.activeTab === 'gray') {
          const res = await getGrayReleasesApi({
            page: this.tableFrom.page,
            limit: this.tableFrom.limit
          })
          this.grayReleases = res.data.list
          this.tableData.total = res.data.total
        }
      } catch (error) {
        this.$message.error('获取数据失败')
      } finally {
        this.listLoading = false
      }
    },
    // 刷新数据
    refreshData() {
      this.getList()
      this.$message.success('数据已刷新')
    },
    // 分页处理
    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    },
    // 显示新增对话框
    async showAddDialog() {
      this.editMode = false
      this.dialogTitle = '新增配置'
      this.resetForm()
      this.configForm.config_type = this.activeTab

      // 新增时立即获取默认支付类型的模板
      await this.onPaymentTypeChange()

      this.dialogVisible = true
    },
    // 编辑配置
    async editConfig(row) {
      this.editMode = true
      this.dialogTitle = '编辑配置'
      
      try {
        // 根据配置类型获取完整的配置详情
        let configData = { ...row }
        
        if (this.activeTab === 'merchant') {
          const res = await getPaymentConfigApi({
            mer_id: row.mer_id,
            payment_type: row.payment_type
          })

          configData = {
            id: row.id,
            config_type: 'merchant',
            mer_id: row.mer_id,
            merchant_name: row.merchant_name,
            payment_type: row.payment_type,
            config: res.data.config || {},
            status: row.status || 'active'
          }

          // 设置配置模板
          this.configTemplate = res.data.template || {}
        }
        
        this.configForm = configData
        this.dialogVisible = true
        
        // 调试信息
        console.log('编辑配置 - row:', row)
        console.log('编辑配置 - configData:', configData)
        console.log('编辑配置 - editMode:', this.editMode)
        console.log('编辑配置 - 支付方式:', configData.payment_type)
        console.log('编辑配置 - 微信字段:', {
          wechat_private_key: configData.wechat_private_key,
          wechat_public_key: configData.wechat_public_key
        })
        console.log('编辑配置 - 支付宝字段:', {
          alipay_private_key: configData.alipay_private_key,
          alipay_public_key: configData.alipay_public_key
        })
        
      } catch (error) {
        console.error('获取配置详情失败:', error)
        // 如果获取详情失败，使用基本数据
        this.configForm = {
          id: row.id,
          config_type: this.activeTab,
          mer_id: row.mer_id || '',
          merchant_name: row.merchant_name || '未知商户',
          payment_type: row.payment_type || 'wechat',
          config: {},
          status: row.status || 'active'
        }
        this.configTemplate = null
        this.dialogVisible = true
        this.$message.warning('获取配置详情失败，使用基本信息编辑')
      }
    },
    // 查看配置
    viewConfig(row) {
      this.editMode = true
      this.dialogTitle = '查看配置'
      this.configForm = { ...row }
      this.dialogVisible = true
    },
    // 保存配置
    async saveConfig() {
      try {
        await this.$refs.configForm.validate()

        await updatePaymentConfigApi({
          mer_id: this.configForm.mer_id,
          payment_type: this.configForm.payment_type,
          config: this.configForm.config
        })

        this.$message.success('配置保存成功')
        this.dialogVisible = false
        this.getList()
      } catch (error) {
        console.error('配置保存失败:', error)
        this.$message.error('配置保存失败: ' + (error.message || error))
      }
    },
    // 热更新
    hotUpdate(row) {
      this.selectedConfig = row
      this.hotUpdateDialogVisible = true
    },
    // 确认热更新
    async confirmHotUpdate() {
      try {
        const data = {
          config_id: this.selectedConfig.id,
          config_type: this.selectedConfig.config_type || this.activeTab,
          ...this.hotUpdateForm
        }
        await hotUpdateConfigApi(data)
        this.$message.success('配置热更新成功')
        this.hotUpdateDialogVisible = false
        this.getList()
      } catch (error) {
        this.$message.error('配置热更新失败')
      }
    },
    // 删除配置
    async deleteConfig(row) {
      try {
        await this.$confirm('确认删除此配置？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 删除配置API调用
        const configType = row.config_type || this.activeTab
        // 重要：使用配置记录ID，不是merchant_id
        const configId = row.id // 这是eb_merchant_payment_config.id
        
        console.log('删除配置 - configType:', configType)
        console.log('删除配置 - configId:', configId)
        console.log('删除配置 - row:', row)
        
        await deletePaymentConfigApi(configType, configId)
        this.$message.success('配置删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除配置失败:', error)
          this.$message.error('配置删除失败: ' + (error.message || error))
        }
      }
    },
    // 显示灰度发布对话框
    showGrayReleaseDialog() {
      this.$message.info('灰度发布功能开发中')
    },
    // 执行灰度发布
    async executeGrayRelease(row) {
      try {
        await grayReleaseActionApi(row.id, 'execute')
        this.$message.success('灰度发布执行成功')
        this.getList()
      } catch (error) {
        this.$message.error('灰度发布执行失败')
      }
    },
    // 完成灰度发布
    async completeGrayRelease(row) {
      try {
        await grayReleaseActionApi(row.id, 'complete')
        this.$message.success('灰度发布完成')
        this.getList()
      } catch (error) {
        this.$message.error('灰度发布完成失败')
      }
    },
    // 回滚灰度发布
    async rollbackGrayRelease(row) {
      try {
        await this.$confirm('确认回滚此灰度发布？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await grayReleaseActionApi(row.id, 'rollback')
        this.$message.success('灰度发布回滚成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('灰度发布回滚失败')
        }
      }
    },
    // 重置表单
    resetForm() {
      // 重置为简化的表单结构
      this.configForm = {
        id: null,
        config_type: 'merchant',
        mer_id: '',
        payment_type: 'wechat',
        config: {},
        status: 'active'
      }

      // 清空配置模板
      this.configTemplate = null

      // 在下一个tick中安全地重置表单验证状态
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          try {
            this.$refs.configForm.clearValidate()
          } catch (error) {
            console.warn('表单验证状态清除失败:', error)
          }
        }
      })
    },
    // 关闭对话框
    closeDialog() {
      this.dialogVisible = false
      this.resetForm()
    },
    // 时间格式化
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp * 1000).toLocaleString()
    },
    // 获取支付方式名称
    getPaymentTypeName(type) {
      const names = {
        wechat: '微信支付',
        alipay: '支付宝',
        routine: '小程序',
        weixinApp: '微信APP',
        alipayApp: '支付宝APP',
        weixinQr: '微信扫码',
        alipayQr: '支付宝扫码',
        alipayBarCode: '支付宝付款码'
      }
      return names[type] || type
    },
    // 获取配置类型标签
    getConfigTypeTag(type) {
      const tags = {      
        merchant_alipay_auth: 'success',
        merchant_config: 'warning',
        system_config: 'danger'
      }
      return tags[type] || 'default'
    },
    // 获取配置类型名称
    getConfigTypeName(type) {
      const names = {
        merchant_alipay_auth: '商户授权',
        merchant_config: '商户配置',
        system_config: '系统配置'
      }
      return names[type] || type
    },
    // 获取灰度状态标签
    getGrayStatusTag(status) {
      const tags = {
        pending: 'info',
        running: 'warning',
        success: 'success',
        failed: 'danger',
        rollback: 'primary'
      }
      return tags[status] || 'default'
    },
    // 获取灰度状态名称
    getGrayStatusName(status) {
      const names = {
        pending: '待执行',
        running: '进行中',
        success: '成功',
        failed: '失败',
        rollback: '已回滚'
      }
      return names[status] || status
    },
    // 支付方式变化处理
    async onPaymentTypeChange() {
      if (!this.configForm.payment_type) return

      try {
        // 使用专门的模板API获取配置模板
        const res = await getConfigTemplateApi({
          payment_type: this.configForm.payment_type,
          mer_id: this.configForm.mer_id || 0  // 新增时mer_id可能为空，使用0
        })

        this.configTemplate = res.data.template || {}

        // 初始化配置字段，保留已有值
        const newConfig = {}
        Object.keys(this.configTemplate).forEach(fieldName => {
          // 保留已有值，否则使用空字符串
          newConfig[fieldName] = this.configForm.config[fieldName] || ''
        })
        this.configForm.config = newConfig

        console.log('支付类型变化 - 模板:', this.configTemplate)
        console.log('支付类型变化 - 配置:', this.configForm.config)
      } catch (error) {
        console.error('获取配置模板失败:', error)
        this.$message.warning('获取配置模板失败，请重试')
      }
    },

    // 获取商户名称
    getMerchantName(merchantId) {
      const merchant = this.merchantList.find(m => m.mer_id === merchantId)
      return merchant ? merchant.mer_name : '未知商户'
    },
    // 获取商户显示文本
    getMerchantDisplayText() {
      // 优先使用保存在configForm中的商户名称（来自后端JOIN查询）
      const merchantName = this.configForm.merchant_name || '未知商户'
      const configId = this.configForm.id || '新配置'
      return `${merchantName} (配置ID: ${configId})`
    },

    // 获取层级标签类型
    getLayerTagType(layer) {
      const typeMap = {
        'base': 'primary',
        'service': 'warning',
        'merchant': 'success'
      }
      return typeMap[layer] || 'info'
    },

    // 获取层级名称
    getLayerName(layer) {
      const nameMap = {
        'base': '基础配置',
        'service': '服务商配置',
        'merchant': '商户配置'
      }
      return nameMap[layer] || layer
    },

    // 判断是否为敏感字段
    isSecretField(fieldName) {
      const secretFields = [
        // 基础密钥字段
        'key', 'private_key', 'api_v3_key',
        // 服务商密钥字段
        'service_key', 'service_v3_key', 'service_private_key',
        // 支付宝密钥字段
        'service_private_key',
        // Token字段
        'app_auth_token', 'refresh_token'
      ]
      return secretFields.includes(fieldName)
    }
  }
}
</script>

<style scoped lang="scss">
.divBox {
  background-color: #f5f7f9;
  padding: 14px;
}

.selCard {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
}

.selWidth {
  width: 200px;
}

.w100 {
  width: 100%;
}

.text-gray {
  color: #999;
}

.ml10 {
  margin-left: 10px;
}

.fee-rate {
  color: #e6a23c;
  font-weight: bold;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  padding: 5px 0;
}

.button-row {
  display: flex;
  justify-content: center;
  gap: 10px;
  width: 100%;
}

.mt14 {
  margin-top: 14px;
}

.mb20 {
  margin-bottom: 20px;
}

.text-center {
  text-align: center;
}

.field-header {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
}

.required-mark {
  color: #f56c6c;
  font-weight: bold;
}

.template-loading {
  margin: 20px 0;
}

.mb5 {
  margin-bottom: 5px;
}
</style> 