<template>
  <div class="divBox">
    <!-- 筛选卡片 -->
    <div class="selCard">
      <el-form ref="searchForm" :model="searchForm" :inline="true" class="demo-form-inline">
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入商户名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="支付方式" prop="payment_type">
          <el-select v-model="searchForm.payment_type" placeholder="请选择支付方式" clearable style="width: 150px">
            <el-option
              v-for="method in paymentMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList(1)">查询</el-button>
          <el-button @click="searchReset">重置</el-button>
          <el-button type="success" @click="refreshData">刷新</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 主要内容 -->
    <el-card class="mt14">
      <div class="mb20">
        <div class="flex">
          <div class="flex-auto">
            <h3>支付配置管理</h3>
            <p class="text-gray">集成分层配置架构的支付配置管理</p>
          </div>
          <div>
            <el-button size="small" type="primary" icon="el-icon-plus" @click="showAddDialog">
              添加配置
            </el-button>
            <el-button size="small" type="info" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
          </div>
        </div>
      </div>

      <!-- 分层配置布局 -->
      <div class="config-layout">
        <el-row :gutter="20">
          <!-- 左侧：配置列表 -->
          <el-col :span="selectedConfig ? 14 : 24">
            <div class="config-list-panel">
              <h4>配置列表</h4>
              <el-table 
                v-loading="listLoading" 
                :data="merchantConfigs" 
                size="small"
                highlight-current-row
                @current-change="handleConfigSelect"
                style="margin-top: 10px;"
              >
                <el-table-column prop="mer_id" label="商户ID" min-width="100" />
                <el-table-column prop="merchant_name" label="商户名称" min-width="150">
                  <template slot-scope="scope">
                    {{ scope.row.merchant_name || '未知商户' }}
                  </template>
                </el-table-column>
                <el-table-column prop="payment_type" label="支付方式" min-width="100">
                  <template slot-scope="scope">
                    <el-tag size="small">{{ getPaymentTypeName(scope.row.payment_type) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" min-width="80">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'" size="small">
                      {{ scope.row.status === 'active' ? '激活' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="updated_at" label="更新时间" min-width="140">
                  <template slot-scope="scope">
                    {{ formatTime(scope.row.updated_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="120" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="success" size="mini" icon="el-icon-edit" @click="editConfig(scope.row)">编辑</el-button>
                    <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteConfig(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <!-- 分页 -->
              <div class="block mt20">
                <el-pagination
                  background
                  :page-size="tableFrom.limit"
                  :current-page="tableFrom.page"
                  layout="total, prev, pager, next, jumper"
                  :total="tableData.total"
                  @size-change="handleSizeChange"
                  @current-change="pageChange"
                />
              </div>
            </div>
          </el-col>
          
          <!-- 右侧：分层配置详情 -->
          <el-col :span="10" v-if="selectedConfig">
            <div class="config-detail-panel">
              <LayeredConfigDetail 
                :config-data="selectedConfig"
                @config-updated="handleConfigUpdated"
                @add-config="showAddDialog"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 配置编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="closeDialog"
    >
      <el-form :model="configForm" :rules="configRules" ref="configForm" label-width="120px">
        <el-form-item label="商户ID" prop="mer_id">
          <el-input v-model="configForm.mer_id" :disabled="editMode" placeholder="请输入商户ID" />
        </el-form-item>
        
        <el-form-item label="支付类型" prop="payment_type">
          <el-select v-model="configForm.payment_type" :disabled="editMode" class="w100" @change="onPaymentTypeChange">
            <el-option
              v-for="method in paymentMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
        </el-form-item>

        <!-- 动态配置字段 -->
        <div v-if="configTemplate">
          <div v-for="(fieldDef, fieldName) in configTemplate" :key="fieldName">
            <el-form-item 
              :label="fieldDef.description" 
              :prop="fieldName"
              :required="fieldDef.required"
            >
              <el-tag v-if="fieldDef.layer" size="mini" :type="getLayerTagType(fieldDef.layer)" class="mb5">
                {{ getLayerName(fieldDef.layer) }}
              </el-tag>
              
              <el-input
                v-if="fieldDef.type === 'string'"
                v-model="configForm.config[fieldName]"
                :placeholder="`请输入${fieldDef.description}`"
                :type="isSecretField(fieldName) ? 'password' : 'text'"
                :show-password="isSecretField(fieldName)"
              />
              
              <el-input
                v-else-if="fieldDef.type === 'text'"
                v-model="configForm.config[fieldName]"
                type="textarea"
                :rows="3"
                :placeholder="`请输入${fieldDef.description}`"
              />
              
              <el-switch
                v-else-if="fieldDef.type === 'boolean'"
                v-model="configForm.config[fieldName]"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
      
      <div slot="footer" class="text-center">
        <el-button icon="el-icon-close" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="saveConfig">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getMerchantConfigsApi,
  getPaymentConfigApi,
  updatePaymentConfigApi,
  deletePaymentConfigApi,
  getConfigPaymentMethodsApi
} from '@/api/payment'
import LayeredConfigDetail from '@/components/payment/LayeredConfigDetail'

export default {
  name: 'IntegratedPaymentConfig',
  components: {
    LayeredConfigDetail
  },
  data() {
    return {
      listLoading: false,
      searchForm: {
        payment_type: '',
        keyword: ''
      },
      merchantConfigs: [],
      paymentMethods: [],
      selectedConfig: null,
      configTemplate: null,
      tableData: {
        data: [],
        total: 0
      },
      tableFrom: {
        page: 1,
        limit: 20
      },
      dialogVisible: false,
      editMode: false,
      dialogTitle: '新增配置',
      configForm: {
        mer_id: '',
        payment_type: '',
        config: {}
      },
      configRules: {
        mer_id: [
          { required: true, message: '请输入商户ID', trigger: 'blur' }
        ],
        payment_type: [
          { required: true, message: '请选择支付类型', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.getPaymentMethods()
    this.getList(1)
  },
  methods: {
    // 获取支付方式列表
    async getPaymentMethods() {
      try {
        const res = await getConfigPaymentMethodsApi()
        this.paymentMethods = res.data || []
      } catch (error) {
        console.error('获取支付方式失败:', error)
      }
    },

    // 获取配置列表
    async getList(page = 1) {
      this.listLoading = true
      try {
        const params = {
          page,
          limit: this.tableFrom.limit,
          ...this.searchForm
        }
        
        const res = await getMerchantConfigsApi(params)
        this.merchantConfigs = res.data.list || []
        this.tableData.total = res.data.total || 0
        this.tableFrom.page = page
      } catch (error) {
        this.$message.error('获取配置列表失败: ' + error.message)
      } finally {
        this.listLoading = false
      }
    },

    // 选择配置
    handleConfigSelect(row) {
      this.selectedConfig = row ? {
        mer_id: row.mer_id,
        payment_type: row.payment_type
      } : null
    },

    // 配置更新回调
    handleConfigUpdated(data) {
      this.getList(this.tableFrom.page)
    },

    // 显示新增对话框
    showAddDialog() {
      this.editMode = false
      this.dialogTitle = '新增配置'
      this.configForm = {
        mer_id: '',
        payment_type: '',
        config: {}
      }
      this.configTemplate = null
      this.dialogVisible = true
    },

    // 编辑配置
    async editConfig(row) {
      try {
        const res = await getPaymentConfigApi({
          mer_id: row.mer_id,
          payment_type: row.payment_type
        })
        
        this.editMode = true
        this.dialogTitle = '编辑配置'
        this.configForm = {
          mer_id: row.mer_id,
          payment_type: row.payment_type,
          config: res.data.config || {}
        }
        this.configTemplate = res.data.template || {}
        this.dialogVisible = true
      } catch (error) {
        this.$message.error('获取配置失败: ' + error.message)
      }
    },

    // 删除配置
    deleteConfig(row) {
      this.$confirm('确定要删除这个配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deletePaymentConfigApi({
            mer_id: row.mer_id,
            payment_type: row.payment_type
          })
          this.$message.success('删除成功')
          this.getList(this.tableFrom.page)
        } catch (error) {
          this.$message.error('删除失败: ' + error.message)
        }
      })
    },

    // 支付类型变化
    async onPaymentTypeChange() {
      if (!this.configForm.payment_type) return

      try {
        const res = await getPaymentConfigApi({
          mer_id: this.configForm.mer_id || 0,
          payment_type: this.configForm.payment_type
        })
        
        this.configTemplate = res.data.template
        
        // 初始化配置字段
        this.configForm.config = {}
        Object.keys(this.configTemplate).forEach(fieldName => {
          this.configForm.config[fieldName] = ''
        })
      } catch (error) {
        console.error('获取配置模板失败:', error)
      }
    },

    // 保存配置
    async saveConfig() {
      try {
        await this.$refs.configForm.validate()
        
        await updatePaymentConfigApi({
          mer_id: this.configForm.mer_id,
          payment_type: this.configForm.payment_type,
          config: this.configForm.config
        })
        
        this.$message.success('配置保存成功')
        this.dialogVisible = false
        this.getList(this.tableFrom.page)
      } catch (error) {
        this.$message.error('保存配置失败: ' + error.message)
      }
    },

    // 关闭对话框
    closeDialog() {
      this.$refs.configForm.resetFields()
      this.configForm.config = {}
      this.configTemplate = null
    },

    // 重置搜索
    searchReset() {
      this.searchForm = {
        payment_type: '',
        keyword: ''
      }
      this.getList(1)
    },

    // 刷新数据
    refreshData() {
      this.getList(this.tableFrom.page)
    },

    // 分页处理
    pageChange(page) {
      this.getList(page)
    },

    handleSizeChange(size) {
      this.tableFrom.limit = size
      this.getList(1)
    },

    // 工具方法
    getPaymentTypeName(type) {
      const method = this.paymentMethods.find(m => m.value === type)
      return method ? method.label : type
    },

    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp * 1000).toLocaleString()
    },

    getLayerTagType(layer) {
      const typeMap = {
        'base': 'primary',
        'service': 'warning',
        'merchant': 'success'
      }
      return typeMap[layer] || 'info'
    },

    getLayerName(layer) {
      const nameMap = {
        'base': '基础配置',
        'service': '服务商配置',
        'merchant': '商户配置'
      }
      return nameMap[layer] || layer
    },

    isSecretField(fieldName) {
      const secretFields = ['key', 'private_key', 'api_v3_key', 'service_key', 'service_v3_key']
      return secretFields.includes(fieldName)
    }
  }
}
</script>

<style scoped>
.config-layout {
  min-height: 600px;
}

.config-list-panel {
  border-right: 1px solid #e6e6e6;
  padding-right: 20px;
}

.config-detail-panel {
  padding-left: 20px;
}

.mb5 {
  margin-bottom: 5px;
}

.mt20 {
  margin-top: 20px;
}

.w100 {
  width: 100%;
}

.text-gray {
  color: #909399;
}

.flex {
  display: flex;
  align-items: center;
}

.flex-auto {
  flex: 1;
}
</style>
