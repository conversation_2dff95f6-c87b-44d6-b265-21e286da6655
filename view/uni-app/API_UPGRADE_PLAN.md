# UNIAPP微信小程序API升级方案

## 修复方案总览

### 修复统计
- **需要修复的文件**: 12个
- **需要替换的API调用**: 约25处
- **预计修复时间**: 3-5个工作日
- **风险等级**: 低-中等

## 具体修复方案

### 1. 剪贴板API修复 (紧急)

#### 影响文件
- `pages/admin/refundDetail/index.vue` (第80-90行)
- `pages/admin/orderDetail/index.vue` (第80-90行)

#### 修复代码
```javascript
// 原代码 (需要替换)
copyOrderNumber(order_sn) {
    wx.setClipboardData({
        data: order_sn,
        success: function(res) {
            wx.getClipboardData({
                success: function(res) {
                    wx.showToast({
                        title: '复制成功',
                        icon: 'success'
                    });
                }
            });
        }
    });
}

// 修复后代码
copyOrderNumber(order_sn) {
    uni.setClipboardData({
        data: order_sn,
        success: function(res) {
            uni.showToast({
                title: '复制成功',
                icon: 'success'
            });
        },
        fail: function(err) {
            uni.showToast({
                title: '复制失败',
                icon: 'none'
            });
        }
    });
}
```

### 2. 登录API修复 (重要)

#### 影响文件
- `pages/users/login/index.vue` (第271行)
- `pages/users/user_phone/index.vue`
- `pages/users/user_modify_phone/index.vue`
- `pages/users/user_modify_pwd/index.vue`

#### 修复代码
```javascript
// 原代码 (需要替换)
wx.login({
    success(res) {
        if (res.code) {
            that.codeVal = res.code
        } else {
            console.log('登录失败！' + res.errMsg)
        }
    }
});

// 修复后代码
uni.login({
    provider: 'weixin',
    success(res) {
        if (res.code) {
            that.codeVal = res.code
        } else {
            console.log('登录失败！' + res.errMsg)
        }
    },
    fail(err) {
        console.log('登录失败！', err)
    }
});
```

### 3. 扫码API修复

#### 影响文件
- `pages/admin/order_cancellation/index.vue`

#### 修复代码
```javascript
// 原代码 (需要替换)
wx.scanCode({
    success: (res) => {
        // 处理扫码结果
        console.log(res.result);
    }
});

// 修复后代码
uni.scanCode({
    success: (res) => {
        // 处理扫码结果
        console.log(res.result);
    },
    fail: (err) => {
        uni.showToast({
            title: '扫码失败',
            icon: 'none'
        });
    }
});
```

### 4. 图片预览API修复

#### 影响文件
- `pages/users/feedback/detail.vue`

#### 修复代码
```javascript
// 原代码 (需要替换)
wx.previewImage({
    current: url,
    urls: [url]
});

// 修复后代码
uni.previewImage({
    current: url,
    urls: [url],
    fail: (err) => {
        uni.showToast({
            title: '预览失败',
            icon: 'none'
        });
    }
});
```

### 5. 分享菜单API处理 (条件编译)

#### 影响文件
- `pages/columnGoods/HotNewGoods/index.vue`
- `pages/points_mall/goods_selection.vue`
- `pages/store/home/<USER>
- `pages/store/index.vue`
- `pages/goods_cate/goods_cate.vue`

#### 修复代码
```javascript
// 原代码 (需要条件编译处理)
wx.showShareMenu({
    withShareTicket: true
});

// 修复后代码 (使用条件编译)
// #ifdef MP-WEIXIN
wx.showShareMenu({
    withShareTicket: true
});
// #endif

// #ifdef H5
// H5平台的分享处理
// #endif

// #ifdef APP-PLUS
// APP平台的分享处理
// #endif
```

## 批量修复脚本

### 创建自动化修复脚本
```bash
#!/bin/bash
# api_fix_script.sh

echo "开始修复UNIAPP项目中的已弃用API..."

# 1. 修复剪贴板API
echo "修复剪贴板API..."
find . -name "*.vue" -type f -exec sed -i 's/wx\.setClipboardData/uni.setClipboardData/g' {} \;
find . -name "*.vue" -type f -exec sed -i 's/wx\.getClipboardData/uni.getClipboardData/g' {} \;

# 2. 修复Toast API
echo "修复Toast API..."
find . -name "*.vue" -type f -exec sed -i 's/wx\.showToast/uni.showToast/g' {} \;

# 3. 修复扫码API
echo "修复扫码API..."
find . -name "*.vue" -type f -exec sed -i 's/wx\.scanCode/uni.scanCode/g' {} \;

# 4. 修复图片预览API
echo "修复图片预览API..."
find . -name "*.vue" -type f -exec sed -i 's/wx\.previewImage/uni.previewImage/g' {} \;

echo "API修复完成！请手动检查登录API的修复。"
```

## 测试验证方案

### 1. 功能测试清单
- [ ] 复制订单号功能测试
- [ ] 用户登录功能测试
- [ ] 扫码功能测试
- [ ] 图片预览功能测试
- [ ] 分享功能测试

### 2. 平台兼容性测试
- [ ] 微信小程序测试
- [ ] H5页面测试
- [ ] APP测试 (如果支持)

### 3. 回归测试
- [ ] 核心业务流程测试
- [ ] 用户体验测试
- [ ] 性能测试

## 风险评估与应对

### 潜在风险
1. **API行为差异**: uni.API与wx.API可能存在细微差异
2. **平台兼容性**: 不同平台的API支持程度不同
3. **用户体验**: 修复过程中可能影响用户体验

### 应对措施
1. **分阶段发布**: 先在测试环境验证，再逐步发布
2. **回滚方案**: 准备快速回滚机制
3. **监控告警**: 加强错误监控和用户反馈收集

## 修复时间表

### 第1天: 紧急修复
- 修复剪贴板API (2小时)
- 修复Toast API (1小时)
- 基础功能测试 (2小时)

### 第2-3天: 重要修复
- 修复登录API (4小时)
- 修复扫码和图片预览API (2小时)
- 全面功能测试 (4小时)

### 第4-5天: 优化和验证
- 条件编译优化 (3小时)
- 平台兼容性测试 (4小时)
- 文档更新 (1小时)

## 后续维护建议

1. **代码规范**: 制定wx.API使用规范，禁止直接使用已弃用API
2. **自动检测**: 集成ESLint规则，自动检测已弃用API使用
3. **定期审查**: 定期审查代码中的API使用情况
4. **文档维护**: 及时更新开发文档和最佳实践
