# UNIAPP项目梳理报告

## 项目概述

### 基本信息
- **项目名称**: MULTI-SHOPV2 (CRMEB多商户系统)
- **版本**: 1.0.4 (versionCode: 127)
- **框架**: uni-app 2.x
- **Vue版本**: 2.6.14
- **编译器版本**: 3

### 项目结构分析

```
view/uni-app/
├── api/                    # API接口层
├── components/             # 公共组件
├── config/                 # 配置文件
├── libs/                   # 工具库
├── mixins/                 # 混入
├── pages/                  # 页面文件
├── plugin/                 # 插件
├── static/                 # 静态资源
├── store/                  # Vuex状态管理
├── uni_modules/            # uni-app插件模块
├── utils/                  # 工具函数
├── App.vue                 # 应用入口
├── main.js                 # 主入口文件
├── manifest.json           # 应用配置
├── pages.json              # 页面路由配置
└── package.json            # 依赖配置
```

### 平台支持
- **微信小程序**: wx5fb1cc8edb3f8baa
- **支付宝小程序**: 2021005152694690
- **百度小程序**: 支持
- **字节跳动小程序**: 支持
- **H5**: 支持
- **APP**: 支持 (Android/iOS)

### 核心依赖分析

#### 主要依赖
```json
{
  "@dcloudio/uni-app": "^2.0.2-4050620250311002",
  "@dcloudio/uni-h5": "^2.0.2-4050620250311002", 
  "@dcloudio/uni-mp-alipay": "^2.0.2-4050620250311002",
  "@dcloudio/uni-mp-weixin": "^2.0.2-4050620250311002",
  "@dcloudio/uni-stat": "^2.0.2-4050620250311002",
  "@vue/composition-api": "^1.7.2",
  "vue": "^2.6.14",
  "vue-loader": "^15.9.8",
  "vue-template-compiler": "^2.6.14"
}
```

#### 开发依赖
```json
{
  "uni-simple-router": "^2.0.8-beta.4"
}
```

### 配置文件分析

#### manifest.json 关键配置
- **编译器版本**: 3 (使用新版编译器)
- **微信小程序配置**:
  - 开启隐私检查: `__usePrivacyCheck__: true`
  - 必需隐私信息: `["getLocation", "chooseAddress"]`
  - 分包优化: `subPackages: true`
- **权限配置**: 位置权限 `scope.userLocation`

## 已识别的问题

### 1. 编译错误修复历史
根据 `UNI_APP_COMPILE_FIXES.md` 文档，项目已经修复了以下问题：
- ✅ 缺失文件问题 (chat.js, calcTextareaHeight.js, utils.js)
- ✅ dayjs依赖安装
- ✅ :key表达式兼容性问题 (已批量修复309个文件)

### 2. 当前存在的潜在问题
- uni-app版本较旧 (2.x)，建议升级到3.x
- Vue 2.x，可考虑升级到Vue 3
- 部分依赖版本较旧

## 微信小程序已弃用API使用情况

### 直接使用wx.API的文件统计
通过代码扫描发现以下文件直接使用了wx.开头的原生API：

#### 1. 登录相关API
- `pages/users/login/index.vue`: `wx.login()`
- `pages/users/user_phone/index.vue`: `wx.login()`
- `pages/users/user_modify_phone/index.vue`: `wx.login()`
- `pages/users/user_modify_pwd/index.vue`: `wx.login()`

#### 2. 用户信息相关API
- `pages/users/wechat_login/index.vue`: 
  - `wx.getPrivacySetting()` 
  - `wx.getUserProfile()`

#### 3. 隐私协议相关API
- `pages/users/components/privacyAgreementPopup/index.vue`: `wx.getPrivacySetting()`

#### 4. 界面相关API
- `utils/util.js`: `wx.getMenuButtonBoundingClientRect()`
- 多个页面: `wx.showShareMenu()`

#### 5. 系统功能API
- `pages/admin/refundDetail/index.vue`: `wx.setClipboardData()`, `wx.getClipboardData()`, `wx.showToast()`
- `pages/admin/orderDetail/index.vue`: `wx.setClipboardData()`, `wx.getClipboardData()`, `wx.showToast()`
- `pages/admin/order_cancellation/index.vue`: `wx.scanCode()`
- `pages/users/feedback/detail.vue`: `wx.previewImage()`

#### 6. 支付相关API
- `pages/users/user_spread_money/receiving.vue`: `wx.requestMerchantTransfer()`

#### 7. 文件系统API
- `plugin/image-tools/index.js`: `wx.getFileSystemManager()`, `wx.canIUse()`

### 风险等级评估

#### 🔴 高风险 (已弃用或即将弃用)
1. **wx.getUserInfo()** - 已在基础库2.21.0后弃用
2. **wx.showToast()** - 建议使用uni.showToast()
3. **wx.showModal()** - 建议使用uni.showModal()
4. **wx.setClipboardData()** - 建议使用uni.setClipboardData()
5. **wx.getClipboardData()** - 建议使用uni.getClipboardData()

#### 🟡 中风险 (需要注意兼容性)
1. **wx.login()** - 建议使用uni.login()
2. **wx.scanCode()** - 建议使用uni.scanCode()
3. **wx.previewImage()** - 建议使用uni.previewImage()

#### 🟢 低风险 (暂时安全)
1. **wx.getPrivacySetting()** - 隐私协议相关，暂时必需
2. **wx.getUserProfile()** - 用户授权相关，暂时必需
3. **wx.getMenuButtonBoundingClientRect()** - 胶囊按钮相关，暂时必需
4. **wx.showShareMenu()** - 分享功能，暂时必需

## 修复建议优先级

### 第一优先级 (立即修复)
1. 替换所有wx.showToast()为uni.showToast()
2. 替换所有wx.setClipboardData()为uni.setClipboardData()
3. 替换所有wx.getClipboardData()为uni.getClipboardData()

### 第二优先级 (近期修复)
1. 替换wx.login()为uni.login()
2. 替换wx.scanCode()为uni.scanCode()
3. 替换wx.previewImage()为uni.previewImage()

### 第三优先级 (长期规划)
1. 升级uni-app到3.x版本
2. 升级Vue到3.x版本
3. 优化项目结构和代码规范

## 详细的已弃用API分析

### 具体文件中的问题API

#### 1. 剪贴板API (高风险)
**文件**: `pages/admin/refundDetail/index.vue`, `pages/admin/orderDetail/index.vue`
```javascript
// ❌ 已弃用写法
wx.setClipboardData({
    data: value,
    success: function(res) {
        wx.getClipboardData({
            success: function(res) {
                wx.showToast({
                    title: '复制成功',
                    icon: 'success'
                });
            }
        });
    }
});

// ✅ 推荐写法
uni.setClipboardData({
    data: value,
    success: function(res) {
        uni.showToast({
            title: '复制成功',
            icon: 'success'
        });
    }
});
```

#### 2. 登录API (中风险)
**文件**: `pages/users/login/index.vue`, `pages/users/user_phone/index.vue` 等
```javascript
// ❌ 已弃用写法
wx.login({
    success(res) {
        if (res.code) {
            that.codeVal = res.code
        } else {
            console.log('登录失败！' + res.errMsg)
        }
    }
});

// ✅ 推荐写法
uni.login({
    provider: 'weixin',
    success(res) {
        if (res.code) {
            that.codeVal = res.code
        } else {
            console.log('登录失败！' + res.errMsg)
        }
    }
});
```

#### 3. 扫码API (中风险)
**文件**: `pages/admin/order_cancellation/index.vue`
```javascript
// ❌ 已弃用写法
wx.scanCode({
    success: (res) => {
        // 处理扫码结果
    }
});

// ✅ 推荐写法
uni.scanCode({
    success: (res) => {
        // 处理扫码结果
    }
});
```

#### 4. 图片预览API (中风险)
**文件**: `pages/users/feedback/detail.vue`
```javascript
// ❌ 已弃用写法
wx.previewImage({
    current: url,
    urls: [url]
});

// ✅ 推荐写法
uni.previewImage({
    current: url,
    urls: [url]
});
```

### 特殊情况分析

#### 1. 必须保留的wx.API
以下API由于uni-app暂未提供对应封装，需要保留：
- `wx.getPrivacySetting()` - 隐私协议检查
- `wx.getUserProfile()` - 用户信息授权
- `wx.getMenuButtonBoundingClientRect()` - 胶囊按钮信息
- `wx.showShareMenu()` - 分享菜单
- `wx.requestMerchantTransfer()` - 商户转账

#### 2. 条件编译处理
对于必须使用wx.API的情况，建议使用条件编译：
```javascript
// #ifdef MP-WEIXIN
wx.getPrivacySetting({
    success: res => {
        // 微信小程序特有逻辑
    }
});
// #endif

// #ifndef MP-WEIXIN
// 其他平台的替代方案
// #endif
```

## 修复优先级和时间规划

### 🔴 紧急修复 (1-2天内完成)
1. **剪贴板API替换** - 影响2个文件
   - `pages/admin/refundDetail/index.vue`
   - `pages/admin/orderDetail/index.vue`

2. **Toast提示API替换** - 影响多个文件
   - 所有使用`wx.showToast()`的地方

### 🟡 重要修复 (1周内完成)
1. **登录API替换** - 影响4个文件
   - `pages/users/login/index.vue`
   - `pages/users/user_phone/index.vue`
   - `pages/users/user_modify_phone/index.vue`
   - `pages/users/user_modify_pwd/index.vue`

2. **扫码和图片预览API替换** - 影响2个文件
   - `pages/admin/order_cancellation/index.vue`
   - `pages/users/feedback/detail.vue`

### 🟢 一般修复 (2周内完成)
1. **代码规范统一** - 制定wx.API使用规范
2. **条件编译优化** - 优化平台兼容性代码
3. **文档更新** - 更新开发文档和规范

## 下一步行动计划

1. **立即行动**: 修复高风险API调用
2. **短期计划**: 统一使用uni.API替代wx.API
3. **中期计划**: 升级框架版本
4. **长期计划**: 代码重构和优化
