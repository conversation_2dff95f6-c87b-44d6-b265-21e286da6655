# uni-app 2.x → 3.x & Vue 2 → 3 分阶段升级实施计划

## 📋 项目升级总体规划

### 升级目标
- **框架升级**: uni-app 2.x → 3.x
- **Vue版本**: Vue 2.6.14 → Vue 3.x
- **构建工具**: webpack → Vite
- **开发体验**: 引入TypeScript、Composition API
- **性能提升**: 构建速度提升3-5倍，运行时性能提升20-30%

### 升级周期
- **总工期**: 8-10周
- **团队规模**: 2-3名开发人员
- **测试周期**: 每阶段2-3天测试验证
- **发布策略**: 灰度发布，逐步全量

## 🗓️ 详细分阶段实施计划

### 第一阶段：环境准备与依赖升级 (第1-2周)

#### 1.1 开发环境准备 (3天)
**目标**: 搭建Vue3/uni-app3开发环境

**具体任务**:
- [ ] 创建升级分支 `feature/vue3-upgrade`
- [ ] 安装HBuilderX最新版本 (支持uni-app3)
- [ ] 配置Node.js 16+ 环境
- [ ] 安装Vue3开发工具

**验收标准**:
- 开发环境能正常运行uni-app3项目
- Vue DevTools 6.x正常工作

#### 1.2 依赖包升级 (4天)
**目标**: 升级所有核心依赖到Vue3兼容版本

**package.json升级清单**:
```json
{
  "dependencies": {
    "@dcloudio/uni-app": "^3.0.0-alpha-3081220230802001",
    "@dcloudio/uni-h5": "^3.0.0-alpha-3081220230802001",
    "@dcloudio/uni-mp-weixin": "^3.0.0-alpha-3081220230802001",
    "vue": "^3.3.4",
    "vuex": "^4.1.0",
    "@vue/composition-api": "移除",
    "vue-loader": "移除",
    "vue-template-compiler": "移除"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.2.3",
    "vite": "^4.4.0",
    "typescript": "^5.1.6",
    "@types/node": "^20.4.5"
  }
}
```

**具体任务**:
- [ ] 升级uni-app相关包
- [ ] 升级Vue到3.x
- [ ] 升级Vuex到4.x
- [ ] 移除Vue2特有依赖
- [ ] 安装Vite和TypeScript
- [ ] 解决依赖冲突

**验收标准**:
- `npm install` 无错误
- 项目能正常启动(即使有运行时错误)

#### 1.3 构建配置迁移 (3天)
**目标**: 从webpack配置迁移到Vite配置

**创建vite.config.js**:
```javascript
import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [uni()],
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  },
  optimizeDeps: {
    include: ['dayjs']
  }
})
```

**具体任务**:
- [ ] 创建vite.config.js
- [ ] 迁移环境变量配置
- [ ] 配置路径别名
- [ ] 配置代理设置

**验收标准**:
- Vite开发服务器能正常启动
- 热重载功能正常

### 第二阶段：核心API迁移 (第3-4周)

#### 2.1 全局API迁移 (5天)
**目标**: 迁移所有Vue2全局API到Vue3

**main.js重构**:
```javascript
// 重构前后对比
// ❌ Vue2写法
import Vue from 'vue'
Vue.use(Vuex)
Vue.component('skeleton', skeleton)
Vue.prototype.$util = util

// ✅ Vue3写法
import { createApp } from 'vue'
const app = createApp(App)
app.use(store)
app.component('skeleton', skeleton)
app.config.globalProperties.$util = util
```

**具体任务**:
- [ ] 重构main.js入口文件
- [ ] 迁移Vue.use()调用
- [ ] 迁移Vue.component()注册
- [ ] 迁移Vue.prototype扩展
- [ ] 迁移Vue.directive()指令
- [ ] 迁移Vue.mixin()混入

**验收标准**:
- 应用能正常启动
- 全局组件和指令正常工作
- 原型链方法可正常访问

#### 2.2 Vuex 4.x迁移 (3天)
**目标**: 升级Vuex到4.x版本

**store/index.js重构**:
```javascript
// ❌ Vuex 3.x
import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
export default new Vuex.Store({...})

// ✅ Vuex 4.x
import { createStore } from 'vuex'
export default createStore({...})
```

**具体任务**:
- [ ] 重构store创建方式
- [ ] 更新组件中store使用
- [ ] 测试所有store功能
- [ ] 验证状态持久化

**验收标准**:
- 所有Vuex功能正常
- 状态管理无异常

#### 2.3 响应式系统迁移 (4天)
**目标**: 移除$set/$delete，利用Vue3响应式优势

**批量替换脚本**:
```bash
# 创建自动替换脚本
#!/bin/bash
echo "开始替换$set和$delete..."
find . -name "*.vue" -type f -exec sed -i 's/this\.\$set(\([^,]*\),\s*\([^,]*\),\s*\([^)]*\))/\1[\2] = \3/g' {} \;
find . -name "*.vue" -type f -exec sed -i 's/this\.\$delete(\([^,]*\),\s*\([^)]*\))/delete \1[\2]/g' {} \;
```

**具体任务**:
- [ ] 扫描所有$set使用位置
- [ ] 批量替换$set为直接赋值
- [ ] 扫描所有$delete使用位置
- [ ] 批量替换$delete为delete操作
- [ ] 测试响应式更新

**验收标准**:
- 无$set/$delete调用
- 响应式更新正常工作

### 第三阶段：事件系统重构 (第5-6周)

#### 3.1 事件总线替换 (6天)
**目标**: 使用mitt替换Vue实例事件总线

**安装mitt库**:
```bash
npm install mitt
```

**创建事件总线**:
```javascript
// utils/eventBus.js
import mitt from 'mitt'
export const eventBus = mitt()
```

**具体任务**:
- [ ] 安装mitt事件库
- [ ] 创建全局事件总线
- [ ] 替换$eventHub使用
- [ ] 替换uni.$emit使用
- [ ] 更新组件间通信
- [ ] 测试事件传递

**验收标准**:
- 所有组件间通信正常
- 事件监听和移除正常

#### 3.2 组件通信优化 (4天)
**目标**: 优化父子组件通信方式

**v-model升级**:
```javascript
// ❌ Vue2写法
// 父组件: <child :value="val" @input="val = $event" />
// 子组件: this.$emit('input', newValue)

// ✅ Vue3写法
// 父组件: <child v-model="val" />
// 子组件: this.$emit('update:modelValue', newValue)
```

**具体任务**:
- [ ] 更新自定义组件v-model
- [ ] 移除.sync修饰符使用
- [ ] 优化props和emits定义
- [ ] 测试组件通信

**验收标准**:
- 父子组件通信正常
- v-model双向绑定正常

### 第四阶段：组件和页面重构 (第7-8周)

#### 4.1 核心组件重构 (7天)
**目标**: 重构关键组件使用Composition API

**重构优先级**:
1. **登录组件** - `pages/users/login/index.vue`
2. **商品搜索** - `pages/columnGoods/goods_search_con/index.vue`
3. **图片懒加载** - `components/easy-loadimage/easy-loadimage.vue`
4. **购物车** - 相关组件

**具体任务**:
- [ ] 创建useLogin组合函数
- [ ] 创建useVerifyCode组合函数
- [ ] 创建useProductSearch组合函数
- [ ] 重构核心业务组件
- [ ] 测试组件功能

**验收标准**:
- 核心功能正常工作
- 代码逻辑更清晰
- 性能有所提升

#### 4.2 生命周期钩子更新 (3天)
**目标**: 更新已变更的生命周期钩子

**生命周期映射**:
```javascript
// Vue2 → Vue3
beforeDestroy → beforeUnmount
destroyed → unmounted
```

**具体任务**:
- [ ] 扫描beforeDestroy使用
- [ ] 替换为beforeUnmount
- [ ] 扫描destroyed使用
- [ ] 替换为unmounted
- [ ] 测试生命周期

**验收标准**:
- 生命周期钩子正常执行
- 组件销毁逻辑正常

### 第五阶段：新特性应用与优化 (第9-10周)

#### 5.1 TypeScript集成 (5天)
**目标**: 引入TypeScript提升开发体验

**配置TypeScript**:
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules"]
}
```

**具体任务**:
- [ ] 配置TypeScript环境
- [ ] 创建类型定义文件
- [ ] 重构核心API类型
- [ ] 添加组件props类型
- [ ] 配置IDE类型检查

**验收标准**:
- TypeScript编译无错误
- IDE提供完整类型提示

#### 5.2 性能优化 (5天)
**目标**: 应用Vue3新特性优化性能

**优化项目**:
- [ ] 使用Suspense优化异步组件
- [ ] 使用Fragment减少DOM层级
- [ ] 使用Teleport优化弹窗
- [ ] 配置代码分割
- [ ] 优化包体积

**具体任务**:
- [ ] 重构异步组件加载
- [ ] 优化弹窗组件结构
- [ ] 配置Vite代码分割
- [ ] 分析包体积变化
- [ ] 性能测试对比

**验收标准**:
- 构建速度提升3倍以上
- 包体积减少20%以上
- 运行时性能提升

## 🧪 测试验证计划

### 功能测试矩阵

| 功能模块 | 测试重点 | 验收标准 |
|---------|---------|----------|
| 用户登录 | 登录流程、状态管理 | 登录成功，状态正确 |
| 商品搜索 | 搜索逻辑、列表渲染 | 搜索结果正确显示 |
| 购物车 | 添加删除、数量计算 | 购物车功能正常 |
| 订单流程 | 下单支付、状态流转 | 订单流程完整 |
| 个人中心 | 信息展示、设置修改 | 用户信息正确 |

### 兼容性测试

| 平台 | 测试版本 | 测试重点 |
|------|---------|----------|
| 微信小程序 | 最新版本 | 核心功能、性能 |
| 支付宝小程序 | 最新版本 | 基础功能 |
| H5 | Chrome/Safari | 响应式、兼容性 |
| APP | Android/iOS | 原生功能集成 |

## ⚠️ 风险控制方案

### 高风险项识别

1. **事件总线重构风险**
   - **风险**: 组件间通信中断
   - **缓解**: 分步骤替换，保留备用方案
   - **回滚**: 快速恢复Vue2事件总线

2. **第三方插件兼容性**
   - **风险**: 插件不支持Vue3
   - **缓解**: 提前验证插件兼容性
   - **备选**: 寻找Vue3兼容替代方案

3. **性能回归风险**
   - **风险**: 升级后性能下降
   - **缓解**: 每阶段性能测试
   - **监控**: 建立性能监控体系

### 回滚策略

1. **代码回滚**
   - 保持Vue2分支完整
   - 每阶段创建检查点
   - 快速切换机制

2. **数据兼容**
   - 确保数据结构兼容
   - 状态管理向下兼容
   - 缓存数据处理

3. **发布回滚**
   - 灰度发布策略
   - 快速回滚机制
   - 用户影响最小化

## 📊 成功指标

### 技术指标
- [ ] 构建速度提升 > 300%
- [ ] 包体积减少 > 20%
- [ ] 热重载速度 < 100ms
- [ ] TypeScript覆盖率 > 80%

### 业务指标
- [ ] 功能完整性 100%
- [ ] 性能无回归
- [ ] 用户体验提升
- [ ] 开发效率提升 > 30%

### 质量指标
- [ ] 代码质量提升
- [ ] 可维护性增强
- [ ] 测试覆盖率 > 85%
- [ ] 文档完整性 100%
