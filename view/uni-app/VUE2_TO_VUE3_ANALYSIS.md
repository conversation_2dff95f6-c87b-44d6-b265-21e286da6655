# Vue2 到 Vue3 升级兼容性分析报告

## 📊 当前项目Vue2特性使用情况统计

### 1. Vue2全局API使用情况

#### 在main.js中发现的Vue2全局API
```javascript
// ❌ Vue2写法 - 需要迁移
Vue.use(Vuex);                           // Vuex注册
Vue.component('skeleton', skeleton);      // 全局组件注册
Vue.component('priceFormat', PriceFormat); // 全局组件注册
Vue.mixin(imageUrlMixin);                // 全局混入
Vue.prototype.$util = util;              // 原型链扩展
Vue.prototype.$Cache = Cache;            // 原型链扩展
Vue.prototype.$eventHub = new Vue();     // 事件总线
Vue.prototype.$wechat = Auth;            // 原型链扩展
Vue.directive('debounce', {...});       // 全局指令
Vue.config.productionTip = false;       // 全局配置
```

### 2. 组件内Vue2特性使用情况

#### 高频使用的Vue2特性
1. **$set方法** - 发现20+处使用
   ```javascript
   // 在goods_search_con/index.vue中
   this.$set(this.where, 'page', 1)
   this.$set(this.where, 'keyword', e.detail.value)
   this.$set(this, 'productList', [])
   ```

2. **事件总线模式** - 发现多处使用
   ```javascript
   // main.js中
   Vue.prototype.$eventHub = new Vue();
   
   // 组件中使用
   uni.$emit('scroll');
   uni.$emit('update', res.data);
   ```

3. **mixins使用** - 发现多个混入
   ```javascript
   // 组件中
   mixins: [sendVerifyCode]
   
   // 全局混入
   Vue.mixin(imageUrlMixin)
   ```

4. **$emit事件传递** - 大量使用
   ```javascript
   this.$emit('myevent');
   this.$emit('imgtap', data.img);
   this.$emit('linkpress', {...});
   ```

5. **computed和watch** - 标准Vue2写法
   ```javascript
   computed: {
       ...configMap(['login_logo','wechat_phone_switch'], mapGetters(['viewColor'])),
       copyData() {
           return this.copyright.status == -1 ? this.copyright.year : this.copyright.Copyright
       }
   },
   watch: {
       formItem: function(nval, oVal) {
           // 监听逻辑
       }
   }
   ```

### 3. Vuex使用情况

#### 当前Vuex结构 (Vue2风格)
```javascript
// store/index.js
import Vue from "vue";
import Vuex from "vuex";
Vue.use(Vuex);

export default new Vuex.Store({
  modules,
  getters,
  strict: debug
});
```

#### 组件中Vuex使用
```javascript
// 使用mapGetters
import { mapGetters } from "vuex"
computed: {
    ...mapGetters(['isLogin']),
    ...configMap({...}, mapGetters(['viewColor']))
}

// 直接调用store
this.$store.commit("LOGIN", {...});
this.$store.commit("SETUID", data.user.uid);
this.$store.state.app.token
```

### 4. 生命周期钩子使用

#### 发现的生命周期钩子
```javascript
// 标准Vue2生命周期
mounted() { ... }
onLoad() { ... }      // uni-app特有
onReady() { ... }     // uni-app特有
onShow() { ... }      // uni-app特有
onHide() { ... }      // uni-app特有
beforeMount() { ... }
```

### 5. 模板语法使用情况

#### 发现的Vue2模板特性
```html
<!-- 标准v-model -->
<input v-model="account" />

<!-- 事件处理 -->
<view @click="submit">登录</view>

<!-- 条件渲染 -->
<div v-if="!auth_token">
<div v-else>

<!-- 列表渲染 -->
<view v-for="(item, index) in list" :key="index">

<!-- 动态绑定 -->
<view :style="viewColor">
<view :class="disabled === true ? 'on' : ''">

<!-- 插槽使用 -->
<slot v-if="!nodes.length" />
```

## 🔄 Vue3兼容性问题分析

### 1. 破坏性变更 (Breaking Changes)

#### 🔴 高影响 - 必须修改
1. **全局API变更**
   - `Vue.use()` → `app.use()`
   - `Vue.component()` → `app.component()`
   - `Vue.mixin()` → `app.mixin()`
   - `Vue.directive()` → `app.directive()`
   - `Vue.prototype` → `app.config.globalProperties`

2. **事件总线移除**
   - `new Vue()` 作为事件总线不再支持
   - 需要使用第三方库或Composition API

3. **$set/$delete移除**
   - Vue3响应式系统基于Proxy，不再需要$set/$delete
   - 直接赋值即可触发响应式更新

#### 🟡 中影响 - 需要调整
1. **Vuex版本升级**
   - Vuex 3.x → Vuex 4.x
   - 创建store的方式变更

2. **生命周期钩子名称变更**
   - `beforeDestroy` → `beforeUnmount`
   - `destroyed` → `unmounted`

3. **v-model变更**
   - 自定义组件v-model的prop和事件名变更
   - `.sync`修饰符移除，统一使用v-model

#### 🟢 低影响 - 可选优化
1. **Composition API**
   - 可以逐步引入，与Options API共存
   - 推荐用于复杂逻辑组件

2. **Fragment支持**
   - 组件可以有多个根节点
   - 模板结构更灵活

### 2. uni-app 2.x → 3.x 变更

#### 🔴 关键变更
1. **编译器升级**
   - 基于Vite构建
   - 更快的热重载和构建速度

2. **TypeScript支持增强**
   - 更好的类型推断
   - 内置TypeScript支持

3. **组合式API支持**
   - 完整支持Vue3 Composition API
   - 更好的逻辑复用

#### 🟡 配置变更
1. **manifest.json配置调整**
   - 部分配置项名称变更
   - 新增配置选项

2. **pages.json增强**
   - 支持更多配置选项
   - 更灵活的页面配置

## 📋 迁移优先级矩阵

| 特性类型 | 影响范围 | 迁移难度 | 优先级 | 预计工时 |
|---------|---------|---------|--------|----------|
| 全局API | 全项目 | 中 | P0 | 8小时 |
| $set/$delete | 20+文件 | 低 | P0 | 4小时 |
| 事件总线 | 5+文件 | 高 | P1 | 12小时 |
| Vuex升级 | 全项目 | 中 | P1 | 6小时 |
| 生命周期 | 少量文件 | 低 | P2 | 2小时 |
| 模板语法 | 少量文件 | 低 | P3 | 2小时 |

## 🛠️ 具体迁移方案

### 1. 全局API迁移方案

#### main.js重构
```javascript
// ❌ Vue2写法
import Vue from 'vue'
import App from './App'
import store from './store'

Vue.use(Vuex);
Vue.component('skeleton', skeleton);
Vue.prototype.$util = util;

const app = new Vue({
    ...App,
    store
})
app.$mount();

// ✅ Vue3写法
import { createApp } from 'vue'
import App from './App.vue'
import { createStore } from 'vuex'
import store from './store'

const app = createApp(App)

app.use(store)
app.component('skeleton', skeleton)
app.config.globalProperties.$util = util

app.mount('#app')
```

### 2. $set/$delete迁移方案

```javascript
// ❌ Vue2写法
this.$set(this.where, 'page', 1)
this.$set(this, 'productList', [])
this.$delete(this.obj, 'key')

// ✅ Vue3写法
this.where.page = 1
this.productList = []
delete this.obj.key
```

### 3. 事件总线迁移方案

#### 方案A: 使用mitt库
```javascript
// 安装: npm install mitt

// utils/eventBus.js
import mitt from 'mitt'
export const eventBus = mitt()

// 使用
import { eventBus } from '@/utils/eventBus'

// 发送事件
eventBus.emit('scroll', data)

// 监听事件
eventBus.on('scroll', handler)

// 移除监听
eventBus.off('scroll', handler)
```

#### 方案B: 使用Composition API
```javascript
// composables/useEventBus.js
import { ref, reactive } from 'vue'

const events = reactive({})

export function useEventBus() {
  const emit = (event, data) => {
    if (events[event]) {
      events[event].forEach(callback => callback(data))
    }
  }
  
  const on = (event, callback) => {
    if (!events[event]) {
      events[event] = []
    }
    events[event].push(callback)
  }
  
  const off = (event, callback) => {
    if (events[event]) {
      const index = events[event].indexOf(callback)
      if (index > -1) {
        events[event].splice(index, 1)
      }
    }
  }
  
  return { emit, on, off }
}
```

### 4. Vuex 4.x迁移方案

```javascript
// ❌ Vuex 3.x写法
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  modules,
  getters,
  strict: debug
})

// ✅ Vuex 4.x写法
import { createStore } from 'vuex'

export default createStore({
  modules,
  getters,
  strict: debug
})
```

## 🧪 渐进式迁移策略

### 阶段1: 基础兼容 (1-2周)
1. 升级依赖版本
2. 修复全局API使用
3. 移除$set/$delete调用
4. 基础功能测试

### 阶段2: 核心功能迁移 (2-3周)
1. 重构事件总线
2. 升级Vuex到4.x
3. 修复生命周期钩子
4. 全面功能测试

### 阶段3: 优化增强 (1-2周)
1. 引入Composition API
2. TypeScript支持
3. 性能优化
4. 代码规范统一

## ⚠️ 风险评估

### 高风险项
1. **事件总线重构** - 可能影响组件间通信
2. **第三方插件兼容性** - 部分插件可能不支持Vue3
3. **uni-app插件市场** - 插件兼容性需要验证

### 风险缓解措施
1. **分支开发** - 在独立分支进行升级
2. **功能对比测试** - 确保功能一致性
3. **回滚方案** - 准备快速回滚机制
4. **分阶段发布** - 逐步验证和发布
