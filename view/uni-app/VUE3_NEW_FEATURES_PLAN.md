# Vue3 & uni-app3 新特性应用方案

## 🚀 Vue3新特性应用规划

### 1. Composition API 应用策略

#### 1.1 适用场景识别
基于当前项目分析，以下组件适合使用Composition API重构：

**高优先级重构目标**:
- `pages/users/login/index.vue` - 复杂登录逻辑
- `pages/columnGoods/goods_search_con/index.vue` - 商品搜索逻辑
- `App.vue` - 全局状态管理
- `components/easy-loadimage/easy-loadimage.vue` - 图片懒加载逻辑

#### 1.2 Composition API重构示例

##### 登录页面重构方案
```javascript
// ❌ Vue2 Options API写法
export default {
  data() {
    return {
      account: "",
      password: "",
      captcha: "",
      formItem: 1,
      current: 1,
      isAgree: false
    }
  },
  computed: {
    ...configMap(['login_logo','wechat_phone_switch'], mapGetters(['viewColor'])),
    copyData() {
      return this.copyright.status == -1 ? this.copyright.year : this.copyright.Copyright
    }
  },
  methods: {
    submit() { /* 登录逻辑 */ },
    register() { /* 注册逻辑 */ },
    handleVerify() { /* 验证码逻辑 */ }
  }
}

// ✅ Vue3 Composition API写法
import { ref, computed, reactive } from 'vue'
import { useStore } from 'vuex'
import { useLogin } from '@/composables/useLogin'
import { useVerifyCode } from '@/composables/useVerifyCode'

export default {
  setup() {
    const store = useStore()
    
    // 响应式数据
    const formData = reactive({
      account: "",
      password: "",
      captcha: "",
      formItem: 1,
      current: 1,
      isAgree: false
    })
    
    // 计算属性
    const viewColor = computed(() => store.getters.viewColor)
    const copyData = computed(() => {
      return copyright.value.status == -1 ? copyright.value.year : copyright.value.Copyright
    })
    
    // 组合式函数
    const { login, register, loading } = useLogin()
    const { sendCode, countdown, disabled } = useVerifyCode()
    
    // 方法
    const submit = async () => {
      if (!formData.isAgree) {
        return uni.showToast({ title: '请勾选用户协议', icon: 'none' })
      }
      await login(formData.account, formData.password)
    }
    
    return {
      formData,
      viewColor,
      copyData,
      submit,
      register,
      sendCode,
      countdown,
      disabled,
      loading
    }
  }
}
```

#### 1.3 自定义组合式函数设计

##### useLogin - 登录逻辑复用
```javascript
// composables/useLogin.js
import { ref } from 'vue'
import { useStore } from 'vuex'
import { loginH5, loginMobile } from '@/api/user'

export function useLogin() {
  const store = useStore()
  const loading = ref(false)
  
  const login = async (account, password) => {
    loading.value = true
    try {
      const { data } = await loginH5({ account, password })
      store.commit('LOGIN', {
        token: data.token,
        time: data.exp
      })
      store.commit('SETUID', data.user.uid)
      store.commit('UPDATE_USERINFO', data.user)
      
      // 跳转逻辑
      const backUrl = uni.getStorageSync('login_back_url') || '/pages/index/index'
      uni.switchTab({ url: backUrl })
      
      return data
    } catch (error) {
      uni.showToast({ title: error.message, icon: 'none' })
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const mobileLogin = async (phone, code) => {
    loading.value = true
    try {
      const { data } = await loginMobile({ phone, code })
      // 处理登录成功逻辑
      return data
    } catch (error) {
      uni.showToast({ title: error.message, icon: 'none' })
      throw error
    } finally {
      loading.value = false
    }
  }
  
  return {
    login,
    mobileLogin,
    loading
  }
}
```

##### useVerifyCode - 验证码逻辑复用
```javascript
// composables/useVerifyCode.js
import { ref, computed } from 'vue'
import { getCodeApi } from '@/api/user'

export function useVerifyCode() {
  const countdown = ref(0)
  const loading = ref(false)
  
  const disabled = computed(() => countdown.value > 0 || loading.value)
  const text = computed(() => {
    return countdown.value > 0 ? `${countdown.value}s后重新发送` : '获取验证码'
  })
  
  const sendCode = async (phone) => {
    if (!phone) {
      uni.showToast({ title: '请输入手机号', icon: 'none' })
      return
    }
    
    loading.value = true
    try {
      await getCodeApi({ phone })
      countdown.value = 60
      startCountdown()
      uni.showToast({ title: '验证码已发送', icon: 'success' })
    } catch (error) {
      uni.showToast({ title: error.message, icon: 'none' })
    } finally {
      loading.value = false
    }
  }
  
  const startCountdown = () => {
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  }
  
  return {
    sendCode,
    countdown,
    disabled,
    text,
    loading
  }
}
```

### 2. 响应式系统升级

#### 2.1 Proxy响应式优势应用

##### 深层响应式对象处理
```javascript
// ❌ Vue2需要$set处理
this.$set(this.where, 'page', 1)
this.$set(this.where, 'keyword', value)
this.$set(this, 'productList', [])

// ✅ Vue3直接赋值
this.where.page = 1
this.where.keyword = value
this.productList = []

// ✅ 或使用reactive
const searchParams = reactive({
  page: 1,
  keyword: '',
  order: 'sales'
})

// 直接修改即可触发响应式
searchParams.page = 2
searchParams.keyword = 'iPhone'
```

#### 2.2 性能优化应用

##### 使用shallowRef优化大数据
```javascript
// 对于大型数据列表，使用shallowRef优化性能
import { shallowRef, triggerRef } from 'vue'

export function useProductList() {
  const productList = shallowRef([])
  
  const updateProductList = (newList) => {
    productList.value = newList
    triggerRef(productList) // 手动触发更新
  }
  
  const addProduct = (product) => {
    productList.value.push(product)
    triggerRef(productList)
  }
  
  return {
    productList,
    updateProductList,
    addProduct
  }
}
```

### 3. Fragment和Teleport应用

#### 3.1 Fragment - 多根节点组件
```vue
<!-- ❌ Vue2必须有单一根节点 -->
<template>
  <div class="wrapper">
    <header>头部</header>
    <main>内容</main>
    <footer>底部</footer>
  </div>
</template>

<!-- ✅ Vue3支持多根节点 -->
<template>
  <header>头部</header>
  <main>内容</main>
  <footer>底部</footer>
</template>
```

#### 3.2 Teleport - 弹窗组件优化
```vue
<!-- components/Modal/index.vue -->
<template>
  <teleport to="body">
    <div v-if="visible" class="modal-overlay" @click="close">
      <div class="modal-content" @click.stop>
        <slot />
      </div>
    </div>
  </teleport>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  props: {
    visible: Boolean
  },
  emits: ['close'],
  setup(props, { emit }) {
    const close = () => emit('close')
    return { close }
  }
})
</script>
```

### 4. TypeScript集成方案

#### 4.1 渐进式TypeScript引入

##### 类型定义文件
```typescript
// types/api.ts
export interface LoginParams {
  account: string
  password: string
}

export interface LoginResponse {
  token: string
  exp: number
  user: UserInfo
}

export interface UserInfo {
  uid: number
  nickname: string
  avatar: string
  phone: string
}

// types/store.ts
export interface AppState {
  token: string | null
  userInfo: UserInfo | null
  globalData: GlobalData
}

export interface GlobalData {
  site_name: string
  site_logo: string
  balance_func_status: number
  // ... 其他配置
}
```

##### 组合式函数类型化
```typescript
// composables/useLogin.ts
import type { LoginParams, LoginResponse } from '@/types/api'

export function useLogin() {
  const loading = ref<boolean>(false)
  
  const login = async (params: LoginParams): Promise<LoginResponse> => {
    loading.value = true
    try {
      const response = await loginH5(params)
      return response.data
    } finally {
      loading.value = false
    }
  }
  
  return {
    login,
    loading: readonly(loading)
  }
}
```

### 5. 性能优化新特性

#### 5.1 Suspense异步组件
```vue
<!-- pages/goods/detail.vue -->
<template>
  <Suspense>
    <template #default>
      <AsyncGoodsDetail :id="goodsId" />
    </template>
    <template #fallback>
      <skeleton />
    </template>
  </Suspense>
</template>

<script>
import { defineAsyncComponent } from 'vue'

export default {
  components: {
    AsyncGoodsDetail: defineAsyncComponent(() => 
      import('@/components/GoodsDetail/index.vue')
    )
  }
}
</script>
```

#### 5.2 watchEffect优化
```javascript
// 替换复杂的watch逻辑
import { watchEffect } from 'vue'

export function useProductSearch() {
  const searchParams = reactive({
    keyword: '',
    page: 1,
    order: 'sales'
  })
  
  const productList = ref([])
  
  // 自动追踪依赖，当searchParams变化时自动执行
  watchEffect(async () => {
    if (searchParams.keyword) {
      const { data } = await searchProducts(searchParams)
      productList.value = data.list
    }
  })
  
  return {
    searchParams,
    productList
  }
}
```

## 🎯 uni-app3新特性应用

### 1. Vite构建优化

#### 1.1 vite.config.js配置
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [uni()],
  optimizeDeps: {
    include: ['dayjs', 'lodash-es']
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['vue', 'vuex'],
          'utils': ['dayjs', 'lodash-es']
        }
      }
    }
  }
})
```

### 2. 组合式API在uni-app中的应用

#### 2.1 页面生命周期组合
```javascript
// composables/usePageLifecycle.js
import { onLoad, onShow, onHide, onUnload } from '@dcloudio/uni-app'

export function usePageLifecycle() {
  onLoad((options) => {
    console.log('页面加载', options)
  })
  
  onShow(() => {
    console.log('页面显示')
  })
  
  onHide(() => {
    console.log('页面隐藏')
  })
  
  onUnload(() => {
    console.log('页面卸载')
  })
}
```

#### 2.2 uni-app API组合化
```javascript
// composables/useUniAPI.js
import { ref } from 'vue'

export function useRequest() {
  const loading = ref(false)
  
  const request = async (options) => {
    loading.value = true
    try {
      const result = await new Promise((resolve, reject) => {
        uni.request({
          ...options,
          success: resolve,
          fail: reject
        })
      })
      return result.data
    } finally {
      loading.value = false
    }
  }
  
  return { request, loading }
}

export function useToast() {
  const showToast = (title, icon = 'none') => {
    uni.showToast({ title, icon })
  }
  
  const showLoading = (title = '加载中...') => {
    uni.showLoading({ title })
  }
  
  const hideLoading = () => {
    uni.hideLoading()
  }
  
  return {
    showToast,
    showLoading,
    hideLoading
  }
}
```

### 3. 新特性应用时间表

#### 第一阶段 (升级后1-2周)
- [ ] 基础Composition API重构
- [ ] 移除$set/$delete使用
- [ ] Fragment多根节点应用
- [ ] 基础TypeScript类型定义

#### 第二阶段 (2-4周)
- [ ] 核心组合式函数开发
- [ ] Suspense异步组件应用
- [ ] Teleport弹窗优化
- [ ] 性能监控和优化

#### 第三阶段 (4-6周)
- [ ] 全面TypeScript化
- [ ] 高级性能优化
- [ ] 新特性最佳实践总结
- [ ] 团队培训和文档

## 📈 预期收益

### 性能提升
- **构建速度**: Vite构建比webpack快3-5倍
- **热重载**: 毫秒级热更新
- **包体积**: Tree-shaking优化，减少20-30%

### 开发体验
- **类型安全**: TypeScript提供完整类型检查
- **逻辑复用**: Composition API提高代码复用性
- **调试体验**: Vue DevTools 6.x更强大的调试功能

### 维护性
- **代码组织**: 更清晰的逻辑分离
- **测试友好**: 组合式函数更易于单元测试
- **团队协作**: 统一的代码风格和最佳实践
