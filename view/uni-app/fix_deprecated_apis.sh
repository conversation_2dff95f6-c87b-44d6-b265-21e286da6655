#!/bin/bash

# UNIAPP微信小程序已弃用API修复脚本
# 作者: AI Assistant
# 日期: $(date +%Y-%m-%d)
# 版本: 1.0

echo "=========================================="
echo "UNIAPP微信小程序已弃用API修复脚本"
echo "=========================================="

# 创建备份目录
BACKUP_DIR="./api_fix_backup_$(date +%Y%m%d_%H%M%S)"
echo "创建备份目录: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 备份需要修改的文件
echo "备份原始文件..."
find . -name "*.vue" -o -name "*.js" | grep -E "(pages|components|utils)" | while read file; do
    if grep -q "wx\." "$file" 2>/dev/null; then
        cp "$file" "$BACKUP_DIR/"
        echo "备份: $file"
    fi
done

echo "=========================================="
echo "开始修复已弃用API..."
echo "=========================================="

# 统计修复前的wx.API使用情况
echo "修复前wx.API使用统计:"
echo "wx.setClipboardData: $(grep -r "wx\.setClipboardData" . --include="*.vue" --include="*.js" | wc -l)"
echo "wx.getClipboardData: $(grep -r "wx\.getClipboardData" . --include="*.vue" --include="*.js" | wc -l)"
echo "wx.showToast: $(grep -r "wx\.showToast" . --include="*.vue" --include="*.js" | wc -l)"
echo "wx.scanCode: $(grep -r "wx\.scanCode" . --include="*.vue" --include="*.js" | wc -l)"
echo "wx.previewImage: $(grep -r "wx\.previewImage" . --include="*.vue" --include="*.js" | wc -l)"

echo "=========================================="

# 1. 修复剪贴板API
echo "1. 修复剪贴板API..."
find . -name "*.vue" -type f -exec sed -i.bak 's/wx\.setClipboardData(/uni.setClipboardData(/g' {} \;
find . -name "*.vue" -type f -exec sed -i.bak 's/wx\.getClipboardData(/uni.getClipboardData(/g' {} \;
echo "   ✓ 剪贴板API修复完成"

# 2. 修复Toast API
echo "2. 修复Toast API..."
find . -name "*.vue" -type f -exec sed -i.bak 's/wx\.showToast(/uni.showToast(/g' {} \;
echo "   ✓ Toast API修复完成"

# 3. 修复扫码API
echo "3. 修复扫码API..."
find . -name "*.vue" -type f -exec sed -i.bak 's/wx\.scanCode(/uni.scanCode(/g' {} \;
echo "   ✓ 扫码API修复完成"

# 4. 修复图片预览API
echo "4. 修复图片预览API..."
find . -name "*.vue" -type f -exec sed -i.bak 's/wx\.previewImage(/uni.previewImage(/g' {} \;
echo "   ✓ 图片预览API修复完成"

# 5. 处理登录API (需要手动处理，因为需要添加provider参数)
echo "5. 标记需要手动处理的登录API..."
grep -rn "wx\.login(" . --include="*.vue" --include="*.js" > "$BACKUP_DIR/manual_fix_login_apis.txt" 2>/dev/null
if [ -s "$BACKUP_DIR/manual_fix_login_apis.txt" ]; then
    echo "   ⚠️  发现需要手动处理的wx.login()调用:"
    cat "$BACKUP_DIR/manual_fix_login_apis.txt"
    echo "   请手动将wx.login()替换为uni.login({provider: 'weixin', ...})"
else
    echo "   ✓ 未发现需要处理的wx.login()调用"
fi

# 清理临时备份文件
echo "清理临时文件..."
find . -name "*.bak" -delete

echo "=========================================="
echo "修复完成统计:"
echo "=========================================="

# 统计修复后的情况
echo "修复后wx.API使用统计:"
echo "wx.setClipboardData: $(grep -r "wx\.setClipboardData" . --include="*.vue" --include="*.js" | wc -l)"
echo "wx.getClipboardData: $(grep -r "wx\.getClipboardData" . --include="*.vue" --include="*.js" | wc -l)"
echo "wx.showToast: $(grep -r "wx\.showToast" . --include="*.vue" --include="*.js" | wc -l)"
echo "wx.scanCode: $(grep -r "wx\.scanCode" . --include="*.vue" --include="*.js" | wc -l)"
echo "wx.previewImage: $(grep -r "wx\.previewImage" . --include="*.vue" --include="*.js" | wc -l)"

echo ""
echo "修复后uni.API使用统计:"
echo "uni.setClipboardData: $(grep -r "uni\.setClipboardData" . --include="*.vue" --include="*.js" | wc -l)"
echo "uni.getClipboardData: $(grep -r "uni\.getClipboardData" . --include="*.vue" --include="*.js" | wc -l)"
echo "uni.showToast: $(grep -r "uni\.showToast" . --include="*.vue" --include="*.js" | wc -l)"
echo "uni.scanCode: $(grep -r "uni\.scanCode" . --include="*.vue" --include="*.js" | wc -l)"
echo "uni.previewImage: $(grep -r "uni\.previewImage" . --include="*.vue" --include="*.js" | wc -l)"

echo "=========================================="
echo "剩余需要注意的wx.API调用:"
echo "=========================================="

# 列出剩余的wx.API调用
echo "以下wx.API调用可能需要保留或特殊处理:"
grep -rn "wx\." . --include="*.vue" --include="*.js" | grep -v "node_modules" | head -20

echo ""
echo "=========================================="
echo "修复完成!"
echo "=========================================="
echo "备份目录: $BACKUP_DIR"
echo ""
echo "下一步操作:"
echo "1. 检查并手动修复wx.login()调用"
echo "2. 测试修复后的功能"
echo "3. 如有问题，可从备份目录恢复文件"
echo ""
echo "建议测试的功能:"
echo "- 复制订单号功能"
echo "- 扫码功能"
echo "- 图片预览功能"
echo "- Toast提示功能"
echo ""
