#!/bin/bash

# Vue2 到 Vue3 自动化迁移脚本
# 版本: 1.0
# 作者: AI Assistant
# 日期: $(date +%Y-%m-%d)

echo "=========================================="
echo "Vue2 到 Vue3 自动化迁移脚本"
echo "=========================================="

# 创建备份目录
BACKUP_DIR="./vue3-migration-backup-$(date +%Y%m%d_%H%M%S)"
echo "创建备份目录: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 备份关键文件
echo "备份关键文件..."
cp -r pages "$BACKUP_DIR/"
cp -r components "$BACKUP_DIR/"
cp -r store "$BACKUP_DIR/"
cp main.js "$BACKUP_DIR/"
cp package.json "$BACKUP_DIR/"
echo "✓ 文件备份完成"

echo "=========================================="
echo "开始自动化迁移..."
echo "=========================================="

# 统计迁移前的情况
echo "迁移前统计:"
echo "Vue2特性使用情况:"
echo "\$set使用次数: $(grep -r "this\.\$set" . --include="*.vue" --include="*.js" | wc -l)"
echo "\$delete使用次数: $(grep -r "this\.\$delete" . --include="*.vue" --include="*.js" | wc -l)"
echo "beforeDestroy使用次数: $(grep -r "beforeDestroy" . --include="*.vue" --include="*.js" | wc -l)"
echo "destroyed使用次数: $(grep -r "destroyed" . --include="*.vue" --include="*.js" | wc -l)"
echo "Vue.prototype使用次数: $(grep -r "Vue\.prototype" . --include="*.js" | wc -l)"

echo "=========================================="

# 1. 替换$set使用
echo "1. 替换\$set调用..."
find . -name "*.vue" -type f -exec sed -i.bak 's/this\.\$set(\([^,]*\),\s*\([^,]*\),\s*\([^)]*\))/\1[\2] = \3/g' {} \;
find . -name "*.js" -type f -exec sed -i.bak 's/this\.\$set(\([^,]*\),\s*\([^,]*\),\s*\([^)]*\))/\1[\2] = \3/g' {} \;
echo "   ✓ \$set替换完成"

# 2. 替换$delete使用
echo "2. 替换\$delete调用..."
find . -name "*.vue" -type f -exec sed -i.bak 's/this\.\$delete(\([^,]*\),\s*\([^)]*\))/delete \1[\2]/g' {} \;
find . -name "*.js" -type f -exec sed -i.bak 's/this\.\$delete(\([^,]*\),\s*\([^)]*\))/delete \1[\2]/g' {} \;
echo "   ✓ \$delete替换完成"

# 3. 替换生命周期钩子
echo "3. 替换生命周期钩子..."
find . -name "*.vue" -type f -exec sed -i.bak 's/beforeDestroy/beforeUnmount/g' {} \;
find . -name "*.vue" -type f -exec sed -i.bak 's/destroyed/unmounted/g' {} \;
find . -name "*.js" -type f -exec sed -i.bak 's/beforeDestroy/beforeUnmount/g' {} \;
find . -name "*.js" -type f -exec sed -i.bak 's/destroyed/unmounted/g' {} \;
echo "   ✓ 生命周期钩子替换完成"

# 4. 处理事件总线使用 (标记需要手动处理的地方)
echo "4. 标记事件总线使用..."
grep -rn "\$eventHub\|\$emit\|\$on\|\$off" . --include="*.vue" --include="*.js" > "$BACKUP_DIR/event_bus_usage.txt" 2>/dev/null
if [ -s "$BACKUP_DIR/event_bus_usage.txt" ]; then
    echo "   ⚠️  发现事件总线使用，需要手动处理:"
    head -10 "$BACKUP_DIR/event_bus_usage.txt"
    echo "   完整列表保存在: $BACKUP_DIR/event_bus_usage.txt"
else
    echo "   ✓ 未发现事件总线使用"
fi

# 5. 标记需要手动处理的全局API
echo "5. 标记全局API使用..."
grep -rn "Vue\.use\|Vue\.component\|Vue\.prototype\|Vue\.mixin\|Vue\.directive" . --include="*.js" > "$BACKUP_DIR/global_api_usage.txt" 2>/dev/null
if [ -s "$BACKUP_DIR/global_api_usage.txt" ]; then
    echo "   ⚠️  发现全局API使用，需要手动处理:"
    cat "$BACKUP_DIR/global_api_usage.txt"
else
    echo "   ✓ 未发现全局API使用"
fi

# 6. 检查.sync修饰符使用
echo "6. 检查.sync修饰符使用..."
grep -rn "\.sync" . --include="*.vue" > "$BACKUP_DIR/sync_modifier_usage.txt" 2>/dev/null
if [ -s "$BACKUP_DIR/sync_modifier_usage.txt" ]; then
    echo "   ⚠️  发现.sync修饰符使用，需要手动处理:"
    cat "$BACKUP_DIR/sync_modifier_usage.txt"
else
    echo "   ✓ 未发现.sync修饰符使用"
fi

# 清理临时备份文件
echo "7. 清理临时文件..."
find . -name "*.bak" -delete
echo "   ✓ 临时文件清理完成"

echo "=========================================="
echo "迁移后统计:"
echo "=========================================="

# 统计迁移后的情况
echo "\$set使用次数: $(grep -r "this\.\$set" . --include="*.vue" --include="*.js" | wc -l)"
echo "\$delete使用次数: $(grep -r "this\.\$delete" . --include="*.vue" --include="*.js" | wc -l)"
echo "beforeDestroy使用次数: $(grep -r "beforeDestroy" . --include="*.vue" --include="*.js" | wc -l)"
echo "destroyed使用次数: $(grep -r "destroyed" . --include="*.vue" --include="*.js" | wc -l)"
echo "beforeUnmount使用次数: $(grep -r "beforeUnmount" . --include="*.vue" --include="*.js" | wc -l)"
echo "unmounted使用次数: $(grep -r "unmounted" . --include="*.vue" --include="*.js" | wc -l)"

echo "=========================================="
echo "自动化迁移完成!"
echo "=========================================="
echo "备份目录: $BACKUP_DIR"
echo ""
echo "⚠️  需要手动处理的项目:"
echo "1. main.js - 全局API迁移"
echo "2. store/index.js - Vuex 4.x升级"
echo "3. 事件总线重构 (如果有使用)"
echo "4. .sync修饰符替换为v-model"
echo "5. 第三方插件兼容性检查"
echo ""
echo "📋 下一步操作:"
echo "1. 检查控制台输出的警告信息"
echo "2. 手动处理标记的问题"
echo "3. 运行项目测试功能"
echo "4. 如有问题，可从备份目录恢复"
echo ""
echo "🧪 建议测试的功能:"
echo "- 页面路由跳转"
echo "- 数据绑定和更新"
echo "- 组件通信"
echo "- 生命周期钩子"
echo "- 状态管理"
echo ""
