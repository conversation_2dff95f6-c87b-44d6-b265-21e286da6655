{"name": "crmeb-multi-shop-uniapp", "version": "1.0.4", "description": "CRMEB多商户系统 uni-app版本", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-lark": "cross-env NODE_ENV=production UNI_PLATFORM=mp-lark vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-lark": "cross-env NODE_ENV=development UNI_PLATFORM=mp-lark vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest", "test:h5": "cross-env UNI_PLATFORM=h5 jest", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-3081220230802001", "@dcloudio/uni-app-plus": "^3.0.0-3081220230802001", "@dcloudio/uni-h5": "^3.0.0-3081220230802001", "@dcloudio/uni-mp-alipay": "^3.0.0-3081220230802001", "@dcloudio/uni-mp-baidu": "^3.0.0-3081220230802001", "@dcloudio/uni-mp-kuaishou": "^3.0.0-3081220230802001", "@dcloudio/uni-mp-lark": "^3.0.0-3081220230802001", "@dcloudio/uni-mp-qq": "^3.0.0-3081220230802001", "@dcloudio/uni-mp-toutiao": "^3.0.0-3081220230802001", "@dcloudio/uni-mp-weixin": "^3.0.0-3081220230802001", "@dcloudio/uni-mp-360": "^3.0.0-3081220230802001", "@dcloudio/uni-quickapp-native": "^3.0.0-3081220230802001", "@dcloudio/uni-quickapp-webview": "^3.0.0-3081220230802001", "@dcloudio/uni-stat": "^2.0.2-4050620250311002", "vue": "^3.3.4", "vuex": "^4.1.0", "pinia": "^2.1.6", "dayjs": "^1.11.9", "mitt": "^3.0.1", "lodash-es": "^4.17.21"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^3.0.0-3081220230802001", "@dcloudio/uni-cli-shared": "^3.0.0-3081220230802001", "@dcloudio/uni-stacktracey": "^3.0.0-3081220230802001", "@dcloudio/vue-cli-plugin-uni": "^3.0.0-3081220230802001", "@dcloudio/webpack-uni-mp-loader": "^3.0.0-3081220230802001", "@dcloudio/webpack-uni-pages-loader": "^3.0.0-3081220230802001", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/compiler-sfc": "^3.3.4", "babel-plugin-import": "^1.13.8", "cross-env": "^7.0.3", "jest": "^29.6.2", "mini-types": "*", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0", "typescript": "^5.1.6", "@types/node": "^20.4.5", "vue-tsc": "^1.8.8", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "prettier": "^3.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/crmeb/CRMEB_Mer_v3.1.git"}, "keywords": ["uni-app", "vue3", "typescript", "crmeb", "multi-shop", "e-commerce"], "author": "CRMEB Team", "license": "MIT"}