// Vue3类型声明文件

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 全局属性类型声明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    // 工具函数
    $util: typeof import('@/utils/util').default
    $Cache: typeof import('@/utils/cache').default
    $wechat: typeof import('@/utils/wechat').default
    
    // API相关
    $api: typeof import('@/api/index').default
    $http: typeof import('@/utils/request').default
    
    // 常用方法
    $toast: (message: string, type?: 'success' | 'error' | 'warning' | 'info') => void
    $loading: {
      show: (message?: string) => void
      hide: () => void
    }
    
    // 路由相关
    $Router: {
      push: (options: UniApp.NavigateToOptions) => void
      replace: (options: UniApp.RedirectToOptions) => void
      back: (delta?: number) => void
      switchTab: (options: UniApp.SwitchTabOptions) => void
      reLaunch: (options: UniApp.ReLaunchOptions) => void
    }
  }
  
  interface ComponentCustomOptions {
    // 页面生命周期
    onLoad?: (options: Record<string, string | undefined>) => void
    onShow?: () => void
    onReady?: () => void
    onHide?: () => void
    onUnload?: () => void
    onResize?: (options: { size: { windowWidth: number; windowHeight: number } }) => void
    onPullDownRefresh?: () => void
    onReachBottom?: () => void
    onTabItemTap?: (options: { index: string; pagePath: string; text: string }) => void
    onShareAppMessage?: (options: { from: string; target?: any; webViewUrl?: string }) => UniApp.ShareAppMessageRes
    onShareTimeline?: () => UniApp.ShareTimelineRes
    onAddToFavorites?: (options: { webviewUrl?: string }) => UniApp.AddToFavoritesRes
    onPageScroll?: (options: { scrollTop: number }) => void
    onNavigationBarButtonTap?: (options: { index: number }) => void
    onBackPress?: (options: { from: 'navigateBack' | 'backbutton' }) => boolean | void
    onNavigationBarSearchInputChanged?: (options: { text: string }) => void
    onNavigationBarSearchInputConfirmed?: (options: { text: string }) => void
    onNavigationBarSearchInputClicked?: () => void
    onNavigationBarSearchInputFocusChanged?: (options: { focus: boolean }) => void
  }
}

// 环境变量类型声明
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_BASE_URL: string
  readonly VITE_APP_API_URL: string
  readonly VITE_APP_UPLOAD_URL: string
  readonly VITE_APP_WS_URL: string
  readonly VITE_APP_ENV: 'development' | 'production' | 'test'
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// uni-app全局类型扩展
declare global {
  // 页面实例类型
  interface Page {
    route?: string
    options?: Record<string, any>
  }
  
  // 应用实例类型
  interface App {
    globalData: {
      userInfo?: any
      token?: string
      [key: string]: any
    }
  }
  
  // 微信小程序特有类型
  interface WechatMiniprogram {
    getAccountInfoSync(): {
      miniProgram: {
        appId: string
        envVersion: 'develop' | 'trial' | 'release'
        version: string
      }
      plugin: {
        appId: string
        version: string
      }
    }
  }
  
  // 支付宝小程序特有类型
  interface AlipayMiniprogram {
    getSystemInfoSync(): any
  }
}

// 工具类型
export type Recordable<T = any> = Record<string, T>
export type Nullable<T> = T | null
export type NonNullable<T> = T extends null | undefined ? never : T
export type Arrayable<T> = T | T[]
export type Awaitable<T> = T | Promise<T>

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// 分页数据类型
export interface PageData<T = any> {
  list: T[]
  total: number
  page: number
  limit: number
  hasMore?: boolean
}

// 用户信息类型
export interface UserInfo {
  uid: number
  nickname: string
  avatar: string
  phone: string
  email?: string
  gender: 0 | 1 | 2 // 0:未知 1:男 2:女
  birthday?: string
  province?: string
  city?: string
  district?: string
  address?: string
  integral: number
  level: number
  experience: number
  status: 0 | 1 // 0:禁用 1:正常
  createTime: string
  updateTime: string
}

// 商品信息类型
export interface GoodsInfo {
  id: number
  title: string
  image: string
  images: string[]
  price: number
  originalPrice: number
  stock: number
  sales: number
  description: string
  content: string
  status: 0 | 1
  sort: number
  categoryId: number
  brandId?: number
  tags?: string[]
  attributes?: Record<string, any>
  createTime: string
  updateTime: string
}

// 订单信息类型
export interface OrderInfo {
  id: number
  orderNo: string
  userId: number
  totalAmount: number
  payAmount: number
  discountAmount: number
  status: number
  payStatus: number
  deliveryStatus: number
  payTime?: string
  deliveryTime?: string
  finishTime?: string
  items: OrderItem[]
  address: OrderAddress
  createTime: string
  updateTime: string
}

export interface OrderItem {
  id: number
  goodsId: number
  goodsTitle: string
  goodsImage: string
  price: number
  quantity: number
  totalAmount: number
  specifications?: Record<string, any>
}

export interface OrderAddress {
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  zipCode?: string
}

// 购物车类型
export interface CartItem {
  id: number
  goodsId: number
  goodsTitle: string
  goodsImage: string
  price: number
  quantity: number
  selected: boolean
  specifications?: Record<string, any>
  stock: number
}

// 地址信息类型
export interface AddressInfo {
  id: number
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  zipCode?: string
  isDefault: boolean
  createTime: string
  updateTime: string
}

export {}
