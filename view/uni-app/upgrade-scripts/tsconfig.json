{
  "compilerOptions": {
    // 基础配置
    "target": "ES2020",
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "allowJs": true,
    "checkJs": false,
    
    // 严格模式配置
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitOverride": true,
    "useUnknownInCatchVariables": true,
    
    // 模块配置
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    
    // 输出配置
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true,
    "removeComments": false,
    "noEmit": true,
    "importHelpers": true,
    
    // 高级配置
    "skipLibCheck": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    
    // JSX配置
    "jsx": "preserve",
    "jsxFactory": "h",
    "jsxFragmentFactory": "Fragment",
    
    // 路径映射
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/utils/*": ["src/utils/*"],
      "@/api/*": ["src/api/*"],
      "@/store/*": ["src/store/*"],
      "@/pages/*": ["src/pages/*"],
      "@/static/*": ["src/static/*"],
      "@/types/*": ["src/types/*"],
      "@/composables/*": ["src/composables/*"]
    },
    
    // 类型定义
    "types": [
      "node",
      "@dcloudio/types",
      "miniprogram-api-typings",
      "@types/wechat-miniprogram"
    ],
    
    // 增量编译
    "incremental": true,
    "tsBuildInfoFile": ".tsbuildinfo"
  },
  
  // 包含的文件
  "include": [
    "src/**/*",
    "src/**/*.vue",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.js",
    "src/**/*.jsx",
    "pages.json",
    "manifest.json",
    "App.vue",
    "main.ts",
    "uni.scss",
    "shims-vue.d.ts",
    "shims-uni.d.ts"
  ],
  
  // 排除的文件
  "exclude": [
    "node_modules",
    "dist",
    "build",
    "unpackage",
    "**/*.spec.ts",
    "**/*.test.ts"
  ],
  
  // Vue特定配置
  "vueCompilerOptions": {
    "target": 3,
    "experimentalRuntimeMode": "runtime-uni-app"
  },
  
  // 编译选项
  "compileOnSave": false,
  
  // 引用项目
  "references": []
}
