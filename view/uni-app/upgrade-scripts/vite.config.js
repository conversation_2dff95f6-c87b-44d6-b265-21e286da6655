import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  
  // Vue3特性配置
  define: {
    __VUE_OPTIONS_API__: true,        // 启用Options API兼容模式
    __VUE_PROD_DEVTOOLS__: false,     // 生产环境禁用devtools
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
  },
  
  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/store': resolve(__dirname, 'src/store'),
      '@/pages': resolve(__dirname, 'src/pages'),
      '@/static': resolve(__dirname, 'src/static')
    }
  },
  
  // CSS配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @import "@/styles/variables.scss";
          @import "@/styles/mixins.scss";
        `
      }
    }
  },
  
  // 开发服务器配置
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  
  // 构建配置
  build: {
    // 构建目标
    target: 'es2015',
    
    // 输出目录
    outDir: 'dist',
    
    // 静态资源目录
    assetsDir: 'static',
    
    // 小于此阈值的导入或引用资源将内联为base64编码
    assetsInlineLimit: 4096,
    
    // 启用/禁用CSS代码拆分
    cssCodeSplit: true,
    
    // 构建后是否生成source map文件
    sourcemap: false,
    
    // 自定义底层的Rollup打包配置
    rollupOptions: {
      output: {
        // 分包策略
        manualChunks: {
          // 将Vue相关库打包到vendor chunk
          'vendor': ['vue', 'vuex', 'pinia'],
          
          // 将工具库打包到utils chunk
          'utils': ['dayjs', 'lodash-es', 'mitt'],
          
          // 将UI组件库打包到ui chunk (如果使用)
          // 'ui': ['vant', 'element-plus']
        },
        
        // chunk文件名
        chunkFileNames: 'js/[name]-[hash].js',
        
        // 入口文件名
        entryFileNames: 'js/[name]-[hash].js',
        
        // 静态资源文件名
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          let extType = info[info.length - 1]
          
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'media'
          } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'images'
          } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'fonts'
          }
          
          return `${extType}/[name]-[hash].[ext]`
        }
      }
    },
    
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        // 生产环境移除console
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  
  // 依赖优化配置
  optimizeDeps: {
    include: [
      'vue',
      'vuex',
      'pinia',
      'dayjs',
      'lodash-es',
      'mitt'
    ],
    exclude: [
      // 排除不需要预构建的依赖
    ]
  },
  
  // 环境变量配置
  envPrefix: ['VITE_', 'UNI_'],
  
  // 实验性功能
  experimental: {
    // 启用渲染优化
    renderBuiltUrl: (filename, { hostType }) => {
      if (hostType === 'js') {
        return { js: `/${filename}` }
      } else {
        return { relative: true }
      }
    }
  }
})

// 条件配置 - 根据不同环境应用不同配置
if (process.env.NODE_ENV === 'development') {
  // 开发环境特定配置
  console.log('🚀 开发环境配置已加载')
} else {
  // 生产环境特定配置
  console.log('📦 生产环境配置已加载')
}
